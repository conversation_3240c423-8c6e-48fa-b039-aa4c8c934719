import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  ArrowLeft,
  Plus,
  TrendingUp,
  Calendar,
  Scale,
  Ruler,
  Target,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

interface Assessment {
  id: number
  peso: number
  altura: number
  imc: number
  gordura_percentual?: number
  massa_gorda?: number
  massa_magra?: number
  circunferencia_cintura?: number
  circunferencia_quadril?: number
  observacoes?: string
  data_avaliacao: string
}

interface Patient {
  id: number
  nome: string
  email: string
}

export default function PatientAssessments() {
  const router = useRouter()
  const { id } = router.query
  const [patient, setPatient] = useState<Patient | null>(null)
  const [assessments, setAssessments] = useState<Assessment[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (id) {
      fetchData()
    }
  }, [id])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Mock data - em produção viria da API
      setPatient({
        id: Number(id),
        nome: 'João Silva',
        email: '<EMAIL>'
      })

      setAssessments([
        {
          id: 1,
          peso: 75.5,
          altura: 175,
          imc: 24.0,
          gordura_percentual: 15.2,
          massa_gorda: 11.5,
          massa_magra: 64.0,
          circunferencia_cintura: 82,
          circunferencia_quadril: 95,
          observacoes: 'Primeira avaliação. Paciente em boa forma física.',
          data_avaliacao: '2024-01-20'
        },
        {
          id: 2,
          peso: 74.8,
          altura: 175,
          imc: 23.8,
          gordura_percentual: 14.8,
          massa_gorda: 11.1,
          massa_magra: 63.7,
          circunferencia_cintura: 81,
          circunferencia_quadril: 94,
          observacoes: 'Pequena redução no peso e gordura corporal.',
          data_avaliacao: '2024-01-10'
        },
        {
          id: 3,
          peso: 76.2,
          altura: 175,
          imc: 24.9,
          gordura_percentual: 16.1,
          massa_gorda: 12.3,
          massa_magra: 63.9,
          circunferencia_cintura: 84,
          circunferencia_quadril: 96,
          observacoes: 'Avaliação inicial.',
          data_avaliacao: '2024-01-01'
        }
      ])
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const getIMCCategory = (imc: number) => {
    if (imc < 18.5) return { label: 'Abaixo do peso', color: 'text-blue-600' }
    if (imc < 25) return { label: 'Peso normal', color: 'text-green-600' }
    if (imc < 30) return { label: 'Sobrepeso', color: 'text-yellow-600' }
    return { label: 'Obesidade', color: 'text-red-600' }
  }

  const calculateProgress = (current: number, previous: number) => {
    const diff = current - previous
    const percentage = ((diff / previous) * 100).toFixed(1)
    return {
      value: diff,
      percentage: percentage,
      isPositive: diff > 0
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Avaliações - {patient?.nome} | Hypatium</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link 
                  href={`/personal/patients/${id}`}
                  className="mr-4 p-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    <TrendingUp className="h-6 w-6 text-blue-600 mr-2" />
                    Avaliações - {patient?.nome}
                  </h1>
                  <p className="text-gray-600">Histórico de avaliações físicas</p>
                </div>
              </div>
              <Link
                href={`/personal/assessments/new?patient=${id}`}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Nova Avaliação
              </Link>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {assessments.length === 0 ? (
              <div className="bg-white shadow rounded-lg p-8 text-center">
                <TrendingUp className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma avaliação encontrada</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Comece criando a primeira avaliação física para este paciente.
                </p>
                <div className="mt-6">
                  <Link
                    href={`/personal/assessments/new?patient=${id}`}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Primeira Avaliação
                  </Link>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Progress Summary */}
                {assessments.length > 1 && (
                  <div className="bg-white shadow rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Progresso Recente</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {(() => {
                        const latest = assessments[0]
                        const previous = assessments[1]
                        const weightProgress = calculateProgress(latest.peso, previous.peso)
                        const imcProgress = calculateProgress(latest.imc, previous.imc)
                        const fatProgress = latest.gordura_percentual && previous.gordura_percentual 
                          ? calculateProgress(latest.gordura_percentual, previous.gordura_percentual)
                          : null
                        const muscleProgress = latest.massa_magra && previous.massa_magra
                          ? calculateProgress(latest.massa_magra, previous.massa_magra)
                          : null

                        return (
                          <>
                            <div className="text-center">
                              <p className="text-2xl font-bold text-gray-900">{latest.peso}kg</p>
                              <p className="text-sm text-gray-500">Peso</p>
                              <p className={`text-xs ${weightProgress.isPositive ? 'text-red-600' : 'text-green-600'}`}>
                                {weightProgress.isPositive ? '+' : ''}{weightProgress.value.toFixed(1)}kg
                              </p>
                            </div>
                            <div className="text-center">
                              <p className="text-2xl font-bold text-gray-900">{latest.imc.toFixed(1)}</p>
                              <p className="text-sm text-gray-500">IMC</p>
                              <p className={`text-xs ${imcProgress.isPositive ? 'text-red-600' : 'text-green-600'}`}>
                                {imcProgress.isPositive ? '+' : ''}{imcProgress.value.toFixed(1)}
                              </p>
                            </div>
                            {fatProgress && (
                              <div className="text-center">
                                <p className="text-2xl font-bold text-gray-900">{latest.gordura_percentual?.toFixed(1)}%</p>
                                <p className="text-sm text-gray-500">Gordura</p>
                                <p className={`text-xs ${fatProgress.isPositive ? 'text-red-600' : 'text-green-600'}`}>
                                  {fatProgress.isPositive ? '+' : ''}{fatProgress.value.toFixed(1)}%
                                </p>
                              </div>
                            )}
                            {muscleProgress && (
                              <div className="text-center">
                                <p className="text-2xl font-bold text-gray-900">{latest.massa_magra?.toFixed(1)}kg</p>
                                <p className="text-sm text-gray-500">Massa Magra</p>
                                <p className={`text-xs ${muscleProgress.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                                  {muscleProgress.isPositive ? '+' : ''}{muscleProgress.value.toFixed(1)}kg
                                </p>
                              </div>
                            )}
                          </>
                        )
                      })()}
                    </div>
                  </div>
                )}

                {/* Assessments List */}
                <div className="space-y-4">
                  {assessments.map((assessment, index) => {
                    const imcCategory = getIMCCategory(assessment.imc)
                    return (
                      <div key={assessment.id} className="bg-white shadow rounded-lg p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h4 className="text-lg font-medium text-gray-900">
                              Avaliação #{assessments.length - index}
                            </h4>
                            <div className="flex items-center text-sm text-gray-500 mt-1">
                              <Calendar className="h-4 w-4 mr-1" />
                              {formatDate(assessment.data_avaliacao)}
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Link
                              href={`/personal/assessments/${assessment.id}`}
                              className="text-blue-600 hover:text-blue-900"
                              title="Visualizar"
                            >
                              <Eye className="h-4 w-4" />
                            </Link>
                            <Link
                              href={`/personal/assessments/${assessment.id}/edit`}
                              className="text-green-600 hover:text-green-900"
                              title="Editar"
                            >
                              <Edit className="h-4 w-4" />
                            </Link>
                            <button
                              className="text-red-600 hover:text-red-900"
                              title="Excluir"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-4">
                          <div className="flex items-center">
                            <Scale className="h-4 w-4 text-gray-400 mr-2" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">{assessment.peso}kg</p>
                              <p className="text-xs text-gray-500">Peso</p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <Ruler className="h-4 w-4 text-gray-400 mr-2" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">{assessment.altura}cm</p>
                              <p className="text-xs text-gray-500">Altura</p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <Target className="h-4 w-4 text-gray-400 mr-2" />
                            <div>
                              <p className={`text-sm font-medium ${imcCategory.color}`}>{assessment.imc.toFixed(1)}</p>
                              <p className="text-xs text-gray-500">IMC</p>
                            </div>
                          </div>
                          {assessment.gordura_percentual && (
                            <div>
                              <p className="text-sm font-medium text-gray-900">{assessment.gordura_percentual.toFixed(1)}%</p>
                              <p className="text-xs text-gray-500">Gordura</p>
                            </div>
                          )}
                          {assessment.massa_magra && (
                            <div>
                              <p className="text-sm font-medium text-gray-900">{assessment.massa_magra.toFixed(1)}kg</p>
                              <p className="text-xs text-gray-500">Massa Magra</p>
                            </div>
                          )}
                          {assessment.circunferencia_cintura && (
                            <div>
                              <p className="text-sm font-medium text-gray-900">{assessment.circunferencia_cintura}cm</p>
                              <p className="text-xs text-gray-500">Cintura</p>
                            </div>
                          )}
                        </div>

                        {assessment.observacoes && (
                          <div className="border-t border-gray-200 pt-4">
                            <p className="text-sm text-gray-600">{assessment.observacoes}</p>
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}
