"""
Serviço para cálculos automáticos de composição corporal e metabolismo
"""

import math
from typing import Dict, Optional, Tuple
from datetime import datetime, date

class CalculationService:
    """Serviço para cálculos de avaliação física e nutrição"""
    
    @staticmethod
    def calcular_imc(peso: float, altura: float) -> float:
        """Calcular Índice de Massa Corporal"""
        return peso / (altura ** 2)
    
    @staticmethod
    def classificar_imc(imc: float) -> str:
        """Classificar IMC segundo OMS"""
        if imc < 18.5:
            return "Abaixo do peso"
        elif imc < 25:
            return "Peso normal"
        elif imc < 30:
            return "Sobrepeso"
        elif imc < 35:
            return "Obesidade grau I"
        elif imc < 40:
            return "Obesidade grau II"
        else:
            return "Obesidade grau III"
    
    @staticmethod
    def calcular_idade(nascimento: date) -> int:
        """Calcular idade em anos"""
        hoje = date.today()
        return hoje.year - nascimento.year - ((hoje.month, hoje.day) < (nascimento.month, nascimento.day))
    
    @staticmethod
    def calcular_gordura_pollock_3_dobras(
        dobras: Dict[str, float], 
        idade: int, 
        sexo: str
    ) -> float:
        """
        Calcular percentual de gordura pelo método Pollock (3 dobras)
        
        Homens: peitoral, abdominal, coxa
        Mulheres: tríceps, supra-ilíaca, coxa
        """
        if sexo.lower() == "masculino":
            # Fórmula para homens
            soma_dobras = dobras.get("peitoral", 0) + dobras.get("abdominal", 0) + dobras.get("coxa", 0)
            densidade = 1.10938 - (0.0008267 * soma_dobras) + (0.0000016 * (soma_dobras ** 2)) - (0.0002574 * idade)
        else:
            # Fórmula para mulheres
            soma_dobras = dobras.get("triceps", 0) + dobras.get("supra_iliaca", 0) + dobras.get("coxa", 0)
            densidade = 1.0994921 - (0.0009929 * soma_dobras) + (0.0000023 * (soma_dobras ** 2)) - (0.0001392 * idade)
        
        # Fórmula de Siri para converter densidade em percentual de gordura
        percentual_gordura = ((4.95 / densidade) - 4.5) * 100
        return max(0, percentual_gordura)
    
    @staticmethod
    def calcular_gordura_pollock_7_dobras(
        dobras: Dict[str, float], 
        idade: int, 
        sexo: str
    ) -> float:
        """
        Calcular percentual de gordura pelo método Pollock (7 dobras)
        
        7 dobras: peitoral, axilar média, tríceps, subescapular, abdominal, supra-ilíaca, coxa
        """
        soma_dobras = sum([
            dobras.get("peitoral", 0),
            dobras.get("axilar_media", 0),
            dobras.get("triceps", 0),
            dobras.get("subescapular", 0),
            dobras.get("abdominal", 0),
            dobras.get("supra_iliaca", 0),
            dobras.get("coxa", 0)
        ])
        
        if sexo.lower() == "masculino":
            densidade = 1.112 - (0.00043499 * soma_dobras) + (0.00000055 * (soma_dobras ** 2)) - (0.00028826 * idade)
        else:
            densidade = 1.097 - (0.00046971 * soma_dobras) + (0.00000056 * (soma_dobras ** 2)) - (0.00012828 * idade)
        
        percentual_gordura = ((4.95 / densidade) - 4.5) * 100
        return max(0, percentual_gordura)
    
    @staticmethod
    def calcular_massa_gorda_magra(peso: float, percentual_gordura: float) -> Tuple[float, float]:
        """Calcular massa gorda e massa magra"""
        massa_gorda = peso * (percentual_gordura / 100)
        massa_magra = peso - massa_gorda
        return massa_gorda, massa_magra
    
    @staticmethod
    def calcular_tmb_harris_benedict(peso: float, altura: float, idade: int, sexo: str) -> float:
        """
        Calcular Taxa Metabólica Basal pela fórmula de Harris-Benedict (revisada)
        """
        altura_cm = altura * 100  # converter para cm
        
        if sexo.lower() == "masculino":
            tmb = 88.362 + (13.397 * peso) + (4.799 * altura_cm) - (5.677 * idade)
        else:
            tmb = 447.593 + (9.247 * peso) + (3.098 * altura_cm) - (4.330 * idade)
        
        return tmb
    
    @staticmethod
    def calcular_tmb_mifflin_st_jeor(peso: float, altura: float, idade: int, sexo: str) -> float:
        """
        Calcular Taxa Metabólica Basal pela fórmula de Mifflin-St Jeor
        """
        altura_cm = altura * 100  # converter para cm
        
        if sexo.lower() == "masculino":
            tmb = (10 * peso) + (6.25 * altura_cm) - (5 * idade) + 5
        else:
            tmb = (10 * peso) + (6.25 * altura_cm) - (5 * idade) - 161
        
        return tmb
    
    @staticmethod
    def calcular_get(tmb: float, nivel_atividade: str) -> float:
        """
        Calcular Gasto Energético Total baseado no nível de atividade
        """
        fatores_atividade = {
            "sedentario": 1.2,          # Pouco ou nenhum exercício
            "leve": 1.375,              # Exercício leve 1-3 dias/semana
            "moderado": 1.55,           # Exercício moderado 3-5 dias/semana
            "intenso": 1.725,           # Exercício intenso 6-7 dias/semana
            "muito_intenso": 1.9        # Exercício muito intenso, trabalho físico
        }
        
        fator = fatores_atividade.get(nivel_atividade.lower(), 1.2)
        return tmb * fator
    
    @staticmethod
    def calcular_calorias_objetivo(get: float, objetivo: str, percentual_ajuste: Optional[float] = None) -> float:
        """
        Calcular calorias para objetivo específico
        """
        if percentual_ajuste is not None:
            return get * (1 + percentual_ajuste / 100)
        
        ajustes_padrao = {
            "deficit": -0.20,    # -20% para emagrecimento
            "manutencao": 0.0,   # 0% para manutenção
            "superavit": 0.15    # +15% para ganho de peso
        }
        
        ajuste = ajustes_padrao.get(objetivo.lower(), 0.0)
        return get * (1 + ajuste)
    
    @staticmethod
    def calcular_macronutrientes(
        calorias: float, 
        proteinas_pct: float = 25, 
        carboidratos_pct: float = 45, 
        gorduras_pct: float = 30
    ) -> Dict[str, float]:
        """
        Calcular distribuição de macronutrientes em gramas
        """
        # Verificar se percentuais somam 100%
        total_pct = proteinas_pct + carboidratos_pct + gorduras_pct
        if abs(total_pct - 100) > 0.1:
            raise ValueError("Percentuais de macronutrientes devem somar 100%")
        
        # Calorias por grama: proteína=4, carboidrato=4, gordura=9
        proteinas_g = (calorias * proteinas_pct / 100) / 4
        carboidratos_g = (calorias * carboidratos_pct / 100) / 4
        gorduras_g = (calorias * gorduras_pct / 100) / 9
        
        return {
            "proteinas_g": round(proteinas_g, 1),
            "carboidratos_g": round(carboidratos_g, 1),
            "gorduras_g": round(gorduras_g, 1),
            "calorias_total": round(calorias, 0)
        }
    
    @staticmethod
    def classificar_percentual_gordura(percentual: float, idade: int, sexo: str) -> str:
        """
        Classificar percentual de gordura baseado em idade e sexo
        """
        if sexo.lower() == "masculino":
            if idade < 30:
                if percentual < 8: return "Muito baixo"
                elif percentual < 14: return "Baixo"
                elif percentual < 18: return "Normal"
                elif percentual < 25: return "Alto"
                else: return "Muito alto"
            elif idade < 50:
                if percentual < 11: return "Muito baixo"
                elif percentual < 17: return "Baixo"
                elif percentual < 22: return "Normal"
                elif percentual < 28: return "Alto"
                else: return "Muito alto"
            else:
                if percentual < 13: return "Muito baixo"
                elif percentual < 20: return "Baixo"
                elif percentual < 25: return "Normal"
                elif percentual < 30: return "Alto"
                else: return "Muito alto"
        else:  # feminino
            if idade < 30:
                if percentual < 16: return "Muito baixo"
                elif percentual < 20: return "Baixo"
                elif percentual < 25: return "Normal"
                elif percentual < 32: return "Alto"
                else: return "Muito alto"
            elif idade < 50:
                if percentual < 18: return "Muito baixo"
                elif percentual < 23: return "Baixo"
                elif percentual < 28: return "Normal"
                elif percentual < 35: return "Alto"
                else: return "Muito alto"
            else:
                if percentual < 20: return "Muito baixo"
                elif percentual < 25: return "Baixo"
                elif percentual < 30: return "Normal"
                elif percentual < 37: return "Alto"
                else: return "Muito alto"
