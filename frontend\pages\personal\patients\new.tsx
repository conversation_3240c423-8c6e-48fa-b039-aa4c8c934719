import { useState } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  User,
  Mail,
  Phone,
  Calendar,
  Target,
  FileText,
  Save,
  X,
  ArrowLeft
} from 'lucide-react'
import Layout from '../../../components/Layout'

const patientSchema = z.object({
  nome: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido').optional().or(z.literal('')),
  telefone: z.string().optional(),
  nascimento: z.string().optional(),
  sexo: z.enum(['masculino', 'feminino', 'outro']).optional(),
  profissao: z.string().optional(),
  objetivo: z.string().optional(),
  observacoes: z.string().optional(),
})

type PatientForm = z.infer<typeof patientSchema>

export default function NewPatientPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<PatientForm>({
    resolver: zodResolver(patientSchema),
  })

  const onSubmit = async (data: PatientForm) => {
    setIsLoading(true)
    setError('')

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Simular criação do paciente (mock)
      console.log('Dados do paciente:', data)

      // Simular delay da API
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Simular ID do paciente criado
      const patientId = Math.floor(Math.random() * 1000) + 1

      // Redirecionar para lista de pacientes (já que a página de detalhes ainda não existe)
      router.push('/personal/patients')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao criar paciente')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Layout title="Novo Paciente - Personal Trainer">
      <Head>
        <title>Novo Paciente - Personal Trainer | Hypatium</title>
        <meta name="description" content="Cadastrar novo paciente" />
      </Head>

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Page Header */}
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <Link
                href="/personal/patients"
                className="mr-4 p-2 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <User className="h-6 w-6 text-blue-600 mr-2" />
                  Novo Paciente - Personal Trainer
                </h1>
                <p className="text-gray-600">Cadastre um novo paciente para acompanhamento</p>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                <div className="md:grid md:grid-cols-3 md:gap-6">
                  <div className="md:col-span-1">
                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                      Informações Pessoais
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Dados básicos do paciente para identificação e contato.
                    </p>
                  </div>
                  <div className="mt-5 md:mt-0 md:col-span-2">
                    <div className="grid grid-cols-6 gap-6">
                      {error && (
                        <div className="col-span-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                          {error}
                        </div>
                      )}

                      <div className="col-span-6">
                        <label htmlFor="nome" className="block text-sm font-medium text-gray-700">
                          Nome completo *
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <User className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            {...register('nome')}
                            type="text"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Nome completo do paciente"
                          />
                        </div>
                        {errors.nome && (
                          <p className="mt-1 text-sm text-red-600">{errors.nome.message}</p>
                        )}
                      </div>

                      <div className="col-span-6 sm:col-span-3">
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                          Email
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Mail className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            {...register('email')}
                            type="email"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="<EMAIL>"
                          />
                        </div>
                        {errors.email && (
                          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                        )}
                      </div>

                      <div className="col-span-6 sm:col-span-3">
                        <label htmlFor="telefone" className="block text-sm font-medium text-gray-700">
                          Telefone
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Phone className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            {...register('telefone')}
                            type="tel"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="(11) 99999-9999"
                          />
                        </div>
                      </div>

                      <div className="col-span-6 sm:col-span-3">
                        <label htmlFor="nascimento" className="block text-sm font-medium text-gray-700">
                          Data de nascimento
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Calendar className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            {...register('nascimento')}
                            type="date"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </div>

                      <div className="col-span-6 sm:col-span-3">
                        <label htmlFor="sexo" className="block text-sm font-medium text-gray-700">
                          Sexo
                        </label>
                        <select
                          {...register('sexo')}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">Selecione...</option>
                          <option value="masculino">Masculino</option>
                          <option value="feminino">Feminino</option>
                          <option value="outro">Outro</option>
                        </select>
                      </div>

                      <div className="col-span-6">
                        <label htmlFor="profissao" className="block text-sm font-medium text-gray-700">
                          Profissão
                        </label>
                        <input
                          {...register('profissao')}
                          type="text"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Profissão do paciente"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                <div className="md:grid md:grid-cols-3 md:gap-6">
                  <div className="md:col-span-1">
                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                      Objetivos e Observações
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Informações sobre os objetivos do paciente e observações importantes.
                    </p>
                  </div>
                  <div className="mt-5 md:mt-0 md:col-span-2">
                    <div className="grid grid-cols-6 gap-6">
                      <div className="col-span-6">
                        <label htmlFor="objetivo" className="block text-sm font-medium text-gray-700">
                          Objetivo principal
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute top-3 left-3 pointer-events-none">
                            <Target className="h-5 w-5 text-gray-400" />
                          </div>
                          <textarea
                            {...register('objetivo')}
                            rows={3}
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Ex: Emagrecimento, ganho de massa muscular, condicionamento físico..."
                          />
                        </div>
                      </div>

                      <div className="col-span-6">
                        <label htmlFor="observacoes" className="block text-sm font-medium text-gray-700">
                          Observações
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute top-3 left-3 pointer-events-none">
                            <FileText className="h-5 w-5 text-gray-400" />
                          </div>
                          <textarea
                            {...register('observacoes')}
                            rows={4}
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Lesões, limitações, medicamentos, histórico médico relevante..."
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <Link
                  href="/personal/patients"
                  className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancelar
                </Link>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isLoading ? (
                    <div className="loading-spinner mr-2"></div>
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Salvar Paciente
                </button>
              </div>
          </form>
        </div>
      </div>
    </Layout>
  )
}
