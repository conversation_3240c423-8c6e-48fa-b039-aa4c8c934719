"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlunoService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const bcrypt = require("bcryptjs");
let AlunoService = class AlunoService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createAlunoDto, personalId) {
        const hashedPassword = await bcrypt.hash(createAlunoDto.senha, 10);
        const aluno = await this.prisma.aluno.create({
            data: {
                ...createAlunoDto,
                senha: hashedPassword,
                personalId,
            },
        });
        const { senha, ...result } = aluno;
        return result;
    }
    async findAllByPersonal(personalId) {
        return this.prisma.aluno.findMany({
            where: { personalId },
            select: {
                id: true,
                nome: true,
                email: true,
                telefone: true,
                pesoAtual: true,
                estatura: true,
                idade: true,
                dataNascimento: true,
                habitos: true,
                objetivos: true,
                dataCadastro: true,
                _count: {
                    select: {
                        avaliacoes: true,
                    },
                },
            },
            orderBy: {
                dataCadastro: 'desc',
            },
        });
    }
    async findOne(id, personalId) {
        const aluno = await this.prisma.aluno.findUnique({
            where: { id },
            include: {
                personal: {
                    select: {
                        id: true,
                        nome: true,
                        email: true,
                    },
                },
                avaliacoes: {
                    orderBy: {
                        dataAvaliacao: 'desc',
                    },
                    take: 5,
                    select: {
                        id: true,
                        dataAvaliacao: true,
                        valorIMC: true,
                        categoriaIMC: true,
                        guedesPercentual: true,
                        pollock3Percentual: true,
                        pollock7Percentual: true,
                    },
                },
                _count: {
                    select: {
                        avaliacoes: true,
                    },
                },
            },
        });
        if (!aluno) {
            throw new common_1.NotFoundException('Aluno não encontrado');
        }
        if (personalId && aluno.personalId !== personalId) {
            throw new common_1.ForbiddenException('Acesso negado a este aluno');
        }
        const { senha, ...result } = aluno;
        return result;
    }
    async update(id, updateAlunoDto, personalId) {
        const aluno = await this.findOne(id, personalId);
        const updateData = { ...updateAlunoDto };
        if (updateAlunoDto.senha) {
            updateData.senha = await bcrypt.hash(updateAlunoDto.senha, 10);
        }
        const updatedAluno = await this.prisma.aluno.update({
            where: { id },
            data: updateData,
        });
        const { senha, ...result } = updatedAluno;
        return result;
    }
    async remove(id, personalId) {
        await this.findOne(id, personalId);
        await this.prisma.aluno.delete({
            where: { id },
        });
        return { message: 'Aluno removido com sucesso' };
    }
    async getAlunoStats(id, personalId) {
        await this.findOne(id, personalId);
        const [totalAvaliacoes, ultimaAvaliacao, primeiraAvaliacao] = await Promise.all([
            this.prisma.avaliacaoComposicao.count({
                where: { alunoId: id },
            }),
            this.prisma.avaliacaoComposicao.findFirst({
                where: { alunoId: id },
                orderBy: { dataAvaliacao: 'desc' },
                select: {
                    id: true,
                    dataAvaliacao: true,
                    valorIMC: true,
                    categoriaIMC: true,
                    guedesPercentual: true,
                    pollock3Percentual: true,
                    pollock7Percentual: true,
                },
            }),
            this.prisma.avaliacaoComposicao.findFirst({
                where: { alunoId: id },
                orderBy: { dataAvaliacao: 'asc' },
                select: {
                    id: true,
                    dataAvaliacao: true,
                    valorIMC: true,
                    categoriaIMC: true,
                    guedesPercentual: true,
                    pollock3Percentual: true,
                    pollock7Percentual: true,
                },
            }),
        ]);
        return {
            totalAvaliacoes,
            ultimaAvaliacao,
            primeiraAvaliacao,
            evolucao: primeiraAvaliacao && ultimaAvaliacao ? {
                imc: {
                    inicial: primeiraAvaliacao.valorIMC,
                    atual: ultimaAvaliacao.valorIMC,
                    diferenca: ultimaAvaliacao.valorIMC ?
                        (ultimaAvaliacao.valorIMC - (primeiraAvaliacao.valorIMC || 0)) : null,
                },
                gordura: {
                    inicial: primeiraAvaliacao.guedesPercentual || primeiraAvaliacao.pollock3Percentual || primeiraAvaliacao.pollock7Percentual,
                    atual: ultimaAvaliacao.guedesPercentual || ultimaAvaliacao.pollock3Percentual || ultimaAvaliacao.pollock7Percentual,
                },
            } : null,
        };
    }
};
exports.AlunoService = AlunoService;
exports.AlunoService = AlunoService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AlunoService);
//# sourceMappingURL=aluno.service.js.map