"""
Endpoints de avaliações físicas
"""

from typing import Any, List, Dict
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.models.models import User
from app.schemas.assessment import (
    PhysicalAssessment,
    PhysicalAssessmentCreate,
    PhysicalAssessmentUpdate,
    BodyCompositionCalculation,
    MetabolicCalculation
)
from app.services.assessment_service import AssessmentService

router = APIRouter()

@router.post("/", response_model=PhysicalAssessment)
def create_assessment(
    *,
    db: Session = Depends(get_db),
    assessment_in: PhysicalAssessmentCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Criar nova avaliação física
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem criar avaliações físicas")

    assessment_service = AssessmentService(db)

    # Definir avaliador como usuário atual se não especificado
    if not assessment_in.avaliador_id:
        assessment_in.avaliador_id = current_user.id

    try:
        assessment = assessment_service.create(assessment_in)
        return assessment
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/patient/{patient_id}", response_model=List[PhysicalAssessment])
def read_assessments_by_patient(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter avaliações físicas de um paciente
    """
    assessment_service = AssessmentService(db)
    assessments = assessment_service.get_by_patient(patient_id, skip=skip, limit=limit)
    return assessments

@router.get("/patient/{patient_id}/latest", response_model=PhysicalAssessment)
def read_latest_assessment(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter última avaliação física de um paciente
    """
    assessment_service = AssessmentService(db)
    assessment = assessment_service.get_latest_by_patient(patient_id)

    if not assessment:
        raise HTTPException(status_code=404, detail="Nenhuma avaliação encontrada para este paciente")

    return assessment

@router.get("/{assessment_id}", response_model=PhysicalAssessment)
def read_assessment(
    *,
    db: Session = Depends(get_db),
    assessment_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter avaliação física por ID
    """
    assessment_service = AssessmentService(db)
    assessment = assessment_service.get(assessment_id)

    if not assessment:
        raise HTTPException(status_code=404, detail="Avaliação não encontrada")

    return assessment

@router.put("/{assessment_id}", response_model=PhysicalAssessment)
def update_assessment(
    *,
    db: Session = Depends(get_db),
    assessment_id: int,
    assessment_in: PhysicalAssessmentUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Atualizar avaliação física
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem atualizar avaliações físicas")

    assessment_service = AssessmentService(db)
    assessment = assessment_service.update(assessment_id, assessment_in)

    if not assessment:
        raise HTTPException(status_code=404, detail="Avaliação não encontrada")

    return assessment

@router.delete("/{assessment_id}")
def delete_assessment(
    *,
    db: Session = Depends(get_db),
    assessment_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Deletar avaliação física
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem deletar avaliações físicas")

    assessment_service = AssessmentService(db)
    success = assessment_service.delete(assessment_id)

    if not success:
        raise HTTPException(status_code=404, detail="Avaliação não encontrada")

    return {"message": "Avaliação deletada com sucesso"}

@router.post("/calculate/body-composition", response_model=Dict[str, Any])
def calculate_body_composition(
    *,
    calculation_data: BodyCompositionCalculation,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Calcular composição corporal sem salvar
    """
    assessment_service = AssessmentService(None)  # Não precisa de DB para cálculos
    result = assessment_service.calculate_body_composition(calculation_data)
    return result

@router.post("/calculate/metabolism", response_model=Dict[str, Any])
def calculate_metabolism(
    *,
    calculation_data: MetabolicCalculation,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Calcular dados metabólicos sem salvar
    """
    assessment_service = AssessmentService(None)  # Não precisa de DB para cálculos
    result = assessment_service.calculate_metabolism(calculation_data)
    return result

@router.get("/patient/{patient_id}/progress", response_model=List[Dict[str, Any]])
def get_progress_data(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter dados de progresso do paciente para gráficos
    """
    assessment_service = AssessmentService(db)
    progress_data = assessment_service.get_progress_data(patient_id, limit=limit)
    return progress_data
