#!/usr/bin/env python3
"""
Script de teste completo do sistema Hypatium
Testa todos os módulos e funcionalidades principais
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional

class HypatiumSystemTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.token = None
        self.test_results = []
        self.created_resources = {
            "users": [],
            "patients": [],
            "assessments": [],
            "workouts": [],
            "nutrition_plans": [],
            "psychology_sessions": [],
            "reports": []
        }
    
    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """Log resultado do teste"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        }
        if data:
            result["data"] = data
        
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        
        if not success:
            print(f"   Dados: {data}")
    
    def make_request(self, method: str, endpoint: str, data: Dict = None, files: Dict = None) -> Optional[Dict]:
        """Fazer requisição HTTP"""
        url = f"{self.base_url}{endpoint}"
        headers = {}
        
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        
        try:
            if method == "GET":
                response = requests.get(url, headers=headers)
            elif method == "POST":
                if files:
                    response = requests.post(url, headers=headers, files=files, data=data)
                else:
                    headers["Content-Type"] = "application/json"
                    response = requests.post(url, headers=headers, json=data)
            elif method == "PUT":
                headers["Content-Type"] = "application/json"
                response = requests.put(url, headers=headers, json=data)
            elif method == "DELETE":
                response = requests.delete(url, headers=headers)
            else:
                return None
            
            if response.status_code < 400:
                return response.json() if response.content else {}
            else:
                return {"error": response.text, "status_code": response.status_code}
                
        except Exception as e:
            return {"error": str(e)}
    
    def test_health_check(self):
        """Testar health check"""
        result = self.make_request("GET", "/health")
        
        if result and result.get("status") == "healthy":
            self.log_test("Health Check", True, "Sistema saudável")
        else:
            self.log_test("Health Check", False, "Sistema não saudável", result)
    
    def test_authentication(self):
        """Testar autenticação"""
        # Registrar usuário de teste
        user_data = {
            "email": "<EMAIL>",
            "password": "testpass123",
            "nome": "Usuário Teste",
            "tipo": "admin"
        }
        
        register_result = self.make_request("POST", "/api/v1/auth/register", user_data)
        
        if register_result and not register_result.get("error"):
            self.log_test("Registro de Usuário", True, "Usuário registrado com sucesso")
            self.created_resources["users"].append(register_result.get("id"))
        else:
            self.log_test("Registro de Usuário", False, "Falha no registro", register_result)
            return False
        
        # Fazer login
        login_data = {
            "username": user_data["email"],
            "password": user_data["password"]
        }
        
        login_result = self.make_request("POST", "/api/v1/auth/login", login_data)
        
        if login_result and login_result.get("access_token"):
            self.token = login_result["access_token"]
            self.log_test("Login", True, "Login realizado com sucesso")
            return True
        else:
            self.log_test("Login", False, "Falha no login", login_result)
            return False
    
    def test_patient_management(self):
        """Testar gestão de pacientes"""
        # Criar paciente
        patient_data = {
            "nome": "João Silva Teste",
            "email": "<EMAIL>",
            "telefone": "(11) 99999-9999",
            "nascimento": "1990-01-01",
            "sexo": "masculino",
            "objetivo": "Perda de peso"
        }
        
        create_result = self.make_request("POST", "/api/v1/patients/", patient_data)
        
        if create_result and not create_result.get("error"):
            patient_id = create_result["id"]
            self.created_resources["patients"].append(patient_id)
            self.log_test("Criação de Paciente", True, f"Paciente criado com ID {patient_id}")
            
            # Buscar paciente
            get_result = self.make_request("GET", f"/api/v1/patients/{patient_id}")
            
            if get_result and get_result.get("nome") == patient_data["nome"]:
                self.log_test("Busca de Paciente", True, "Paciente encontrado corretamente")
                return patient_id
            else:
                self.log_test("Busca de Paciente", False, "Paciente não encontrado", get_result)
        else:
            self.log_test("Criação de Paciente", False, "Falha na criação", create_result)
        
        return None
    
    def test_physical_assessment(self, patient_id: int):
        """Testar avaliação física"""
        assessment_data = {
            "paciente_id": patient_id,
            "peso": 75.5,
            "altura": 1.78,
            "data_avaliacao": "2024-01-15T10:00:00"
        }
        
        create_result = self.make_request("POST", "/api/v1/assessments/", assessment_data)
        
        if create_result and not create_result.get("error"):
            assessment_id = create_result["id"]
            self.created_resources["assessments"].append(assessment_id)
            
            # Verificar se IMC foi calculado
            if create_result.get("imc"):
                expected_imc = 75.5 / (1.78 ** 2)
                calculated_imc = create_result["imc"]
                
                if abs(calculated_imc - expected_imc) < 0.1:
                    self.log_test("Avaliação Física", True, f"Avaliação criada com IMC calculado: {calculated_imc:.2f}")
                    return assessment_id
                else:
                    self.log_test("Avaliação Física", False, f"IMC incorreto: {calculated_imc} vs {expected_imc:.2f}")
            else:
                self.log_test("Avaliação Física", False, "IMC não calculado", create_result)
        else:
            self.log_test("Avaliação Física", False, "Falha na criação", create_result)
        
        return None
    
    def test_nutrition_system(self, patient_id: int):
        """Testar sistema de nutrição"""
        # Testar calculadora nutricional
        calc_data = {
            "peso": 75.0,
            "altura": 1.78,
            "idade": 30,
            "sexo": "masculino",
            "nivel_atividade": "moderado",
            "objetivo": "manutencao"
        }
        
        calc_result = self.make_request("POST", "/api/v1/nutrition/calculate", calc_data)
        
        if calc_result and calc_result.get("tmb") and calc_result.get("get"):
            self.log_test("Calculadora Nutricional", True, f"TMB: {calc_result['tmb']}, GET: {calc_result['get']}")
            
            # Criar plano nutricional
            plan_data = {
                "paciente_id": patient_id,
                "calorias_alvo": calc_result["calorias_alvo"],
                "proteinas_g": calc_result["macronutrientes"]["proteinas"],
                "carboidratos_g": calc_result["macronutrientes"]["carboidratos"],
                "gorduras_g": calc_result["macronutrientes"]["gorduras"]
            }
            
            plan_result = self.make_request("POST", "/api/v1/nutrition/", plan_data)
            
            if plan_result and not plan_result.get("error"):
                plan_id = plan_result["id"]
                self.created_resources["nutrition_plans"].append(plan_id)
                self.log_test("Plano Nutricional", True, f"Plano criado com ID {plan_id}")
                return plan_id
            else:
                self.log_test("Plano Nutricional", False, "Falha na criação", plan_result)
        else:
            self.log_test("Calculadora Nutricional", False, "Falha no cálculo", calc_result)
        
        return None
    
    def test_workout_system(self, patient_id: int):
        """Testar sistema de treinos"""
        workout_data = {
            "paciente_id": patient_id,
            "nome": "Treino Teste A",
            "descricao": "Treino de teste para validação do sistema",
            "data_inicio": "2024-01-15"
        }
        
        create_result = self.make_request("POST", "/api/v1/workouts/", workout_data)
        
        if create_result and not create_result.get("error"):
            workout_id = create_result["id"]
            self.created_resources["workouts"].append(workout_id)
            self.log_test("Criação de Treino", True, f"Treino criado com ID {workout_id}")
            
            # Adicionar exercício
            exercise_data = {
                "treino_id": workout_id,
                "nome_exercicio": "Supino Reto",
                "grupo_muscular": "Peitoral",
                "series": 3,
                "repeticoes": "10-12",
                "carga": "60kg",
                "descanso": "90s"
            }
            
            exercise_result = self.make_request("POST", "/api/v1/workouts/exercises/", exercise_data)
            
            if exercise_result and not exercise_result.get("error"):
                self.log_test("Adição de Exercício", True, "Exercício adicionado ao treino")
                return workout_id
            else:
                self.log_test("Adição de Exercício", False, "Falha na adição", exercise_result)
        else:
            self.log_test("Criação de Treino", False, "Falha na criação", create_result)
        
        return None
    
    def test_psychology_system(self, patient_id: int):
        """Testar sistema de psicologia"""
        session_data = {
            "paciente_id": patient_id,
            "data_sessao": "2024-01-15T14:00:00",
            "tipo_sessao": "individual",
            "observacoes": "Sessão de teste para validação do sistema",
            "humor_inicial": 7,
            "humor_final": 8
        }
        
        create_result = self.make_request("POST", "/api/v1/psychology/", session_data)
        
        if create_result and not create_result.get("error"):
            session_id = create_result["id"]
            self.created_resources["psychology_sessions"].append(session_id)
            self.log_test("Sessão Psicológica", True, f"Sessão criada com ID {session_id}")
            return session_id
        else:
            self.log_test("Sessão Psicológica", False, "Falha na criação", create_result)
        
        return None
    
    def test_report_system(self, patient_id: int):
        """Testar sistema de relatórios"""
        report_data = {
            "paciente_id": patient_id,
            "tipo_relatorio": "avaliacao_completa",
            "incluir_avaliacoes": True,
            "incluir_treinos": True,
            "incluir_nutricao": True,
            "incluir_psicologia": True
        }
        
        create_result = self.make_request("POST", "/api/v1/reports/generate", report_data)
        
        if create_result and not create_result.get("error"):
            report_id = create_result["id"]
            self.created_resources["reports"].append(report_id)
            self.log_test("Geração de Relatório", True, f"Relatório gerado com ID {report_id}")
            return report_id
        else:
            self.log_test("Geração de Relatório", False, "Falha na geração", create_result)
        
        return None
    
    def test_pdf_generation(self, report_id: int):
        """Testar geração de PDF"""
        pdf_result = self.make_request("POST", f"/api/v1/pdf/report/{report_id}")
        
        if pdf_result and pdf_result.get("success"):
            self.log_test("Geração de PDF", True, f"PDF gerado: {pdf_result.get('filename')}")
        else:
            self.log_test("Geração de PDF", False, "Falha na geração", pdf_result)
    
    def test_notifications(self, patient_id: int):
        """Testar sistema de notificações"""
        # Testar status do sistema
        status_result = self.make_request("GET", "/api/v1/notifications/status")
        
        if status_result and not status_result.get("error"):
            self.log_test("Status de Notificações", True, "Sistema de notificações operacional")
            
            # Testar notificação de teste
            test_result = self.make_request("POST", f"/api/v1/notifications/test/{patient_id}")
            
            if test_result and test_result.get("success"):
                self.log_test("Teste de Notificação", True, "Notificação de teste enviada")
            else:
                self.log_test("Teste de Notificação", False, "Falha no envio", test_result)
        else:
            self.log_test("Status de Notificações", False, "Sistema indisponível", status_result)
    
    def test_monitoring(self):
        """Testar sistema de monitoramento"""
        # Testar métricas do sistema
        metrics_result = self.make_request("GET", "/api/v1/monitoring/metrics/system")
        
        if metrics_result and metrics_result.get("cpu"):
            self.log_test("Métricas do Sistema", True, f"CPU: {metrics_result['cpu']['percent']}%")
        else:
            self.log_test("Métricas do Sistema", False, "Falha na obtenção", metrics_result)
        
        # Testar health check detalhado
        health_result = self.make_request("GET", "/api/v1/monitoring/health/detailed")
        
        if health_result and health_result.get("overall_status"):
            self.log_test("Health Check Detalhado", True, f"Status: {health_result['overall_status']}")
        else:
            self.log_test("Health Check Detalhado", False, "Falha na verificação", health_result)
    
    def run_complete_test(self):
        """Executar teste completo do sistema"""
        print("🚀 Iniciando teste completo do sistema Hypatium...\n")
        
        # Testes básicos
        self.test_health_check()
        
        if not self.test_authentication():
            print("❌ Falha na autenticação. Interrompendo testes.")
            return False
        
        # Testes de módulos principais
        patient_id = self.test_patient_management()
        if not patient_id:
            print("❌ Falha na gestão de pacientes. Interrompendo testes.")
            return False
        
        assessment_id = self.test_physical_assessment(patient_id)
        nutrition_plan_id = self.test_nutrition_system(patient_id)
        workout_id = self.test_workout_system(patient_id)
        session_id = self.test_psychology_system(patient_id)
        
        # Testes de funcionalidades avançadas
        report_id = self.test_report_system(patient_id)
        if report_id:
            self.test_pdf_generation(report_id)
        
        self.test_notifications(patient_id)
        self.test_monitoring()
        
        # Resumo dos resultados
        self.print_test_summary()
        
        return True
    
    def print_test_summary(self):
        """Imprimir resumo dos testes"""
        print("\n" + "="*60)
        print("📊 RESUMO DOS TESTES")
        print("="*60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - successful_tests
        
        print(f"Total de testes: {total_tests}")
        print(f"✅ Sucessos: {successful_tests}")
        print(f"❌ Falhas: {failed_tests}")
        print(f"📈 Taxa de sucesso: {(successful_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Testes que falharam:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test']}: {result['message']}")
        
        print("\n📋 Recursos criados durante os testes:")
        for resource_type, ids in self.created_resources.items():
            if ids:
                print(f"   - {resource_type}: {len(ids)} criados")
        
        print("\n🎯 Sistema Hypatium testado com sucesso!")

if __name__ == "__main__":
    # Verificar se o servidor está rodando
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Servidor não está respondendo corretamente")
            sys.exit(1)
    except requests.exceptions.RequestException:
        print("❌ Servidor não está rodando em http://localhost:8000")
        print("   Execute: uvicorn main:app --reload")
        sys.exit(1)
    
    # Executar testes
    tester = HypatiumSystemTester()
    success = tester.run_complete_test()
    
    sys.exit(0 if success else 1)
