import { Controller, Post, Body, UseGuards, Request } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @ApiOperation({ summary: 'Login de usuário' })
  @ApiResponse({ status: 200, description: 'Login realizado com sucesso' })
  @ApiResponse({ status: 401, description: 'Credenciais inválidas' })
  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @ApiOperation({ summary: 'Registro de Personal Trainer' })
  @ApiResponse({ status: 201, description: 'Personal Trainer criado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @Post('register/personal')
  async registerPersonal(@Body() registerDto: RegisterDto) {
    return this.authService.registerPersonal(registerDto);
  }

  @ApiOperation({ summary: 'Verificar token JWT' })
  @ApiResponse({ status: 200, description: 'Token válido' })
  @ApiResponse({ status: 401, description: 'Token inválido' })
  @UseGuards(AuthGuard('jwt'))
  @Post('verify')
  async verify(@Request() req) {
    return {
      valid: true,
      user: req.user,
    };
  }
}
