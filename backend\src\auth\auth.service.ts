import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import * as bcrypt from 'bcryptjs';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    // Primeiro tenta encontrar um Personal Trainer
    const personal = await this.prisma.personalTrainer.findUnique({
      where: { email },
    });

    if (personal && await bcrypt.compare(password, personal.senha)) {
      const { senha, ...result } = personal;
      return { ...result, userType: 'personal' };
    }

    // Se não encontrou, tenta encontrar um Aluno
    const aluno = await this.prisma.aluno.findUnique({
      where: { email },
    });

    if (aluno && await bcrypt.compare(password, aluno.senha)) {
      const { senha, ...result } = aluno;
      return { ...result, userType: 'aluno' };
    }

    return null;
  }

  async login(loginDto: LoginDto) {
    const user = await this.validateUser(loginDto.email, loginDto.password);
    
    if (!user) {
      throw new UnauthorizedException('Credenciais inválidas');
    }

    const payload = { 
      email: user.email, 
      sub: user.id, 
      userType: user.userType,
      nome: user.nome 
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        email: user.email,
        nome: user.nome,
        userType: user.userType,
      },
    };
  }

  async registerPersonal(registerDto: RegisterDto) {
    const hashedPassword = await bcrypt.hash(registerDto.password, 10);
    
    const personal = await this.prisma.personalTrainer.create({
      data: {
        nome: registerDto.nome,
        email: registerDto.email,
        telefone: registerDto.telefone,
        senha: hashedPassword,
      },
    });

    const { senha, ...result } = personal;
    return result;
  }

  async registerAluno(registerDto: RegisterDto & { personalId: string; pesoAtual: number; estatura: number; dataNascimento: Date; idade: number }) {
    const hashedPassword = await bcrypt.hash(registerDto.password, 10);
    
    const aluno = await this.prisma.aluno.create({
      data: {
        nome: registerDto.nome,
        email: registerDto.email,
        telefone: registerDto.telefone,
        senha: hashedPassword,
        personalId: registerDto.personalId,
        pesoAtual: registerDto.pesoAtual,
        estatura: registerDto.estatura,
        dataNascimento: registerDto.dataNascimento,
        idade: registerDto.idade,
      },
    });

    const { senha, ...result } = aluno;
    return result;
  }
}
