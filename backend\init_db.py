#!/usr/bin/env python3
"""
Script para inicializar o banco de dados
"""

from app.core.database import engine, Base
from app.models import models

def init_database():
    """Criar todas as tabelas"""
    print("🗄️ Criando tabelas do banco de dados...")
    
    try:
        # Criar todas as tabelas
        Base.metadata.create_all(bind=engine)
        print("✅ Tabelas criadas com sucesso!")
        
        # Listar tabelas criadas
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        print(f"📋 Tabelas criadas: {', '.join(tables)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar tabelas: {str(e)}")
        return False

if __name__ == "__main__":
    success = init_database()
    if success:
        print("\n🎉 Banco de dados inicializado com sucesso!")
    else:
        print("\n❌ Falha na inicialização do banco de dados")
