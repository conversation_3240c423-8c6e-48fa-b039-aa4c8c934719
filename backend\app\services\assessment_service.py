"""
Serviço para gerenciamento de avaliações físicas
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, date

from app.models.models import PhysicalAssessment, Patient
from app.schemas.assessment import (
    PhysicalAssessmentCreate, 
    PhysicalAssessmentUpdate,
    BodyCompositionCalculation,
    MetabolicCalculation
)
from app.services.calculation_service import CalculationService

class AssessmentService:
    def __init__(self, db: Session):
        self.db = db
        self.calc_service = CalculationService()

    def get(self, assessment_id: int) -> Optional[PhysicalAssessment]:
        """Obter avaliação física por ID"""
        return self.db.query(PhysicalAssessment).filter(
            PhysicalAssessment.id == assessment_id
        ).first()

    def get_by_patient(self, patient_id: int, skip: int = 0, limit: int = 100) -> List[PhysicalAssessment]:
        """Obter avaliações físicas de um paciente"""
        return self.db.query(PhysicalAssessment).filter(
            PhysicalAssessment.paciente_id == patient_id
        ).order_by(PhysicalAssessment.data_avaliacao.desc()).offset(skip).limit(limit).all()

    def get_latest_by_patient(self, patient_id: int) -> Optional[PhysicalAssessment]:
        """Obter última avaliação física de um paciente"""
        return self.db.query(PhysicalAssessment).filter(
            PhysicalAssessment.paciente_id == patient_id
        ).order_by(PhysicalAssessment.data_avaliacao.desc()).first()

    def create(self, assessment_in: PhysicalAssessmentCreate) -> PhysicalAssessment:
        """Criar nova avaliação física com cálculos automáticos"""
        
        # Obter dados do paciente para cálculos
        patient = self.db.query(Patient).filter(Patient.id == assessment_in.paciente_id).first()
        if not patient:
            raise ValueError("Paciente não encontrado")

        # Calcular idade se data de nascimento disponível
        idade = None
        if patient.nascimento:
            idade = self.calc_service.calcular_idade(patient.nascimento.date())

        # Criar objeto de avaliação
        db_assessment = PhysicalAssessment(
            **assessment_in.dict()
        )

        # Calcular IMC
        db_assessment.imc = self.calc_service.calcular_imc(
            assessment_in.peso, 
            assessment_in.altura
        )

        # Calcular percentual de gordura se dobras cutâneas disponíveis
        if assessment_in.dobras_cutaneas and idade and patient.sexo:
            metodo = assessment_in.metodo_utilizado or "pollock_3"
            
            if metodo == "pollock_3":
                db_assessment.gordura_percentual = self.calc_service.calcular_gordura_pollock_3_dobras(
                    assessment_in.dobras_cutaneas,
                    idade,
                    patient.sexo.value
                )
            elif metodo == "pollock_7":
                db_assessment.gordura_percentual = self.calc_service.calcular_gordura_pollock_7_dobras(
                    assessment_in.dobras_cutaneas,
                    idade,
                    patient.sexo.value
                )

        # Calcular massa gorda e magra
        if db_assessment.gordura_percentual:
            massa_gorda, massa_magra = self.calc_service.calcular_massa_gorda_magra(
                assessment_in.peso,
                db_assessment.gordura_percentual
            )
            db_assessment.massa_gorda = massa_gorda
            db_assessment.massa_magra = massa_magra

        # Calcular TMB e GET se dados suficientes
        if idade and patient.sexo:
            db_assessment.tmb = self.calc_service.calcular_tmb_mifflin_st_jeor(
                assessment_in.peso,
                assessment_in.altura,
                idade,
                patient.sexo.value
            )
            
            # GET com nível de atividade padrão (pode ser ajustado depois)
            db_assessment.get = self.calc_service.calcular_get(
                db_assessment.tmb,
                "moderado"  # padrão
            )

        # Salvar no banco
        self.db.add(db_assessment)
        self.db.commit()
        self.db.refresh(db_assessment)
        
        return db_assessment

    def update(self, assessment_id: int, assessment_in: PhysicalAssessmentUpdate) -> Optional[PhysicalAssessment]:
        """Atualizar avaliação física"""
        db_assessment = self.get(assessment_id)
        if not db_assessment:
            return None

        # Atualizar campos fornecidos
        update_data = assessment_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_assessment, field, value)

        # Recalcular se peso ou altura foram alterados
        if "peso" in update_data or "altura" in update_data:
            db_assessment.imc = self.calc_service.calcular_imc(
                db_assessment.peso,
                db_assessment.altura
            )

        # Recalcular composição corporal se necessário
        if "dobras_cutaneas" in update_data or "peso" in update_data:
            patient = self.db.query(Patient).filter(Patient.id == db_assessment.paciente_id).first()
            if patient and patient.nascimento and patient.sexo:
                idade = self.calc_service.calcular_idade(patient.nascimento.date())
                
                if db_assessment.dobras_cutaneas:
                    metodo = db_assessment.metodo_utilizado or "pollock_3"
                    
                    if metodo == "pollock_3":
                        db_assessment.gordura_percentual = self.calc_service.calcular_gordura_pollock_3_dobras(
                            db_assessment.dobras_cutaneas,
                            idade,
                            patient.sexo.value
                        )
                    elif metodo == "pollock_7":
                        db_assessment.gordura_percentual = self.calc_service.calcular_gordura_pollock_7_dobras(
                            db_assessment.dobras_cutaneas,
                            idade,
                            patient.sexo.value
                        )

                    # Recalcular massa gorda e magra
                    if db_assessment.gordura_percentual:
                        massa_gorda, massa_magra = self.calc_service.calcular_massa_gorda_magra(
                            db_assessment.peso,
                            db_assessment.gordura_percentual
                        )
                        db_assessment.massa_gorda = massa_gorda
                        db_assessment.massa_magra = massa_magra

        self.db.commit()
        self.db.refresh(db_assessment)
        return db_assessment

    def delete(self, assessment_id: int) -> bool:
        """Deletar avaliação física"""
        db_assessment = self.get(assessment_id)
        if not db_assessment:
            return False

        self.db.delete(db_assessment)
        self.db.commit()
        return True

    def calculate_body_composition(self, calculation_data: BodyCompositionCalculation) -> Dict[str, Any]:
        """Calcular composição corporal sem salvar no banco"""
        
        # Calcular IMC
        imc = self.calc_service.calcular_imc(calculation_data.peso, calculation_data.altura)
        classificacao_imc = self.calc_service.classificar_imc(imc)

        result = {
            "imc": round(imc, 2),
            "classificacao_imc": classificacao_imc
        }

        # Calcular percentual de gordura se dobras disponíveis
        if calculation_data.dobras_cutaneas:
            if calculation_data.metodo == "pollock_3":
                gordura_pct = self.calc_service.calcular_gordura_pollock_3_dobras(
                    calculation_data.dobras_cutaneas,
                    calculation_data.idade,
                    calculation_data.sexo
                )
            elif calculation_data.metodo == "pollock_7":
                gordura_pct = self.calc_service.calcular_gordura_pollock_7_dobras(
                    calculation_data.dobras_cutaneas,
                    calculation_data.idade,
                    calculation_data.sexo
                )
            else:
                gordura_pct = None

            if gordura_pct:
                massa_gorda, massa_magra = self.calc_service.calcular_massa_gorda_magra(
                    calculation_data.peso,
                    gordura_pct
                )
                
                classificacao_gordura = self.calc_service.classificar_percentual_gordura(
                    gordura_pct,
                    calculation_data.idade,
                    calculation_data.sexo
                )

                result.update({
                    "gordura_percentual": round(gordura_pct, 2),
                    "classificacao_gordura": classificacao_gordura,
                    "massa_gorda": round(massa_gorda, 2),
                    "massa_magra": round(massa_magra, 2)
                })

        return result

    def calculate_metabolism(self, calculation_data: MetabolicCalculation) -> Dict[str, Any]:
        """Calcular dados metabólicos sem salvar no banco"""
        
        # Calcular TMB
        tmb = self.calc_service.calcular_tmb_mifflin_st_jeor(
            calculation_data.peso,
            calculation_data.altura,
            calculation_data.idade,
            calculation_data.sexo
        )

        # Calcular GET
        get = self.calc_service.calcular_get(tmb, calculation_data.nivel_atividade)

        # Calcular calorias para objetivo
        calorias_objetivo = self.calc_service.calcular_calorias_objetivo(
            get,
            calculation_data.objetivo
        )

        return {
            "tmb": round(tmb, 0),
            "get": round(get, 0),
            "calorias_objetivo": round(calorias_objetivo, 0),
            "nivel_atividade": calculation_data.nivel_atividade,
            "objetivo": calculation_data.objetivo
        }

    def get_progress_data(self, patient_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Obter dados de progresso do paciente"""
        assessments = self.get_by_patient(patient_id, limit=limit)
        
        progress_data = []
        for assessment in assessments:
            data_point = {
                "data": assessment.data_avaliacao.isoformat(),
                "peso": assessment.peso,
                "imc": assessment.imc,
                "gordura_percentual": assessment.gordura_percentual,
                "massa_gorda": assessment.massa_gorda,
                "massa_magra": assessment.massa_magra
            }
            progress_data.append(data_point)
        
        return progress_data
