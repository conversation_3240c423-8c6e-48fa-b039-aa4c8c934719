import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  Activity, 
  Users, 
  Database, 
  Server,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  HardDrive,
  Cpu,
  MemoryStick,
  RefreshCw,
  Settings,
  FileText,
  Bell
} from 'lucide-react'

interface SystemMetrics {
  cpu: { percent: number; count: number }
  memory: { percent: number; used_gb: number; total_gb: number }
  disk: { percent: number; used_gb: number; total_gb: number }
  uptime: { hours: number }
}

interface DatabaseMetrics {
  connections: { active: number; total: number }
  database_size: string
  tables: { [key: string]: number }
}

interface ApplicationMetrics {
  totals: { users: number; patients: number }
  activity_24h: { assessments: number; psychology_sessions: number; reports_generated: number }
}

interface HealthCheck {
  overall_status: string
  checks: { [key: string]: { status: string; message: string } }
  summary: { total_checks: number; healthy: number; warnings: number; critical: number }
}

export default function AdminDashboard() {
  const router = useRouter()
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null)
  const [databaseMetrics, setDatabaseMetrics] = useState<DatabaseMetrics | null>(null)
  const [applicationMetrics, setApplicationMetrics] = useState<ApplicationMetrics | null>(null)
  const [healthCheck, setHealthCheck] = useState<HealthCheck | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())
  const [currentUser, setCurrentUser] = useState<any>(null)

  useEffect(() => {
    checkAuth()
    fetchData()
    
    // Auto-refresh a cada 30 segundos
    const interval = setInterval(fetchData, 30000)
    return () => clearInterval(interval)
  }, [])

  const checkAuth = async () => {
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/users/me`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const userData = await response.json()
        if (userData.tipo !== 'admin') {
          router.push('/dashboard')
          return
        }
        setCurrentUser(userData)
      } else {
        router.push('/login')
      }
    } catch (error) {
      router.push('/login')
    }
  }

  const fetchData = async () => {
    const token = localStorage.getItem('token')
    if (!token) return

    try {
      const headers = { 'Authorization': `Bearer ${token}` }
      
      // Buscar métricas em paralelo
      const [systemRes, dbRes, appRes, healthRes] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/monitoring/metrics/system`, { headers }),
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/monitoring/metrics/database`, { headers }),
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/monitoring/metrics/application`, { headers }),
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/monitoring/health/detailed`, { headers })
      ])

      if (systemRes.ok) setSystemMetrics(await systemRes.json())
      if (dbRes.ok) setDatabaseMetrics(await dbRes.json())
      if (appRes.ok) setApplicationMetrics(await appRes.json())
      if (healthRes.ok) setHealthCheck(await healthRes.json())

      setLastUpdate(new Date())
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case 'critical': return <XCircle className="h-5 w-5 text-red-600" />
      default: return <Activity className="h-5 w-5 text-gray-600" />
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Dashboard Administrativo - Hypatium</title>
        <meta name="description" content="Dashboard administrativo do sistema Hypatium" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href="/dashboard" className="text-blue-600 hover:text-blue-800">
                  ← Voltar ao Dashboard
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Server className="h-6 w-6 mr-2" />
                Dashboard Administrativo
              </h1>
              <div className="flex items-center space-x-4">
                <button
                  onClick={fetchData}
                  className="p-2 text-gray-600 hover:text-gray-900"
                  title="Atualizar dados"
                >
                  <RefreshCw className="h-5 w-5" />
                </button>
                <span className="text-sm text-gray-500">
                  Última atualização: {lastUpdate.toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            
            {/* Status Geral */}
            {healthCheck && (
              <div className="mb-6">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center">
                      {getStatusIcon(healthCheck.overall_status)}
                      <h3 className="ml-2 text-lg font-medium text-gray-900">
                        Status Geral do Sistema
                      </h3>
                      <span className={`ml-2 text-sm font-medium ${getStatusColor(healthCheck.overall_status)}`}>
                        {healthCheck.overall_status.toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{healthCheck.summary.healthy}</div>
                        <div className="text-sm text-gray-500">Saudáveis</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-600">{healthCheck.summary.warnings}</div>
                        <div className="text-sm text-gray-500">Avisos</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">{healthCheck.summary.critical}</div>
                        <div className="text-sm text-gray-500">Críticos</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-600">{healthCheck.summary.total_checks}</div>
                        <div className="text-sm text-gray-500">Total</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Métricas do Sistema */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {/* CPU */}
              {systemMetrics && (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center">
                      <Cpu className="h-8 w-8 text-blue-600" />
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-gray-900">CPU</h3>
                        <p className="text-2xl font-bold text-gray-900">{systemMetrics.cpu.percent}%</p>
                        <p className="text-sm text-gray-500">{systemMetrics.cpu.count} cores</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Memória */}
              {systemMetrics && (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center">
                      <MemoryStick className="h-8 w-8 text-green-600" />
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-gray-900">Memória</h3>
                        <p className="text-2xl font-bold text-gray-900">{systemMetrics.memory.percent}%</p>
                        <p className="text-sm text-gray-500">
                          {systemMetrics.memory.used_gb}GB / {systemMetrics.memory.total_gb}GB
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Disco */}
              {systemMetrics && (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center">
                      <HardDrive className="h-8 w-8 text-purple-600" />
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-gray-900">Disco</h3>
                        <p className="text-2xl font-bold text-gray-900">{systemMetrics.disk.percent}%</p>
                        <p className="text-sm text-gray-500">
                          {systemMetrics.disk.used_gb}GB / {systemMetrics.disk.total_gb}GB
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Uptime */}
              {systemMetrics && (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center">
                      <Activity className="h-8 w-8 text-orange-600" />
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-gray-900">Uptime</h3>
                        <p className="text-2xl font-bold text-gray-900">{systemMetrics.uptime.hours.toFixed(1)}h</p>
                        <p className="text-sm text-gray-500">Sistema ativo</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Métricas da Aplicação */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* Banco de Dados */}
              {databaseMetrics && (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900 flex items-center">
                      <Database className="h-5 w-5 mr-2" />
                      Banco de Dados
                    </h3>
                  </div>
                  <div className="p-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Conexões Ativas</p>
                        <p className="text-2xl font-bold text-gray-900">{databaseMetrics.connections.active}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Total Conexões</p>
                        <p className="text-2xl font-bold text-gray-900">{databaseMetrics.connections.total}</p>
                      </div>
                      <div className="col-span-2">
                        <p className="text-sm text-gray-500">Tamanho do Banco</p>
                        <p className="text-lg font-bold text-gray-900">{databaseMetrics.database_size}</p>
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Registros por Tabela</h4>
                      <div className="space-y-2">
                        {Object.entries(databaseMetrics.tables).map(([table, count]) => (
                          <div key={table} className="flex justify-between">
                            <span className="text-sm text-gray-600">{table}</span>
                            <span className="text-sm font-medium text-gray-900">{count}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Atividade da Aplicação */}
              {applicationMetrics && (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900 flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2" />
                      Atividade da Aplicação
                    </h3>
                  </div>
                  <div className="p-6">
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-500">Total Usuários</p>
                        <p className="text-2xl font-bold text-gray-900">{applicationMetrics.totals.users}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Total Pacientes</p>
                        <p className="text-2xl font-bold text-gray-900">{applicationMetrics.totals.patients}</p>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Atividade (24h)</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Avaliações</span>
                          <span className="text-sm font-medium text-gray-900">{applicationMetrics.activity_24h.assessments}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Sessões Psicológicas</span>
                          <span className="text-sm font-medium text-gray-900">{applicationMetrics.activity_24h.psychology_sessions}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Relatórios Gerados</span>
                          <span className="text-sm font-medium text-gray-900">{applicationMetrics.activity_24h.reports_generated}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Verificações de Saúde Detalhadas */}
            {healthCheck && (
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Verificações de Saúde</h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(healthCheck.checks).map(([checkName, check]) => (
                      <div key={checkName} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center">
                          {getStatusIcon(check.status)}
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">{checkName}</p>
                            <p className="text-sm text-gray-500">{check.message}</p>
                          </div>
                        </div>
                        <span className={`text-sm font-medium ${getStatusColor(check.status)}`}>
                          {check.status.toUpperCase()}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Links de Administração */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
              <Link href="/admin/logs" className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
                <div className="flex items-center">
                  <FileText className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">Logs do Sistema</h3>
                    <p className="text-sm text-gray-500">Visualizar logs de aplicação e performance</p>
                  </div>
                </div>
              </Link>

              <Link href="/admin/users" className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">Gerenciar Usuários</h3>
                    <p className="text-sm text-gray-500">Administrar usuários e permissões</p>
                  </div>
                </div>
              </Link>

              <Link href="/settings/notifications" className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
                <div className="flex items-center">
                  <Bell className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">Configurações</h3>
                    <p className="text-sm text-gray-500">Configurar sistema e notificações</p>
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
