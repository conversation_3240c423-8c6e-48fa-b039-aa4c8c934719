"""
Schemas Pydantic para relatórios
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

class AutomaticReportBase(BaseModel):
    """Schema base para relatório automático"""
    data_geracao: datetime
    tipo: str = Field(..., pattern="^(fisico|mental|nutricional|integrado)$")
    data_inicio_analise: Optional[datetime] = None
    data_fim_analise: Optional[datetime] = None
    titulo: Optional[str] = Field(None, max_length=200)
    resumo_executivo: Optional[str] = None
    analise_detalhada: Optional[str] = None
    recomendacoes: Optional[str] = None
    conclusoes: Optional[str] = None
    dados_fonte: Optional[Dict[str, Any]] = None
    pdf_url: Optional[str] = None
    html_content: Optional[str] = None
    gerado_por_ia: bool = True
    modelo_ia_utilizado: Optional[str] = None
    versao_prompt: Optional[str] = None

class AutomaticReportCreate(AutomaticReportBase):
    """Schema para criação de relatório automático"""
    paciente_id: int

class AutomaticReportUpdate(BaseModel):
    """Schema para atualização de relatório automático"""
    titulo: Optional[str] = Field(None, max_length=200)
    resumo_executivo: Optional[str] = None
    analise_detalhada: Optional[str] = None
    recomendacoes: Optional[str] = None
    conclusoes: Optional[str] = None
    pdf_url: Optional[str] = None
    html_content: Optional[str] = None

class AutomaticReport(AutomaticReportBase):
    """Schema para resposta de relatório automático"""
    id: int
    paciente_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ReportGenerationRequest(BaseModel):
    """Schema para solicitação de geração de relatório"""
    paciente_id: int
    tipo: str = Field(..., pattern="^(fisico|mental|nutricional|integrado)$")
    data_inicio: Optional[datetime] = None
    data_fim: Optional[datetime] = None
    incluir_graficos: bool = True
    incluir_recomendacoes: bool = True
    formato_saida: str = Field(default="pdf", pattern="^(pdf|html|ambos)$")
    observacoes_adicionais: Optional[str] = None

class ReportTemplate(BaseModel):
    """Schema para template de relatório"""
    nome: str
    tipo: str
    descricao: Optional[str] = None
    template_html: str
    variaveis_necessarias: list
    ativo: bool = True

class ReportData(BaseModel):
    """Schema para dados do relatório"""
    paciente_info: Dict[str, Any]
    avaliacoes_fisicas: Optional[list] = None
    treinos: Optional[list] = None
    planos_nutricionais: Optional[list] = None
    sessoes_psicologicas: Optional[list] = None
    periodo_analise: Dict[str, datetime]
    metricas_calculadas: Optional[Dict[str, Any]] = None

class AIPromptTemplate(BaseModel):
    """Schema para template de prompt da IA"""
    nome: str
    tipo_relatorio: str
    prompt_sistema: str
    prompt_usuario: str
    parametros: Optional[Dict[str, Any]] = None
    versao: str = "1.0"
    ativo: bool = True

class ReportMetrics(BaseModel):
    """Schema para métricas do relatório"""
    # Métricas físicas
    variacao_peso: Optional[float] = None
    variacao_gordura: Optional[float] = None
    variacao_massa_magra: Optional[float] = None

    # Métricas de treino
    total_treinos: Optional[int] = None
    aderencia_treino: Optional[float] = None
    progressao_cargas: Optional[float] = None

    # Métricas nutricionais
    aderencia_dieta: Optional[float] = None
    variacao_calorica: Optional[float] = None

    # Métricas psicológicas
    evolucao_humor: Optional[float] = None
    evolucao_ansiedade: Optional[float] = None
    numero_sessoes: Optional[int] = None

    # Métricas gerais
    periodo_dias: Optional[int] = None
    consistencia_dados: Optional[float] = None
