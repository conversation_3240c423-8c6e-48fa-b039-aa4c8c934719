import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { Plus, Search, Filter, Users } from 'lucide-react'
import Layout from './Layout'

interface Patient {
  id: number
  nome: string
  email: string
  telefone: string
  data_nascimento: string
  status: string
  created_at: string
  [key: string]: any // Para campos específicos de cada área
}

interface PatientsPageProps {
  area: 'personal' | 'nutrition' | 'psychology'
  title: string
  icon: React.ComponentType<any>
  color: string
  patients: Patient[]
  loading: boolean
  searchTerm: string
  onSearchChange: (term: string) => void
  customColumns?: Array<{
    key: string
    label: string
    render?: (patient: Patient) => React.ReactNode
  }>
}

export default function PatientsPage({
  area,
  title,
  icon: Icon,
  color,
  patients,
  loading,
  searchTerm,
  onSearchChange,
  customColumns = []
}: PatientsPageProps) {
  const router = useRouter()

  const filteredPatients = patients.filter(patient =>
    patient.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const defaultColumns = [
    {
      key: 'patient',
      label: 'Paciente',
      render: (patient: Patient) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{patient.nome}</div>
          <div className="text-sm text-gray-500">{patient.email}</div>
        </div>
      )
    },
    {
      key: 'contact',
      label: 'Contato',
      render: (patient: Patient) => (
        <span className="text-sm text-gray-900">{patient.telefone}</span>
      )
    },
    ...customColumns,
    {
      key: 'status',
      label: 'Status',
      render: (patient: Patient) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-${color}-100 text-${color}-800`}>
          {patient.status}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Ações',
      render: (patient: Patient) => (
        <div className="text-sm font-medium space-x-4">
          <Link
            href={`/${area}/patients/${patient.id}`}
            className={`text-${color}-600 hover:text-${color}-900`}
          >
            Ver
          </Link>
          <Link
            href={`/${area}/patients/${patient.id}/edit`}
            className="text-blue-600 hover:text-blue-900"
          >
            Editar
          </Link>
        </div>
      )
    }
  ]

  return (
    <Layout title={title}>
      <Head>
        <title>{title} | Hypatium</title>
      </Head>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Page Header */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Icon className={`h-6 w-6 text-${color}-600 mr-2`} />
                {title}
              </h1>
              <p className="text-gray-600">Gerencie seus pacientes e acompanhamentos</p>
            </div>
            <Link
              href={`/${area}/patients/new`}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-${color}-600 hover:bg-${color}-700`}
            >
              <Plus className="h-4 w-4 mr-2" />
              Novo Paciente
            </Link>
          </div>

          {/* Search and Filters */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Buscar pacientes..."
                      value={searchTerm}
                      onChange={(e) => onSearchChange(e.target.value)}
                      className={`w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-${color}-500 focus:border-${color}-500`}
                    />
                  </div>
                </div>
                <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                  <Filter className="h-4 w-4 mr-2" />
                  Filtros
                </button>
              </div>
            </div>
          </div>

          {/* Patients List */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              {loading ? (
                <div className="text-center py-8">
                  <div className={`animate-spin rounded-full h-8 w-8 border-b-2 border-${color}-600 mx-auto`}></div>
                  <p className="mt-2 text-gray-600">Carregando pacientes...</p>
                </div>
              ) : filteredPatients.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum paciente encontrado</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm ? 'Tente ajustar sua busca' : 'Comece adicionando seu primeiro paciente'}
                  </p>
                  <Link
                    href={`/${area}/patients/new`}
                    className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-${color}-600 hover:bg-${color}-700`}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Adicionar Paciente
                  </Link>
                </div>
              ) : (
                <div className="overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        {defaultColumns.map((column) => (
                          <th
                            key={column.key}
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            {column.label}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredPatients.map((patient) => (
                        <tr key={patient.id} className="hover:bg-gray-50">
                          {defaultColumns.map((column) => (
                            <td key={column.key} className="px-6 py-4 whitespace-nowrap">
                              {column.render ? column.render(patient) : patient[column.key]}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
