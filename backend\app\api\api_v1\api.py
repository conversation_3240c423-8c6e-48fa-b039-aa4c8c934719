"""
Router principal da API v1
"""

from fastapi import APIRouter
from app.api.api_v1.endpoints import auth, users, patients, assessments, workouts, nutrition, psychology, reports, upload, pdf, notifications, monitoring

api_router = APIRouter()

# Incluir todas as rotas
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(patients.router, prefix="/patients", tags=["patients"])
api_router.include_router(assessments.router, prefix="/assessments", tags=["assessments"])
api_router.include_router(workouts.router, prefix="/workouts", tags=["workouts"])
api_router.include_router(nutrition.router, prefix="/nutrition", tags=["nutrition"])
api_router.include_router(psychology.router, prefix="/psychology", tags=["psychology"])
api_router.include_router(reports.router, prefix="/reports", tags=["reports"])
api_router.include_router(upload.router, prefix="/upload", tags=["upload"])
api_router.include_router(pdf.router, prefix="/pdf", tags=["pdf"])
api_router.include_router(notifications.router, prefix="/notifications", tags=["notifications"])
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["monitoring"])
