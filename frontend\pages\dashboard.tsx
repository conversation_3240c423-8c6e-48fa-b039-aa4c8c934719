import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import Layout from '../components/Layout'
import {
  Activity,
  Users,
  Brain,
  Utensils,
  FileText,
  Plus,
  TrendingUp,
  Calendar,
  Bell,
  Settings,
  LogOut
} from 'lucide-react'

interface User {
  id: number
  nome: string
  email: string
  tipo: string
}

interface DashboardStats {
  total_pacientes: number
  avaliacoes_mes: number
  treinos_ativos: number
  sessoes_mes: number
}

export default function Dashboard() {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    fetchUserData(token)
  }, [router])

  const fetchUserData = async (token: string) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/users/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        throw new Error('Token inválido')
      }

      const userData = await response.json()
      setUser(userData)

      // Simular stats por enquanto
      setStats({
        total_pacientes: 12,
        avaliacoes_mes: 8,
        treinos_ativos: 15,
        sessoes_mes: 6
      })
    } catch (error) {
      console.error('Erro ao buscar dados do usuário:', error)
      localStorage.removeItem('token')
      router.push('/login')
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('token')
    router.push('/')
  }

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Bom dia'
    if (hour < 18) return 'Boa tarde'
    return 'Boa noite'
  }

  const getUserTypeLabel = (tipo: string) => {
    const labels = {
      personal: 'Personal Trainer',
      nutricionista: 'Nutricionista',
      psicologo: 'Psicólogo',
      admin: 'Administrador'
    }
    return labels[tipo as keyof typeof labels] || tipo
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <Layout title="Dashboard">
      <Head>
        <title>Dashboard - Hypatium</title>
        <meta name="description" content="Dashboard da plataforma Hypatium" />
      </Head>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {/* Welcome Section */}
          <div className="px-4 py-6 sm:px-0">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">
                {getGreeting()}, {user.nome}!
              </h1>
              <p className="mt-1 text-sm text-gray-600">
                {getUserTypeLabel(user.tipo)} • {user.email}
              </p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Total de Pacientes
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {stats?.total_pacientes || 0}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <TrendingUp className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Avaliações este mês
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {stats?.avaliacoes_mes || 0}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Activity className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Treinos Ativos
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {stats?.treinos_ativos || 0}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Calendar className="h-6 w-6 text-orange-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Sessões este mês
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {stats?.sessoes_mes || 0}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Personal Trainer Actions */}
              {(user.tipo === 'personal' || user.tipo === 'admin') && (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <Users className="h-6 w-6 text-blue-600" />
                      <h3 className="ml-2 text-lg font-medium text-gray-900">
                        Personal Trainer
                      </h3>
                    </div>
                    <div className="space-y-3">
                      <Link
                        href="/personal/patients/new"
                        className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Novo Paciente
                      </Link>
                      <Link
                        href="/personal/patients"
                        className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Ver Pacientes
                      </Link>
                    </div>
                  </div>
                </div>
              )}

              {/* Nutricionista Actions */}
              {(user.tipo === 'nutricionista' || user.tipo === 'admin') && (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <Utensils className="h-6 w-6 text-green-600" />
                      <h3 className="ml-2 text-lg font-medium text-gray-900">
                        Nutrição
                      </h3>
                    </div>
                    <div className="space-y-3">
                      <Link
                        href="/nutrition/patients/new"
                        className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Novo Paciente
                      </Link>
                      <Link
                        href="/nutrition/patients"
                        className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Ver Pacientes
                      </Link>
                    </div>
                  </div>
                </div>
              )}

              {/* Psicólogo Actions */}
              {(user.tipo === 'psicologo' || user.tipo === 'admin') && (
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <Brain className="h-6 w-6 text-purple-600" />
                      <h3 className="ml-2 text-lg font-medium text-gray-900">
                        Psicologia
                      </h3>
                    </div>
                    <div className="space-y-3">
                      <Link
                        href="/psychology/patients/new"
                        className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Novo Paciente
                      </Link>
                      <Link
                        href="/psychology/patients"
                        className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Ver Pacientes
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
      </div>
    </Layout>
  )
}
