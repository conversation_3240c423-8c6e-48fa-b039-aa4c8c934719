import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
export declare class RelatorioService {
    private prisma;
    private configService;
    private openai;
    constructor(prisma: PrismaService, configService: ConfigService);
    gerarRelatorio(avaliacaoComposicaoId: string, personalId: string): Promise<any>;
    private gerarAnaliseIA;
    private construirPromptAnalise;
    private gerarAnaliseBasica;
    private gerarDadosGraficos;
    findRelatoriosByAluno(alunoId: string, personalId?: string): Promise<any>;
    findRelatorioById(id: string, personalId?: string): Promise<any>;
    gerarRelatorioVolumeLoad(avaliacaoComposicaoId: string, personalId: string): Promise<{
        aluno: any;
        analise: string;
        dadosVolumeLoad: unknown[];
        graficos: {
            vlPorExercicio: {
                exercicio: string;
                vlTotal: any;
            }[];
            progressaoPorSemana: any;
        };
    }>;
    private processarDadosVolumeLoad;
    private gerarAnaliseVolumeLoadIA;
    private construirPromptVolumeLoad;
    private gerarAnaliseVolumeLoadBasica;
    private gerarGraficosVolumeLoad;
}
