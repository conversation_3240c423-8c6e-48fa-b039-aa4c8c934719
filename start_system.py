#!/usr/bin/env python3
"""
Script para inicializar o sistema Hypatium localmente
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def print_header():
    print("🏥" + "="*60)
    print("   HYPATIUM - Sistema de Saúde Integrado")
    print("   Inicializando sistema local...")
    print("="*62)

def check_requirements():
    """Verificar se os requisitos estão instalados"""
    print("\n🔍 Verificando requisitos...")
    
    # Verificar Python
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ é necessário")
        return False
    print("✅ Python:", sys.version.split()[0])
    
    # Verificar Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Node.js:", result.stdout.strip())
        else:
            print("❌ Node.js não encontrado")
            return False
    except FileNotFoundError:
        print("❌ Node.js não encontrado")
        return False
    
    return True

def setup_backend():
    """Configurar e iniciar o backend"""
    print("\n🐍 Configurando Backend...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Diretório backend não encontrado")
        return False
    
    # Criar diretórios necessários
    os.makedirs("backend/logs", exist_ok=True)
    os.makedirs("backend/uploads", exist_ok=True)
    os.makedirs("backend/static/pdfs", exist_ok=True)
    
    # Instalar dependências
    print("📦 Instalando dependências do backend...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"
        ], check=True, cwd=".")
        print("✅ Dependências do backend instaladas")
    except subprocess.CalledProcessError:
        print("❌ Erro ao instalar dependências do backend")
        return False
    
    return True

def setup_frontend():
    """Configurar o frontend"""
    print("\n⚛️ Configurando Frontend...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ Diretório frontend não encontrado")
        return False
    
    # Instalar dependências
    print("📦 Instalando dependências do frontend...")
    try:
        subprocess.run(["npm", "install"], check=True, cwd="frontend")
        print("✅ Dependências do frontend instaladas")
    except subprocess.CalledProcessError:
        print("❌ Erro ao instalar dependências do frontend")
        return False
    
    return True

def create_env_file():
    """Criar arquivo .env se não existir"""
    print("\n⚙️ Configurando variáveis de ambiente...")
    
    env_file = Path("backend/.env")
    if env_file.exists():
        print("✅ Arquivo .env já existe")
        return True
    
    env_content = """# Hypatium Local Environment
DATABASE_URL=sqlite:///./hypatium.db
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Hypatium
VERSION=1.0.0

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000"]

# Optional - Configure these for full functionality
# OPENAI_API_KEY=your-openai-api-key-here
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password

# Environment
ENVIRONMENT=development
DEBUG=true
TESTING=false
"""
    
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print("✅ Arquivo .env criado")
    return True

def start_backend():
    """Iniciar o backend"""
    print("\n🚀 Iniciando Backend...")
    
    try:
        # Iniciar o backend em background
        backend_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"
        ], cwd="backend")
        
        # Aguardar o backend ficar pronto
        print("⏳ Aguardando backend ficar pronto...")
        for i in range(30):  # 30 segundos timeout
            try:
                response = requests.get("http://localhost:8000/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Backend iniciado com sucesso!")
                    print("📡 API disponível em: http://localhost:8000")
                    print("📚 Documentação em: http://localhost:8000/docs")
                    return backend_process
            except:
                pass
            time.sleep(1)
        
        print("❌ Backend não respondeu em 30 segundos")
        backend_process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ Erro ao iniciar backend: {str(e)}")
        return None

def create_initial_data():
    """Criar dados iniciais"""
    print("\n👤 Criando usuário administrador...")
    
    try:
        # Criar usuário admin
        admin_data = {
            "email": "<EMAIL>",
            "password": "admin123",
            "nome": "Administrador",
            "tipo": "admin"
        }
        
        response = requests.post("http://localhost:8000/api/v1/auth/register", json=admin_data)
        if response.status_code == 200:
            print("✅ Usuário administrador criado!")
        else:
            print("⚠️ Usuário administrador pode já existir")
        
        # Criar usuário personal trainer
        personal_data = {
            "email": "<EMAIL>",
            "password": "personal123",
            "nome": "Personal Trainer Demo",
            "tipo": "personal"
        }
        
        response = requests.post("http://localhost:8000/api/v1/auth/register", json=personal_data)
        if response.status_code == 200:
            print("✅ Usuário Personal Trainer criado!")
        
        # Criar usuário nutricionista
        nutri_data = {
            "email": "<EMAIL>",
            "password": "nutri123",
            "nome": "Nutricionista Demo",
            "tipo": "nutricionista"
        }
        
        response = requests.post("http://localhost:8000/api/v1/auth/register", json=nutri_data)
        if response.status_code == 200:
            print("✅ Usuário Nutricionista criado!")
        
        # Criar usuário psicólogo
        psico_data = {
            "email": "<EMAIL>",
            "password": "psico123",
            "nome": "Psicólogo Demo",
            "tipo": "psicologo"
        }
        
        response = requests.post("http://localhost:8000/api/v1/auth/register", json=psico_data)
        if response.status_code == 200:
            print("✅ Usuário Psicólogo criado!")
            
    except Exception as e:
        print(f"⚠️ Erro ao criar usuários: {str(e)}")

def start_frontend():
    """Iniciar o frontend"""
    print("\n🌐 Iniciando Frontend...")
    
    try:
        # Criar arquivo .env.local para o frontend
        frontend_env = Path("frontend/.env.local")
        if not frontend_env.exists():
            with open(frontend_env, 'w') as f:
                f.write("NEXT_PUBLIC_API_URL=http://localhost:8000\n")
        
        # Iniciar o frontend
        frontend_process = subprocess.Popen([
            "npm", "run", "dev"
        ], cwd="frontend")
        
        # Aguardar o frontend ficar pronto
        print("⏳ Aguardando frontend ficar pronto...")
        for i in range(60):  # 60 segundos timeout
            try:
                response = requests.get("http://localhost:3000", timeout=2)
                if response.status_code == 200:
                    print("✅ Frontend iniciado com sucesso!")
                    print("🌐 Aplicação disponível em: http://localhost:3000")
                    return frontend_process
            except:
                pass
            time.sleep(1)
        
        print("❌ Frontend não respondeu em 60 segundos")
        return frontend_process  # Retornar mesmo assim, pode estar carregando
        
    except Exception as e:
        print(f"❌ Erro ao iniciar frontend: {str(e)}")
        return None

def print_access_info():
    """Imprimir informações de acesso"""
    print("\n" + "="*62)
    print("🎉 SISTEMA HYPATIUM INICIADO COM SUCESSO!")
    print("="*62)
    print("\n🌐 ACESSO AO SISTEMA:")
    print("   URL: http://localhost:3000")
    print("\n👤 USUÁRIOS DE TESTE:")
    print("   📧 Administrador:")
    print("      Email: <EMAIL>")
    print("      Senha: admin123")
    print("\n   💪 Personal Trainer:")
    print("      Email: <EMAIL>")
    print("      Senha: personal123")
    print("\n   🥗 Nutricionista:")
    print("      Email: <EMAIL>")
    print("      Senha: nutri123")
    print("\n   🧠 Psicólogo:")
    print("      Email: <EMAIL>")
    print("      Senha: psico123")
    print("\n📡 API E DOCUMENTAÇÃO:")
    print("   API: http://localhost:8000")
    print("   Docs: http://localhost:8000/docs")
    print("   Admin: http://localhost:3000/admin/dashboard")
    print("\n⚠️ PARA PARAR O SISTEMA:")
    print("   Pressione Ctrl+C neste terminal")
    print("="*62)

def main():
    """Função principal"""
    print_header()
    
    # Verificar requisitos
    if not check_requirements():
        print("\n❌ Requisitos não atendidos. Instale Python 3.8+ e Node.js")
        return False
    
    # Configurar ambiente
    if not create_env_file():
        return False
    
    # Configurar backend
    if not setup_backend():
        return False
    
    # Configurar frontend
    if not setup_frontend():
        return False
    
    # Iniciar backend
    backend_process = start_backend()
    if not backend_process:
        return False
    
    # Criar dados iniciais
    create_initial_data()
    
    # Iniciar frontend
    frontend_process = start_frontend()
    if not frontend_process:
        backend_process.terminate()
        return False
    
    # Imprimir informações de acesso
    print_access_info()
    
    try:
        # Manter os processos rodando
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Parando sistema...")
        if backend_process:
            backend_process.terminate()
        if frontend_process:
            frontend_process.terminate()
        print("✅ Sistema parado com sucesso!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
