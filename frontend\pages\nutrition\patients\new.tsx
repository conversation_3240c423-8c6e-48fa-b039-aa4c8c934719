import { useState } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { ArrowLeft, Save, Utensils } from 'lucide-react'
import Layout from '../../../components/Layout'

interface PatientForm {
  nome: string
  email: string
  telefone: string
  data_nascimento: string
  sexo: string
  altura: string
  peso: string
  objetivo: string
  nivel_atividade: string
  restricoes_alimentares: string
  observacoes: string
}

export default function NewNutritionPatient() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<PatientForm>({
    nome: '',
    email: '',
    telefone: '',
    data_nascimento: '',
    sexo: '',
    altura: '',
    peso: '',
    objetivo: '',
    nivel_atividade: '',
    restricoes_alimentares: '',
    observacoes: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Aqui você faria a chamada para a API
      console.log('Dados do paciente:', formData)

      // Simular salvamento
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Redirecionar para lista de pacientes
      router.push('/nutrition/patients')
    } catch (error) {
      console.error('Erro ao salvar paciente:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <Layout title="Novo Paciente - Nutrição">
      <Head>
        <title>Novo Paciente - Nutrição | Hypatium</title>
      </Head>

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Page Header */}
            <div className="mb-6">
              <div className="flex items-center mb-4">
                <Link
                  href="/nutrition/patients"
                  className="mr-4 p-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    <Utensils className="h-6 w-6 text-green-600 mr-2" />
                    Novo Paciente - Nutrição
                  </h1>
                  <p className="text-gray-600">Adicione um novo paciente para acompanhamento nutricional</p>
                </div>
              </div>
            </div>

            {/* Form */}
            <div className="bg-white shadow rounded-lg">
              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                {/* Dados Pessoais */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Dados Pessoais</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Nome Completo *
                      </label>
                      <input
                        type="text"
                        name="nome"
                        required
                        value={formData.nome}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email *
                      </label>
                      <input
                        type="email"
                        name="email"
                        required
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Telefone
                      </label>
                      <input
                        type="tel"
                        name="telefone"
                        value={formData.telefone}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Data de Nascimento *
                      </label>
                      <input
                        type="date"
                        name="data_nascimento"
                        required
                        value={formData.data_nascimento}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Sexo *
                      </label>
                      <select
                        name="sexo"
                        required
                        value={formData.sexo}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      >
                        <option value="">Selecione</option>
                        <option value="masculino">Masculino</option>
                        <option value="feminino">Feminino</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Dados Físicos */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Dados Físicos</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Altura (cm) *
                      </label>
                      <input
                        type="number"
                        name="altura"
                        required
                        min="100"
                        max="250"
                        value={formData.altura}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Peso (kg) *
                      </label>
                      <input
                        type="number"
                        name="peso"
                        required
                        min="30"
                        max="300"
                        step="0.1"
                        value={formData.peso}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Objetivos e Atividade */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Objetivos e Atividade</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Objetivo *
                      </label>
                      <select
                        name="objetivo"
                        required
                        value={formData.objetivo}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      >
                        <option value="">Selecione</option>
                        <option value="perda_peso">Perda de Peso</option>
                        <option value="ganho_peso">Ganho de Peso</option>
                        <option value="manutencao">Manutenção</option>
                        <option value="ganho_massa">Ganho de Massa Muscular</option>
                        <option value="performance">Melhora de Performance</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Nível de Atividade *
                      </label>
                      <select
                        name="nivel_atividade"
                        required
                        value={formData.nivel_atividade}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      >
                        <option value="">Selecione</option>
                        <option value="sedentario">Sedentário</option>
                        <option value="leve">Atividade Leve</option>
                        <option value="moderado">Atividade Moderada</option>
                        <option value="intenso">Atividade Intensa</option>
                        <option value="muito_intenso">Muito Intenso</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Restrições e Observações */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Informações Adicionais</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Restrições Alimentares
                      </label>
                      <textarea
                        name="restricoes_alimentares"
                        rows={3}
                        value={formData.restricoes_alimentares}
                        onChange={handleChange}
                        placeholder="Ex: Intolerância à lactose, alergia a amendoim..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Observações
                      </label>
                      <textarea
                        name="observacoes"
                        rows={3}
                        value={formData.observacoes}
                        onChange={handleChange}
                        placeholder="Informações adicionais sobre o paciente..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex justify-end space-x-4 pt-6 border-t">
                  <Link
                    href="/nutrition/patients"
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Cancelar
                  </Link>
                  <button
                    type="submit"
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                  >
                    {loading ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    {loading ? 'Salvando...' : 'Salvar Paciente'}
                  </button>
                </div>
              </form>
            </div>
          </div>
      </div>
    </Layout>
  )
}
