"""
Serviço para upload e gerenciamento de arquivos
"""

import os
import uuid
import shutil
from typing import Optional, List, Dict, Any
from pathlib import Path
from fastapi import UploadFile
import mimetypes

class UploadService:
    def __init__(self, upload_dir: str = "static/uploads"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        
        # Configurações de upload
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.allowed_extensions = {
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
            'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv'],
            'audio': ['.mp3', '.wav', '.m4a', '.aac', '.ogg'],
            'document': ['.pdf', '.doc', '.docx', '.txt', '.rtf']
        }
        
        # Diretórios por tipo
        self.type_dirs = {
            'image': self.upload_dir / 'images',
            'video': self.upload_dir / 'videos', 
            'audio': self.upload_dir / 'audio',
            'document': self.upload_dir / 'documents',
            'other': self.upload_dir / 'other'
        }
        
        # Criar diretórios se não existirem
        for dir_path in self.type_dirs.values():
            dir_path.mkdir(parents=True, exist_ok=True)

    def get_file_type(self, filename: str) -> str:
        """Determinar tipo do arquivo baseado na extensão"""
        ext = Path(filename).suffix.lower()
        
        for file_type, extensions in self.allowed_extensions.items():
            if ext in extensions:
                return file_type
        
        return 'other'

    def validate_file(self, file: UploadFile) -> Dict[str, Any]:
        """Validar arquivo antes do upload"""
        errors = []
        warnings = []
        
        # Verificar tamanho do arquivo
        if hasattr(file, 'size') and file.size and file.size > self.max_file_size:
            errors.append(f"Arquivo muito grande. Máximo permitido: {self.max_file_size / (1024*1024):.1f}MB")
        
        # Verificar extensão
        if file.filename:
            ext = Path(file.filename).suffix.lower()
            file_type = self.get_file_type(file.filename)
            
            if file_type == 'other' and ext:
                warnings.append(f"Tipo de arquivo '{ext}' pode não ser suportado")
        
        # Verificar nome do arquivo
        if not file.filename or file.filename.strip() == '':
            errors.append("Nome do arquivo é obrigatório")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'file_type': self.get_file_type(file.filename) if file.filename else 'other'
        }

    async def upload_file(
        self, 
        file: UploadFile, 
        subfolder: Optional[str] = None,
        custom_filename: Optional[str] = None
    ) -> Dict[str, Any]:
        """Upload de arquivo"""
        
        # Validar arquivo
        validation = self.validate_file(file)
        if not validation['valid']:
            return {
                'success': False,
                'errors': validation['errors'],
                'warnings': validation.get('warnings', [])
            }
        
        try:
            # Determinar diretório de destino
            file_type = validation['file_type']
            target_dir = self.type_dirs[file_type]
            
            if subfolder:
                target_dir = target_dir / subfolder
                target_dir.mkdir(parents=True, exist_ok=True)
            
            # Gerar nome único do arquivo
            if custom_filename:
                filename = custom_filename
            else:
                # Manter extensão original
                original_ext = Path(file.filename).suffix if file.filename else ''
                unique_id = str(uuid.uuid4())
                filename = f"{unique_id}{original_ext}"
            
            file_path = target_dir / filename
            
            # Salvar arquivo
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # Obter informações do arquivo
            file_info = self.get_file_info(file_path)
            
            # URL relativa para acesso
            relative_path = file_path.relative_to(self.upload_dir)
            file_url = f"/static/uploads/{relative_path.as_posix()}"
            
            return {
                'success': True,
                'file_path': str(file_path),
                'file_url': file_url,
                'filename': filename,
                'original_filename': file.filename,
                'file_type': file_type,
                'file_info': file_info,
                'warnings': validation.get('warnings', [])
            }
            
        except Exception as e:
            return {
                'success': False,
                'errors': [f"Erro ao salvar arquivo: {str(e)}"]
            }

    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """Obter informações detalhadas do arquivo"""
        if not file_path.exists():
            return {}
        
        stat = file_path.stat()
        mime_type, _ = mimetypes.guess_type(str(file_path))
        
        return {
            'size': stat.st_size,
            'size_mb': round(stat.st_size / (1024 * 1024), 2),
            'created_at': stat.st_ctime,
            'modified_at': stat.st_mtime,
            'mime_type': mime_type,
            'extension': file_path.suffix.lower()
        }

    def delete_file(self, file_path: str) -> bool:
        """Deletar arquivo"""
        try:
            path = Path(file_path)
            if path.exists() and path.is_file():
                path.unlink()
                return True
            return False
        except Exception:
            return False

    def list_files(
        self, 
        file_type: Optional[str] = None, 
        subfolder: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Listar arquivos"""
        
        if file_type and file_type in self.type_dirs:
            search_dir = self.type_dirs[file_type]
        else:
            search_dir = self.upload_dir
        
        if subfolder:
            search_dir = search_dir / subfolder
        
        if not search_dir.exists():
            return []
        
        files = []
        count = 0
        
        for file_path in search_dir.rglob('*'):
            if file_path.is_file() and count < limit:
                relative_path = file_path.relative_to(self.upload_dir)
                file_url = f"/static/uploads/{relative_path.as_posix()}"
                
                file_info = self.get_file_info(file_path)
                detected_type = self.get_file_type(file_path.name)
                
                files.append({
                    'filename': file_path.name,
                    'file_path': str(file_path),
                    'file_url': file_url,
                    'file_type': detected_type,
                    'file_info': file_info
                })
                count += 1
        
        # Ordenar por data de modificação (mais recente primeiro)
        files.sort(key=lambda x: x['file_info'].get('modified_at', 0), reverse=True)
        
        return files

    def get_upload_stats(self) -> Dict[str, Any]:
        """Obter estatísticas de upload"""
        stats = {
            'total_files': 0,
            'total_size': 0,
            'by_type': {}
        }
        
        for file_type, type_dir in self.type_dirs.items():
            if not type_dir.exists():
                continue
                
            type_files = list(type_dir.rglob('*'))
            type_files = [f for f in type_files if f.is_file()]
            
            type_size = sum(f.stat().st_size for f in type_files)
            
            stats['by_type'][file_type] = {
                'count': len(type_files),
                'size': type_size,
                'size_mb': round(type_size / (1024 * 1024), 2)
            }
            
            stats['total_files'] += len(type_files)
            stats['total_size'] += type_size
        
        stats['total_size_mb'] = round(stats['total_size'] / (1024 * 1024), 2)
        
        return stats

    def cleanup_old_files(self, days_old: int = 30) -> Dict[str, Any]:
        """Limpar arquivos antigos"""
        import time
        
        cutoff_time = time.time() - (days_old * 24 * 60 * 60)
        deleted_files = []
        errors = []
        
        for file_path in self.upload_dir.rglob('*'):
            if file_path.is_file():
                try:
                    if file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        deleted_files.append(str(file_path))
                except Exception as e:
                    errors.append(f"Erro ao deletar {file_path}: {str(e)}")
        
        return {
            'deleted_count': len(deleted_files),
            'deleted_files': deleted_files,
            'errors': errors
        }
