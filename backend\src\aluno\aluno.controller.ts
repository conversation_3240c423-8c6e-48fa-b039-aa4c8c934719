import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AlunoService } from './aluno.service';
import { CreateAlunoDto } from './dto/create-aluno.dto';
import { UpdateAlunoDto } from './dto/update-aluno.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('alunos')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('alunos')
export class AlunoController {
  constructor(private readonly alunoService: AlunoService) {}

  @ApiOperation({ summary: 'Criar novo aluno (apenas Personal Trainer)' })
  @ApiResponse({ status: 201, description: 'Aluno criado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Roles('personal')
  @Post()
  create(@Body() createAlunoDto: CreateAlunoDto, @Request() req) {
    return this.alunoService.create(createAlunoDto, req.user.id);
  }

  @ApiOperation({ summary: 'Listar alunos do Personal Trainer' })
  @ApiResponse({ status: 200, description: 'Lista de alunos encontrada' })
  @Roles('personal')
  @Get()
  findAllByPersonal(@Request() req) {
    return this.alunoService.findAllByPersonal(req.user.id);
  }

  @ApiOperation({ summary: 'Buscar aluno por ID' })
  @ApiResponse({ status: 200, description: 'Aluno encontrado' })
  @ApiResponse({ status: 404, description: 'Aluno não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
    return this.alunoService.findOne(id, personalId);
  }

  @ApiOperation({ summary: 'Atualizar dados do aluno' })
  @ApiResponse({ status: 200, description: 'Aluno atualizado com sucesso' })
  @ApiResponse({ status: 404, description: 'Aluno não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateAlunoDto: UpdateAlunoDto, @Request() req) {
    const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
    return this.alunoService.update(id, updateAlunoDto, personalId);
  }

  @ApiOperation({ summary: 'Remover aluno (apenas Personal Trainer)' })
  @ApiResponse({ status: 200, description: 'Aluno removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Aluno não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Roles('personal')
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.alunoService.remove(id, req.user.id);
  }

  @ApiOperation({ summary: 'Buscar estatísticas do aluno' })
  @ApiResponse({ status: 200, description: 'Estatísticas encontradas' })
  @ApiResponse({ status: 404, description: 'Aluno não encontrado' })
  @Get(':id/stats')
  getStats(@Param('id') id: string, @Request() req) {
    const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
    return this.alunoService.getAlunoStats(id, personalId);
  }
}
