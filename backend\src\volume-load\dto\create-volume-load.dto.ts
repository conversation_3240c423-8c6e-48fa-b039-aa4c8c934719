import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CreateVolumeLoadDto {
  @ApiProperty({
    description: 'ID da avaliação de composição',
    example: 'clp123abc456',
  })
  @IsString()
  @IsNotEmpty()
  avaliacaoComposicaoId: string;

  @ApiProperty({
    description: 'Número do exercício (1-15)',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  @Max(15)
  @Transform(({ value }) => parseInt(value))
  exercicio: number;

  @ApiProperty({
    description: 'Número da semana (1-8)',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  @Max(8)
  @Transform(({ value }) => parseInt(value))
  semana: number;

  // Série 1
  @ApiProperty({
    description: 'Repetições da série 1',
    example: 12,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  serie1Repeticoes?: number;

  @ApiProperty({
    description: 'Carga da série 1 em kg',
    example: 50.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  serie1Carga?: number;

  // Série 2
  @ApiProperty({
    description: 'Repetições da série 2',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  serie2Repeticoes?: number;

  @ApiProperty({
    description: 'Carga da série 2 em kg',
    example: 52.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  serie2Carga?: number;

  // Série 3
  @ApiProperty({
    description: 'Repetições da série 3',
    example: 8,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  serie3Repeticoes?: number;

  @ApiProperty({
    description: 'Carga da série 3 em kg',
    example: 55.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  serie3Carga?: number;

  // Série 4
  @ApiProperty({
    description: 'Repetições da série 4',
    example: 8,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  serie4Repeticoes?: number;

  @ApiProperty({
    description: 'Carga da série 4 em kg',
    example: 55.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  serie4Carga?: number;

  // Série 5
  @ApiProperty({
    description: 'Repetições da série 5',
    example: 6,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  serie5Repeticoes?: number;

  @ApiProperty({
    description: 'Carga da série 5 em kg',
    example: 57.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  serie5Carga?: number;

  // Série 6
  @ApiProperty({
    description: 'Repetições da série 6',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  serie6Repeticoes?: number;

  @ApiProperty({
    description: 'Carga da série 6 em kg',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  serie6Carga?: number;

  // Série 7
  @ApiProperty({
    description: 'Repetições da série 7',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  serie7Repeticoes?: number;

  @ApiProperty({
    description: 'Carga da série 7 em kg',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  serie7Carga?: number;

  // Série 8
  @ApiProperty({
    description: 'Repetições da série 8',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  serie8Repeticoes?: number;

  @ApiProperty({
    description: 'Carga da série 8 em kg',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  serie8Carga?: number;

  // Série 9
  @ApiProperty({
    description: 'Repetições da série 9',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  serie9Repeticoes?: number;

  @ApiProperty({
    description: 'Carga da série 9 em kg',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  serie9Carga?: number;

  // Série 10
  @ApiProperty({
    description: 'Repetições da série 10',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  serie10Repeticoes?: number;

  @ApiProperty({
    description: 'Carga da série 10 em kg',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  serie10Carga?: number;
}
