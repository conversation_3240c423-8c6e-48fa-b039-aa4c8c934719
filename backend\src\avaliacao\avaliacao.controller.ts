import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  UseGuards,
  Request
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AvaliacaoService } from './avaliacao.service';
import { CreateAvaliacaoComposicaoDto } from './dto/create-avaliacao-composicao.dto';
import { CreateAvaliacaoEnergeticaDto } from './dto/create-avaliacao-energetica.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('avaliacoes')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('avaliacoes')
export class AvaliacaoController {
  constructor(private readonly avaliacaoService: AvaliacaoService) {}

  @ApiOperation({ summary: 'Criar avaliação de composição corporal (apenas Personal Trainer)' })
  @ApiResponse({ status: 201, description: 'Avaliação criada com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Roles('personal')
  @Post('composicao')
  createComposicao(@Body() createAvaliacaoDto: CreateAvaliacaoComposicaoDto, @Request() req) {
    return this.avaliacaoService.createAvaliacaoComposicao(createAvaliacaoDto, req.user.id);
  }

  @ApiOperation({ summary: 'Criar avaliação energética (apenas Personal Trainer)' })
  @ApiResponse({ status: 201, description: 'Avaliação energética criada com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Roles('personal')
  @Post('composicao/:id/energetica')
  createEnergetica(
    @Param('id') avaliacaoComposicaoId: string,
    @Body() createAvaliacaoEnergeticaDto: CreateAvaliacaoEnergeticaDto,
    @Request() req
  ) {
    return this.avaliacaoService.createAvaliacaoEnergetica(
      avaliacaoComposicaoId,
      createAvaliacaoEnergeticaDto,
      req.user.id
    );
  }

  @ApiOperation({ summary: 'Buscar avaliações por aluno' })
  @ApiResponse({ status: 200, description: 'Avaliações encontradas' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Get('aluno/:alunoId')
  findByAluno(@Param('alunoId') alunoId: string, @Request() req) {
    const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
    return this.avaliacaoService.findAvaliacoesByAluno(alunoId, personalId);
  }

  @ApiOperation({ summary: 'Buscar avaliação por ID' })
  @ApiResponse({ status: 200, description: 'Avaliação encontrada' })
  @ApiResponse({ status: 404, description: 'Avaliação não encontrada' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
    return this.avaliacaoService.findAvaliacaoById(id, personalId);
  }
}
