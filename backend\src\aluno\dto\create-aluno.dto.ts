import { IsEmail, IsNotEmpty, IsString, IsN<PERSON>ber, IsO<PERSON>al, IsDateString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CreateAlunoDto {
  @ApiProperty({
    description: 'Nome completo do aluno',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  nome: string;

  @ApiProperty({
    description: 'Email do aluno',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Telefone do aluno',
    example: '(11) 99999-9999',
    required: false,
  })
  @IsString()
  @IsOptional()
  telefone?: string;

  @ApiProperty({
    description: 'Senha do aluno (mínimo 6 caracteres)',
    example: 'senha123',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  senha: string;

  @ApiProperty({
    description: 'Peso atual do aluno em kg',
    example: 65.5,
  })
  @IsNumber()
  @Min(20)
  @Max(300)
  @Transform(({ value }) => parseFloat(value))
  pesoAtual: number;

  @ApiProperty({
    description: 'Estatura do aluno em cm',
    example: 165,
  })
  @IsNumber()
  @Min(100)
  @Max(250)
  @Transform(({ value }) => parseFloat(value))
  estatura: number;

  @ApiProperty({
    description: 'Data de nascimento do aluno',
    example: '1990-05-15',
  })
  @IsDateString()
  @IsNotEmpty()
  dataNascimento: string;

  @ApiProperty({
    description: 'Idade do aluno',
    example: 33,
  })
  @IsNumber()
  @Min(10)
  @Max(120)
  @Transform(({ value }) => parseInt(value))
  idade: number;

  @ApiProperty({
    description: 'Hábitos do aluno (JSON string)',
    example: '{"exercicio": "sedentario", "alimentacao": "regular", "sono": "7-8h"}',
    required: false,
  })
  @IsString()
  @IsOptional()
  habitos?: string;

  @ApiProperty({
    description: 'Objetivos do aluno (JSON string)',
    example: '{"principal": "perder peso", "meta": "10kg em 6 meses", "secundarios": ["melhorar condicionamento"]}',
    required: false,
  })
  @IsString()
  @IsOptional()
  objetivos?: string;
}
