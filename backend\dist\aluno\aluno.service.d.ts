import { PrismaService } from '../prisma/prisma.service';
import { CreateAlunoDto } from './dto/create-aluno.dto';
import { UpdateAlunoDto } from './dto/update-aluno.dto';
export declare class AlunoService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createAlunoDto: CreateAlunoDto, personalId: string): Promise<any>;
    findAllByPersonal(personalId: string): Promise<any>;
    findOne(id: string, personalId?: string): Promise<any>;
    update(id: string, updateAlunoDto: UpdateAlunoDto, personalId?: string): Promise<any>;
    remove(id: string, personalId: string): Promise<{
        message: string;
    }>;
    getAlunoStats(id: string, personalId?: string): Promise<{
        totalAvaliacoes: any;
        ultimaAvaliacao: any;
        primeiraAvaliacao: any;
        evolucao: {
            imc: {
                inicial: any;
                atual: any;
                diferenca: number | null;
            };
            gordura: {
                inicial: any;
                atual: any;
            };
        } | null;
    }>;
}
