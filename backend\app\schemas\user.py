"""
Schemas Pydantic para usuários
"""

from typing import Optional
from pydantic import BaseModel, EmailStr
from datetime import datetime
from app.models.models import UserType

class UserBase(BaseModel):
    """Schema base para usuário"""
    nome: str
    email: EmailStr
    tipo: UserType
    telefone: Optional[str] = None
    ativo: bool = True

class UserCreate(UserBase):
    """Schema para criação de usuário"""
    senha: str

class UserUpdate(BaseModel):
    """Schema para atualização de usuário"""
    nome: Optional[str] = None
    email: Optional[EmailStr] = None
    telefone: Optional[str] = None
    ativo: Optional[bool] = None

class UserInDB(UserBase):
    """Schema para usuário no banco de dados"""
    id: int
    senha_hash: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class User(UserBase):
    """Schema para resposta de usuário (sem senha)"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class UserLogin(BaseModel):
    """Schema para login de usuário"""
    email: EmailStr
    senha: str

class Token(BaseModel):
    """Schema para token de acesso"""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """Schema para dados do token"""
    email: Optional[str] = None
