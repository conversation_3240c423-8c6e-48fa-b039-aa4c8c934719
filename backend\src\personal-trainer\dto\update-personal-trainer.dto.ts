import { IsOptional, IsString, IsEmail } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdatePersonalTrainerDto {
  @ApiProperty({
    description: 'Nome do Personal Trainer',
    example: '<PERSON>',
    required: false,
  })
  @IsOptional()
  @IsString()
  nome?: string;

  @ApiProperty({
    description: 'Email do Personal Trainer',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'Telefone do Personal Trainer',
    example: '(11) 99999-9999',
    required: false,
  })
  @IsOptional()
  @IsString()
  telefone?: string;
}
