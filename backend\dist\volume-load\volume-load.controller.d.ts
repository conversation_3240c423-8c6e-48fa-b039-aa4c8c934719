import { VolumeLoadService } from './volume-load.service';
import { CreateVolumeLoadDto } from './dto/create-volume-load.dto';
import { UpdateVolumeLoadDto } from './dto/update-volume-load.dto';
export declare class VolumeLoadController {
    private readonly volumeLoadService;
    constructor(volumeLoadService: VolumeLoadService);
    create(createVolumeLoadDto: CreateVolumeLoadDto, req: any): Promise<any>;
    findByAvaliacao(avaliacaoId: string, req: any): Promise<any>;
    getStats(avaliacaoId: string, req: any): Promise<{
        porExercicio: unknown[];
        porSemana: unknown[];
        vlTotalMesociclo: any;
    }>;
    update(id: string, updateVolumeLoadDto: UpdateVolumeLoadDto, req: any): Promise<any>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}
