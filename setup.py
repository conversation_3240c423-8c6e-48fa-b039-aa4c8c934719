#!/usr/bin/env python3
"""
Script de configuração inicial do Hypatium
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(command, cwd=None):
    """Executar comando no shell"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            cwd=cwd,
            capture_output=True,
            text=True
        )
        print(f"✅ {command}")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao executar: {command}")
        print(f"Erro: {e.stderr}")
        return None

def check_requirements():
    """Verificar se os requisitos estão instalados"""
    print("🔍 Verificando requisitos...")
    
    # Verificar Python
    python_version = sys.version_info
    if python_version.major < 3 or python_version.minor < 11:
        print("❌ Python 3.11+ é necessário")
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}")
    
    # Verificar Node.js
    node_result = run_command("node --version")
    if not node_result:
        print("❌ Node.js não encontrado. Instale Node.js 18+")
        return False
    print(f"✅ Node.js {node_result.strip()}")
    
    # Verificar PostgreSQL (opcional)
    pg_result = run_command("psql --version")
    if not pg_result:
        print("⚠️  PostgreSQL não encontrado. Você pode usar Docker.")
    else:
        print(f"✅ PostgreSQL {pg_result.strip()}")
    
    return True

def setup_backend():
    """Configurar backend"""
    print("\n🐍 Configurando backend...")
    
    backend_dir = Path("backend")
    
    # Criar ambiente virtual
    if not (backend_dir / "venv").exists():
        run_command("python -m venv venv", cwd=backend_dir)
        print("✅ Ambiente virtual criado")
    
    # Ativar ambiente virtual e instalar dependências
    if os.name == 'nt':  # Windows
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/Mac
        pip_cmd = "venv/bin/pip"
    
    run_command(f"{pip_cmd} install -r requirements.txt", cwd=backend_dir)

def setup_frontend():
    """Configurar frontend"""
    print("\n⚛️  Configurando frontend...")
    
    frontend_dir = Path("frontend")
    
    # Instalar dependências
    run_command("npm install", cwd=frontend_dir)

def setup_environment():
    """Configurar arquivos de ambiente"""
    print("\n🔧 Configurando ambiente...")
    
    if not Path(".env").exists():
        # Copiar arquivo de exemplo
        with open(".env.example", "r") as f:
            content = f.read()
        
        with open(".env", "w") as f:
            f.write(content)
        
        print("✅ Arquivo .env criado a partir do exemplo")
        print("⚠️  Configure as variáveis de ambiente no arquivo .env")

def main():
    """Função principal"""
    print("🌟 Configuração inicial do Hypatium")
    print("=" * 50)
    
    if not check_requirements():
        print("\n❌ Requisitos não atendidos. Instale as dependências necessárias.")
        return
    
    setup_environment()
    setup_backend()
    setup_frontend()
    
    print("\n🎉 Configuração concluída!")
    print("\n📋 Próximos passos:")
    print("1. Configure o arquivo .env com suas credenciais")
    print("2. Inicie o PostgreSQL (ou use Docker: docker-compose up postgres)")
    print("3. Execute o backend: cd backend && uvicorn main:app --reload")
    print("4. Execute o frontend: cd frontend && npm run dev")
    print("5. Acesse http://localhost:3000")
    print("\n💡 Ou use Docker: docker-compose up")

if __name__ == "__main__":
    main()
