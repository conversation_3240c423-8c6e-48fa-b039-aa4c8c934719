"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAvaliacaoComposicaoDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class CreateAvaliacaoComposicaoDto {
    alunoId;
    subescapular;
    tricipital;
    bicipital;
    toracica;
    abdominal;
    axilarMedia;
    suprailíaca;
    coxa;
    panturrilha;
    ombro;
    torax;
    cintura;
    abdomen;
    abdomenInferior;
    bracoD;
    bracoE;
    bracoContraidoD;
    bracoContraidoE;
    antebracoD;
    antebracoE;
    quadril;
    coxaProximalD;
    coxaMedialD;
    coxaDistalD;
    coxaProximalE;
    coxaMedialE;
    coxaDistalE;
    panturrilhaD;
    panturrilhaE;
}
exports.CreateAvaliacaoComposicaoDto = CreateAvaliacaoComposicaoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID do aluno',
        example: 'clp123abc456',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAvaliacaoComposicaoDto.prototype, "alunoId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dobra subescapular em mm',
        example: 15.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "subescapular", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dobra tricipital em mm',
        example: 12.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "tricipital", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dobra bicipital em mm',
        example: 8.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "bicipital", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dobra torácica em mm',
        example: 10.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "toracica", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dobra abdominal em mm',
        example: 18.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "abdominal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dobra axilar média em mm',
        example: 9.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "axilarMedia", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dobra suprailíaca em mm',
        example: 14.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "suprail\u00EDaca", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dobra da coxa em mm',
        example: 16.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "coxa", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dobra da panturrilha em mm',
        example: 11.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "panturrilha", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do ombro em cm',
        example: 110.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(50),
    (0, class_validator_1.Max)(200),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "ombro", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do tórax em cm',
        example: 95.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(50),
    (0, class_validator_1.Max)(200),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "torax", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência da cintura em cm',
        example: 85.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(50),
    (0, class_validator_1.Max)(200),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "cintura", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do abdomen em cm',
        example: 88.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(50),
    (0, class_validator_1.Max)(200),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "abdomen", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do abdomen inferior em cm',
        example: 90.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(50),
    (0, class_validator_1.Max)(200),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "abdomenInferior", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do braço direito em cm',
        example: 32.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(15),
    (0, class_validator_1.Max)(60),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "bracoD", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do braço esquerdo em cm',
        example: 31.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(15),
    (0, class_validator_1.Max)(60),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "bracoE", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do braço contraído direito em cm',
        example: 34.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(15),
    (0, class_validator_1.Max)(60),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "bracoContraidoD", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do braço contraído esquerdo em cm',
        example: 33.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(15),
    (0, class_validator_1.Max)(60),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "bracoContraidoE", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do antebraço direito em cm',
        example: 26.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(15),
    (0, class_validator_1.Max)(50),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "antebracoD", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do antebraço esquerdo em cm',
        example: 25.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(15),
    (0, class_validator_1.Max)(50),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "antebracoE", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência do quadril em cm',
        example: 98.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(60),
    (0, class_validator_1.Max)(150),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "quadril", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência da coxa proximal direita em cm',
        example: 55.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(30),
    (0, class_validator_1.Max)(80),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "coxaProximalD", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência da coxa medial direita em cm',
        example: 52.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(30),
    (0, class_validator_1.Max)(80),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "coxaMedialD", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência da coxa distal direita em cm',
        example: 48.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(30),
    (0, class_validator_1.Max)(80),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "coxaDistalD", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência da coxa proximal esquerda em cm',
        example: 54.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(30),
    (0, class_validator_1.Max)(80),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "coxaProximalE", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência da coxa medial esquerda em cm',
        example: 51.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(30),
    (0, class_validator_1.Max)(80),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "coxaMedialE", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência da coxa distal esquerda em cm',
        example: 47.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(30),
    (0, class_validator_1.Max)(80),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "coxaDistalE", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência da panturrilha direita em cm',
        example: 36.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(20),
    (0, class_validator_1.Max)(60),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "panturrilhaD", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Circunferência da panturrilha esquerda em cm',
        example: 35.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(20),
    (0, class_validator_1.Max)(60),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoComposicaoDto.prototype, "panturrilhaE", void 0);
//# sourceMappingURL=create-avaliacao-composicao.dto.js.map