// Paleta de cores Hypatium
export const colors = {
  primary: {
    50: '#E8F8F5',
    100: '#D1F2EB',
    200: '#A3E4D7',
    300: '#76D7C4',
    400: '#48C9B0',
    500: '#1ABC9C', // Cor principal
    600: '#17A085',
    700: '#148F77',
    800: '#117A65',
    900: '#0E6655',
  },
  secondary: {
    50: '#E8F6F3',
    100: '#D0EDE6',
    200: '#A2DBCE',
    300: '#73C9B5',
    400: '#48C9B0', // Verde secundário
    500: '#27AE60', // Verde escuro
    600: '#229954',
    700: '#1E8449',
    800: '#196F3D',
    900: '#145A32',
  },
  success: {
    50: '#E8F5E8',
    100: '#C8E6C9',
    200: '#A5D6A7',
    300: '#81C784',
    400: '#66BB6A',
    500: '#2ECC71', // Verde sucesso
    600: '#27AE60',
    700: '#229954',
    800: '#1B5E20',
    900: '#1B5E20',
  },
  gray: {
    50: '#F8F9FA',
    100: '#F1F3F4',
    200: '#E8EAED',
    300: '#DADCE0',
    400: '#BDC1C6',
    500: '#9AA0A6',
    600: '#80868B',
    700: '#5F6368',
    800: '#3C4043',
    900: '#333333', // Cinza escuro principal
  },
  white: '#FFFFFF',
  black: '#000000',
  
  // Estados
  error: '#E74C3C',
  warning: '#F39C12',
  info: '#3498DB',
  
  // Gradientes
  gradients: {
    primary: 'linear-gradient(135deg, #1ABC9C 0%, #48C9B0 100%)',
    secondary: 'linear-gradient(135deg, #2ECC71 0%, #27AE60 100%)',
    dark: 'linear-gradient(135deg, #333333 0%, #2C3E50 100%)',
  },
} as const;

export type ColorKey = keyof typeof colors;
export type ColorShade = keyof typeof colors.primary;
