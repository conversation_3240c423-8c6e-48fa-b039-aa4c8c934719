import { Controller, Get, Body, Patch, Param, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { PersonalTrainerService } from './personal-trainer.service';
import { UpdatePersonalTrainerDto } from './dto/update-personal-trainer.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('personal-trainer')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('personal')
@Controller('personal-trainer')
export class PersonalTrainerController {
  constructor(private readonly personalTrainerService: PersonalTrainerService) {}

  @ApiOperation({ summary: 'Buscar perfil do Personal Trainer' })
  @ApiResponse({ status: 200, description: 'Perfil encontrado com sucesso' })
  @ApiResponse({ status: 404, description: 'Personal Trainer não encontrado' })
  @Get('profile')
  findProfile(@Request() req) {
    return this.personalTrainerService.findOne(req.user.id);
  }

  @ApiOperation({ summary: 'Buscar Personal Trainer por ID' })
  @ApiResponse({ status: 200, description: 'Personal Trainer encontrado com sucesso' })
  @ApiResponse({ status: 404, description: 'Personal Trainer não encontrado' })
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.personalTrainerService.findOne(id);
  }

  @ApiOperation({ summary: 'Atualizar dados do Personal Trainer' })
  @ApiResponse({ status: 200, description: 'Personal Trainer atualizado com sucesso' })
  @ApiResponse({ status: 404, description: 'Personal Trainer não encontrado' })
  @Patch(':id')
  update(@Param('id') id: string, @Body() updatePersonalTrainerDto: UpdatePersonalTrainerDto) {
    return this.personalTrainerService.update(id, updatePersonalTrainerDto);
  }

  @ApiOperation({ summary: 'Buscar estatísticas do dashboard' })
  @ApiResponse({ status: 200, description: 'Estatísticas encontradas com sucesso' })
  @Get('dashboard/stats')
  getDashboardStats(@Request() req) {
    return this.personalTrainerService.getDashboardStats(req.user.id);
  }
}
