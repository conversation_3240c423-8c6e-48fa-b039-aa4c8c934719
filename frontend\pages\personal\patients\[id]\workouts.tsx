import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  ArrowLeft,
  Plus,
  Activity,
  Calendar,
  Clock,
  Target,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

interface Workout {
  id: number
  nome: string
  descricao: string
  tipo: string
  duracao_estimada: number
  nivel_dificuldade: string
  data_criacao: string
  ativo: boolean
  exercicios_count: number
}

interface Patient {
  id: number
  nome: string
  email: string
}

export default function PatientWorkouts() {
  const router = useRouter()
  const { id } = router.query
  const [patient, setPatient] = useState<Patient | null>(null)
  const [workouts, setWorkouts] = useState<Workout[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (id) {
      fetchData()
    }
  }, [id])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Mock data - em produção viria da <PERSON>
      setPatient({
        id: Number(id),
        nome: '<PERSON>',
        email: '<EMAIL>'
      })

      setWorkouts([
        {
          id: 1,
          nome: 'Treino A - Peito e Tríceps',
          descricao: 'Treino focado em peito e tríceps para hipertrofia',
          tipo: 'Hipertrofia',
          duracao_estimada: 60,
          nivel_dificuldade: 'Intermediário',
          data_criacao: '2024-01-15',
          ativo: true,
          exercicios_count: 8
        },
        {
          id: 2,
          nome: 'Treino B - Costas e Bíceps',
          descricao: 'Treino para desenvolvimento das costas e bíceps',
          tipo: 'Hipertrofia',
          duracao_estimada: 65,
          nivel_dificuldade: 'Intermediário',
          data_criacao: '2024-01-16',
          ativo: true,
          exercicios_count: 7
        },
        {
          id: 3,
          nome: 'Treino C - Pernas',
          descricao: 'Treino completo de membros inferiores',
          tipo: 'Hipertrofia',
          duracao_estimada: 70,
          nivel_dificuldade: 'Avançado',
          data_criacao: '2024-01-17',
          ativo: true,
          exercicios_count: 9
        }
      ])
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'iniciante':
        return 'bg-green-100 text-green-800'
      case 'intermediário':
        return 'bg-yellow-100 text-yellow-800'
      case 'avançado':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Treinos - {patient?.nome} | Hypatium</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link 
                  href={`/personal/patients/${id}`}
                  className="mr-4 p-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    <Activity className="h-6 w-6 text-blue-600 mr-2" />
                    Treinos - {patient?.nome}
                  </h1>
                  <p className="text-gray-600">Gerencie os treinos do paciente</p>
                </div>
              </div>
              <Link
                href={`/personal/workouts/new?patient=${id}`}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Novo Treino
              </Link>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {workouts.length === 0 ? (
              <div className="bg-white shadow rounded-lg p-8 text-center">
                <Activity className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum treino criado</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Comece criando o primeiro treino para este paciente.
                </p>
                <div className="mt-6">
                  <Link
                    href={`/personal/workouts/new?patient=${id}`}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Criar Primeiro Treino
                  </Link>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {workouts.map((workout) => (
                  <div key={workout.id} className="bg-white shadow rounded-lg overflow-hidden">
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900 truncate">
                          {workout.nome}
                        </h3>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getDifficultyColor(workout.nivel_dificuldade)}`}>
                          {workout.nivel_dificuldade}
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                        {workout.descricao}
                      </p>

                      <div className="space-y-2 mb-4">
                        <div className="flex items-center text-sm text-gray-500">
                          <Target className="h-4 w-4 mr-2" />
                          {workout.tipo}
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-2" />
                          {workout.duracao_estimada} minutos
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <Activity className="h-4 w-4 mr-2" />
                          {workout.exercicios_count} exercícios
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className="h-4 w-4 mr-2" />
                          Criado em {formatDate(workout.data_criacao)}
                        </div>
                      </div>

                      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div className="flex space-x-2">
                          <Link
                            href={`/personal/workouts/${workout.id}`}
                            className="text-blue-600 hover:text-blue-900"
                            title="Visualizar"
                          >
                            <Eye className="h-4 w-4" />
                          </Link>
                          <Link
                            href={`/personal/workouts/${workout.id}/edit`}
                            className="text-green-600 hover:text-green-900"
                            title="Editar"
                          >
                            <Edit className="h-4 w-4" />
                          </Link>
                          <button
                            className="text-red-600 hover:text-red-900"
                            title="Excluir"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          workout.ativo ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {workout.ativo ? 'Ativo' : 'Inativo'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}
