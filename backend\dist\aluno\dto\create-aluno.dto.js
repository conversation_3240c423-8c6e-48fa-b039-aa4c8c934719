"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAlunoDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class CreateAlunoDto {
    nome;
    email;
    telefone;
    senha;
    pesoAtual;
    estatura;
    dataNascimento;
    idade;
    habitos;
    objetivos;
}
exports.CreateAlunoDto = CreateAlunoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome completo do aluno',
        example: 'Maria Silva',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAlunoDto.prototype, "nome", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email do aluno',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAlunoDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone do aluno',
        example: '(11) 99999-9999',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateAlunoDto.prototype, "telefone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Senha do aluno (mínimo 6 caracteres)',
        example: 'senha123',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MinLength)(6),
    __metadata("design:type", String)
], CreateAlunoDto.prototype, "senha", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Peso atual do aluno em kg',
        example: 65.5,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(20),
    (0, class_validator_1.Max)(300),
    (0, class_transformer_1.Transform)(({ value }) => parseFloat(value)),
    __metadata("design:type", Number)
], CreateAlunoDto.prototype, "pesoAtual", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Estatura do aluno em cm',
        example: 165,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(100),
    (0, class_validator_1.Max)(250),
    (0, class_transformer_1.Transform)(({ value }) => parseFloat(value)),
    __metadata("design:type", Number)
], CreateAlunoDto.prototype, "estatura", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de nascimento do aluno',
        example: '1990-05-15',
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAlunoDto.prototype, "dataNascimento", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Idade do aluno',
        example: 33,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(10),
    (0, class_validator_1.Max)(120),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    __metadata("design:type", Number)
], CreateAlunoDto.prototype, "idade", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Hábitos do aluno (JSON string)',
        example: '{"exercicio": "sedentario", "alimentacao": "regular", "sono": "7-8h"}',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateAlunoDto.prototype, "habitos", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Objetivos do aluno (JSON string)',
        example: '{"principal": "perder peso", "meta": "10kg em 6 meses", "secundarios": ["melhorar condicionamento"]}',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateAlunoDto.prototype, "objetivos", void 0);
//# sourceMappingURL=create-aluno.dto.js.map