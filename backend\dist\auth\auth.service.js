"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma/prisma.service");
const bcrypt = require("bcryptjs");
let AuthService = class AuthService {
    prisma;
    jwtService;
    constructor(prisma, jwtService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
    }
    async validateUser(email, password) {
        const personal = await this.prisma.personalTrainer.findUnique({
            where: { email },
        });
        if (personal && await bcrypt.compare(password, personal.senha)) {
            const { senha, ...result } = personal;
            return { ...result, userType: 'personal' };
        }
        const aluno = await this.prisma.aluno.findUnique({
            where: { email },
        });
        if (aluno && await bcrypt.compare(password, aluno.senha)) {
            const { senha, ...result } = aluno;
            return { ...result, userType: 'aluno' };
        }
        return null;
    }
    async login(loginDto) {
        const user = await this.validateUser(loginDto.email, loginDto.password);
        if (!user) {
            throw new common_1.UnauthorizedException('Credenciais inválidas');
        }
        const payload = {
            email: user.email,
            sub: user.id,
            userType: user.userType,
            nome: user.nome
        };
        return {
            access_token: this.jwtService.sign(payload),
            user: {
                id: user.id,
                email: user.email,
                nome: user.nome,
                userType: user.userType,
            },
        };
    }
    async registerPersonal(registerDto) {
        const hashedPassword = await bcrypt.hash(registerDto.password, 10);
        const personal = await this.prisma.personalTrainer.create({
            data: {
                nome: registerDto.nome,
                email: registerDto.email,
                telefone: registerDto.telefone,
                senha: hashedPassword,
            },
        });
        const { senha, ...result } = personal;
        return result;
    }
    async registerAluno(registerDto) {
        const hashedPassword = await bcrypt.hash(registerDto.password, 10);
        const aluno = await this.prisma.aluno.create({
            data: {
                nome: registerDto.nome,
                email: registerDto.email,
                telefone: registerDto.telefone,
                senha: hashedPassword,
                personalId: registerDto.personalId,
                pesoAtual: registerDto.pesoAtual,
                estatura: registerDto.estatura,
                dataNascimento: registerDto.dataNascimento,
                idade: registerDto.idade,
            },
        });
        const { senha, ...result } = aluno;
        return result;
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, typeof (_a = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _a : Object])
], AuthService);
//# sourceMappingURL=auth.service.js.map