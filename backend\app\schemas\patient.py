"""
Schemas Pydantic para pacientes
"""

from typing import Optional
from pydantic import BaseModel, EmailStr
from datetime import datetime, date
from app.models.models import Gender

class PatientBase(BaseModel):
    """Schema base para paciente"""
    nome: str
    email: Optional[EmailStr] = None
    telefone: Optional[str] = None
    nascimento: Optional[date] = None
    sexo: Optional[Gender] = None
    profissao: Optional[str] = None
    objetivo: Optional[str] = None
    observacoes: Optional[str] = None
    ativo: bool = True

class PatientCreate(PatientBase):
    """Schema para criação de paciente"""
    responsavel_id: int

class PatientUpdate(BaseModel):
    """Schema para atualização de paciente"""
    nome: Optional[str] = None
    email: Optional[EmailStr] = None
    telefone: Optional[str] = None
    nascimento: Optional[date] = None
    sexo: Optional[Gender] = None
    profissao: Optional[str] = None
    objetivo: Optional[str] = None
    observacoes: Optional[str] = None
    ativo: Optional[bool] = None

class Patient(PatientBase):
    """Schema para resposta de paciente"""
    id: int
    responsavel_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class PatientWithDetails(Patient):
    """Schema para paciente com detalhes completos"""
    # Aqui podemos adicionar relacionamentos quando necessário
    pass
