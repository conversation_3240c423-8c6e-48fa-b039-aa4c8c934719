import Image from 'next/image'

interface LogoProps {
  size?: number
  className?: string
  showText?: boolean
  textClassName?: string
}

export default function Logo({ 
  size = 40, 
  className = "rounded-md", 
  showText = true,
  textClassName = "ml-2 text-2xl font-bold text-gray-900"
}: LogoProps) {
  return (
    <div className="flex items-center">
      <Image
        src="/logo.png"
        alt="Hypatium Logo"
        width={size}
        height={size}
        className={className}
        priority
      />
      {showText && (
        <span className={textClassName}>
          Hypatium
        </span>
      )}
    </div>
  )
}
