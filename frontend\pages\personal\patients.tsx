import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import {
  Users,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  TrendingUp,
  Calendar,
  Phone,
  Mail
} from 'lucide-react'
import Layout from '../../components/Layout'

interface Patient {
  id: number
  nome: string
  email?: string
  telefone?: string
  nascimento?: string
  sexo?: string
  objetivo?: string
  ativo: boolean
  created_at: string
}

export default function PatientsPage() {
  const router = useRouter()
  const [patients, setPatients] = useState<Patient[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterActive, setFilterActive] = useState(true)

  useEffect(() => {
    fetchPatients()
  }, [])

  const fetchPatients = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Mock data - em produção viria da <PERSON>
      const mockPatients = [
        {
          id: 1,
          nome: '<PERSON>',
          email: '<EMAIL>',
          telefone: '(11) 97777-7777',
          nascimento: '1990-05-10',
          sexo: 'masculino',
          profissao: 'Engenheiro de Software',
          objetivo: 'Ganho de massa muscular',
          observacoes: 'Paciente dedicado',
          ativo: true,
          created_at: '2024-01-20'
        },
        {
          id: 2,
          nome: 'Maria Silva',
          email: '<EMAIL>',
          telefone: '(11) 98888-8888',
          nascimento: '1985-07-15',
          sexo: 'feminino',
          profissao: 'Designer',
          objetivo: 'Condicionamento físico',
          observacoes: 'Iniciante',
          ativo: true,
          created_at: '2024-01-15'
        },
        {
          id: 3,
          nome: 'Carlos Oliveira',
          email: '<EMAIL>',
          telefone: '(11) 99999-9999',
          nascimento: '1988-03-22',
          sexo: 'masculino',
          profissao: 'Médico',
          objetivo: 'Perda de peso',
          observacoes: 'Tem limitações no joelho',
          ativo: false,
          created_at: '2024-01-10'
        }
      ]

      setPatients(mockPatients)
    } catch (error) {
      console.error('Erro ao buscar pacientes:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredPatients = patients.filter(patient => {
    const matchesSearch = patient.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         patient.email?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterActive ? patient.ativo : true
    return matchesSearch && matchesFilter
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const calculateAge = (birthDate: string) => {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }

    return age
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  return (
    <Layout title="Pacientes - Personal Trainer">
      <Head>
        <title>Meus Pacientes - Hypatium</title>
        <meta name="description" content="Gerencie seus pacientes" />
      </Head>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Filters */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="Buscar pacientes..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filterActive}
                      onChange={(e) => setFilterActive(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Apenas ativos</span>
                  </label>
                  <button className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                    <Filter className="h-4 w-4 mr-2" />
                    Filtros
                  </button>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total de Pacientes</p>
                    <p className="text-2xl font-bold text-gray-900">{patients.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Pacientes Ativos</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {patients.filter(p => p.ativo).length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Calendar className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Novos este mês</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {patients.filter(p => {
                        const created = new Date(p.created_at)
                        const now = new Date()
                        return created.getMonth() === now.getMonth() &&
                               created.getFullYear() === now.getFullYear()
                      }).length}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Patients List */}
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {filteredPatients.length === 0 ? (
                  <li className="px-6 py-12 text-center">
                    <Users className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum paciente encontrado</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {searchTerm ? 'Tente ajustar sua busca.' : 'Comece adicionando um novo paciente.'}
                    </p>
                    {!searchTerm && (
                      <div className="mt-6">
                        <Link
                          href="/personal/patients/new"
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Novo Paciente
                        </Link>
                      </div>
                    )}
                  </li>
                ) : (
                  filteredPatients.map((patient) => (
                    <li key={patient.id}>
                      <div className="px-6 py-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="flex-shrink-0">
                              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                <span className="text-sm font-medium text-blue-600">
                                  {patient.nome.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="flex items-center">
                                <p className="text-sm font-medium text-gray-900">{patient.nome}</p>
                                {!patient.ativo && (
                                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Inativo
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center text-sm text-gray-500 space-x-4">
                                {patient.email && (
                                  <div className="flex items-center">
                                    <Mail className="h-4 w-4 mr-1" />
                                    {patient.email}
                                  </div>
                                )}
                                {patient.telefone && (
                                  <div className="flex items-center">
                                    <Phone className="h-4 w-4 mr-1" />
                                    {patient.telefone}
                                  </div>
                                )}
                                {patient.nascimento && (
                                  <div>
                                    {calculateAge(patient.nascimento)} anos
                                  </div>
                                )}
                              </div>
                              {patient.objetivo && (
                                <p className="text-sm text-gray-600 mt-1">{patient.objetivo}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Link
                              href={`/personal/patients/${patient.id}`}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <Eye className="h-4 w-4" />
                            </Link>
                            <Link
                              href={`/personal/patients/${patient.id}/edit`}
                              className="text-gray-600 hover:text-gray-900"
                            >
                              <Edit className="h-4 w-4" />
                            </Link>
                            <button className="text-red-600 hover:text-red-900">
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))
                )}
              </ul>
            </div>
          </div>
      </div>
    </Layout>
  )
}
