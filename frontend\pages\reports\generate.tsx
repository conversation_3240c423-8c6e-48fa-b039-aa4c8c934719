import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  FileText, 
  User, 
  Calendar,
  Settings,
  Download,
  Eye,
  Sparkles,
  BarChart3
} from 'lucide-react'

const reportSchema = z.object({
  patient_id: z.number().min(1, 'Selecione um paciente'),
  report_type: z.enum(['comprehensive', 'physical_only', 'psychological_only', 'nutritional_only']),
  period_days: z.number().min(7).max(365).default(30),
})

type ReportForm = z.infer<typeof reportSchema>

interface Patient {
  id: number
  nome: string
}

interface ReportTemplate {
  id: string
  name: string
  description: string
  sections: string[]
}

interface GeneratedReport {
  id: number
  tipo_relatorio: string
  conteudo_gerado: string
  data_geracao: string
  status: string
}

export default function GenerateReportPage() {
  const router = useRouter()
  const { patient: patientId } = router.query
  const [patients, setPatients] = useState<Patient[]>([])
  const [templates, setTemplates] = useState<ReportTemplate[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState('')
  const [generatedReport, setGeneratedReport] = useState<GeneratedReport | null>(null)
  const [previewData, setPreviewData] = useState<any>(null)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<ReportForm>({
    resolver: zodResolver(reportSchema),
    defaultValues: {
      patient_id: patientId ? parseInt(patientId as string) : 0,
      report_type: 'comprehensive',
      period_days: 30,
    }
  })

  const watchedValues = watch()

  useEffect(() => {
    fetchPatients()
    fetchTemplates()
  }, [])

  useEffect(() => {
    if (patientId) {
      setValue('patient_id', parseInt(patientId as string))
    }
  }, [patientId, setValue])

  useEffect(() => {
    if (watchedValues.patient_id && watchedValues.period_days) {
      fetchPreviewData()
    }
  }, [watchedValues.patient_id, watchedValues.period_days])

  const fetchPatients = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/patients/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        setPatients(data)
      }
    } catch (error) {
      console.error('Erro ao buscar pacientes:', error)
    }
  }

  const fetchTemplates = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/reports/templates`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        setTemplates(data)
      }
    } catch (error) {
      console.error('Erro ao buscar templates:', error)
    }
  }

  const fetchPreviewData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/reports/patient/${watchedValues.patient_id}/preview?period_days=${watchedValues.period_days}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (response.ok) {
        const data = await response.json()
        setPreviewData(data)
      }
    } catch (error) {
      console.error('Erro ao buscar preview:', error)
    }
  }

  const onSubmit = async (data: ReportForm) => {
    setIsGenerating(true)
    setError('')

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/reports/generate/${data.patient_id}?report_type=${data.report_type}&period_days=${data.period_days}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Erro ao gerar relatório')
      }

      const report = await response.json()
      setGeneratedReport(report)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao gerar relatório')
    } finally {
      setIsGenerating(false)
    }
  }

  const getTemplateById = (id: string) => {
    return templates.find(t => t.id === id)
  }

  const selectedPatient = patients.find(p => p.id === watchedValues.patient_id)
  const selectedTemplate = getTemplateById(watchedValues.report_type)

  return (
    <>
      <Head>
        <title>Gerar Relatório - Hypatium</title>
        <meta name="description" content="Gerar relatório automático com IA" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href="/dashboard" className="text-blue-600 hover:text-blue-800">
                  ← Voltar ao Dashboard
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <FileText className="h-6 w-6 mr-2" />
                Gerar Relatório com IA
              </h1>
              <div></div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Form */}
              <div className="space-y-6">
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Configurações do Relatório */}
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="mb-6">
                      <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center">
                        <Settings className="h-5 w-5 mr-2" />
                        Configurações do Relatório
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Configure os parâmetros para geração do relatório automático.
                      </p>
                    </div>

                    {error && (
                      <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                        {error}
                      </div>
                    )}

                    <div className="grid grid-cols-6 gap-6">
                      <div className="col-span-6">
                        <label className="block text-sm font-medium text-gray-700">
                          Paciente *
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <User className="h-5 w-5 text-gray-400" />
                          </div>
                          <select
                            {...register('patient_id', { valueAsNumber: true })}
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value={0}>Selecione um paciente...</option>
                            {patients.map(patient => (
                              <option key={patient.id} value={patient.id}>
                                {patient.nome}
                              </option>
                            ))}
                          </select>
                        </div>
                        {errors.patient_id && (
                          <p className="mt-1 text-sm text-red-600">{errors.patient_id.message}</p>
                        )}
                      </div>

                      <div className="col-span-6 sm:col-span-3">
                        <label className="block text-sm font-medium text-gray-700">
                          Tipo de Relatório *
                        </label>
                        <select
                          {...register('report_type')}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                          {templates.map(template => (
                            <option key={template.id} value={template.id}>
                              {template.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="col-span-6 sm:col-span-3">
                        <label className="block text-sm font-medium text-gray-700">
                          Período (dias) *
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Calendar className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            {...register('period_days', { valueAsNumber: true })}
                            type="number"
                            min="7"
                            max="365"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        {errors.period_days && (
                          <p className="mt-1 text-sm text-red-600">{errors.period_days.message}</p>
                        )}
                      </div>

                      {selectedTemplate && (
                        <div className="col-span-6">
                          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                            <h4 className="text-sm font-medium text-blue-900">
                              {selectedTemplate.name}
                            </h4>
                            <p className="text-sm text-blue-700 mt-1">
                              {selectedTemplate.description}
                            </p>
                            <div className="mt-2">
                              <p className="text-xs text-blue-600">Seções incluídas:</p>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {selectedTemplate.sections.map(section => (
                                  <span
                                    key={section}
                                    className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                                  >
                                    {section}
                                  </span>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={isGenerating || !watchedValues.patient_id}
                      className="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      {isGenerating ? (
                        <>
                          <div className="loading-spinner mr-2"></div>
                          Gerando Relatório...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          Gerar com IA
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>

              {/* Preview/Results */}
              <div className="space-y-6">
                {/* Preview Data */}
                {previewData && selectedPatient && (
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                      <BarChart3 className="h-5 w-5 mr-2" />
                      Dados Disponíveis - {selectedPatient.nome}
                    </h3>
                    
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <p className="text-2xl font-bold text-blue-600">
                          {previewData.data_summary?.assessments_count || 0}
                        </p>
                        <p className="text-sm text-blue-700">Avaliações</p>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <p className="text-2xl font-bold text-green-600">
                          {previewData.data_summary?.psychology_sessions_count || 0}
                        </p>
                        <p className="text-sm text-green-700">Sessões</p>
                      </div>
                      <div className="text-center p-3 bg-purple-50 rounded-lg">
                        <p className="text-2xl font-bold text-purple-600">
                          {previewData.data_summary?.nutrition_plans_count || 0}
                        </p>
                        <p className="text-sm text-purple-700">Planos</p>
                      </div>
                    </div>

                    <div className="text-sm text-gray-600">
                      <p>Período: {watchedValues.period_days} dias</p>
                      <p>Dados coletados para análise da IA</p>
                    </div>
                  </div>
                )}

                {/* Generated Report */}
                {generatedReport && (
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <FileText className="h-5 w-5 mr-2" />
                        Relatório Gerado
                      </h3>
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 flex items-center text-sm">
                          <Eye className="h-4 w-4 mr-1" />
                          Visualizar
                        </button>
                        <button className="text-green-600 hover:text-green-800 flex items-center text-sm">
                          <Download className="h-4 w-4 mr-1" />
                          Download PDF
                        </button>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                      <div className="prose prose-sm max-w-none">
                        <pre className="whitespace-pre-wrap text-sm text-gray-800">
                          {generatedReport.conteudo_gerado}
                        </pre>
                      </div>
                    </div>

                    <div className="mt-4 text-xs text-gray-500">
                      Gerado em: {new Date(generatedReport.data_geracao).toLocaleString('pt-BR')}
                    </div>
                  </div>
                )}

                {!previewData && !generatedReport && (
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="text-center text-gray-500">
                      <Sparkles className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">
                        Relatório com IA
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Selecione um paciente para ver os dados disponíveis e gerar um relatório automático.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
