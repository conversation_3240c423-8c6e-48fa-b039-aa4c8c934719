// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model PersonalTrainer {
  id           String   @id @default(cuid())
  nome         String
  email        String   @unique
  telefone     String?
  senha        String
  dataCadastro DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relacionamentos
  alunos      Aluno[]
  avaliacoes  AvaliacaoComposicao[]

  @@map("personal_trainers")
}

model Aluno {
  id              String    @id @default(cuid())
  nome            String
  email           String    @unique
  telefone        String?
  senha           String
  pesoAtual       Float     // kg
  estatura        Float     // cm
  dataNascimento  DateTime
  idade           Int
  habitos         String?   // JSON string
  objetivos       String?   // JSON string
  personalId      String
  dataCadastro    DateTime  @default(now())
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relacionamentos
  personal        PersonalTrainer @relation(fields: [personalId], references: [id])
  avaliacoes      AvaliacaoComposicao[]

  @@map("alunos")
}

model AvaliacaoComposicao {
  id              String   @id @default(cuid())
  alunoId         String
  personalId      String
  dataAvaliacao   DateTime @default(now())
  
  // Dobras Cutâneas (mm)
  subescapular    Float?
  tricipital      Float?
  bicipital       Float?
  toracica        Float?
  abdominal       Float?
  axilarMedia     Float?
  suprailíaca     Float?
  coxa            Float?
  panturrilha     Float?
  somaDobras      Float?   // Calculado automaticamente
  
  // Circunferências (cm)
  ombro           Float?
  torax           Float?
  cintura         Float?
  abdomen         Float?
  abdomenInferior Float?
  bracoD          Float?
  bracoE          Float?
  bracoContraidoD Float?
  bracoContraidoE Float?
  antebracoD      Float?
  antebracoE      Float?
  quadril         Float?
  coxaProximalD   Float?
  coxaMedialD     Float?
  coxaDistalD     Float?
  coxaProximalE   Float?
  coxaMedialE     Float?
  coxaDistalE     Float?
  panturrilhaD    Float?
  panturrilhaE    Float?
  
  // IMC e Classificação
  valorIMC        Float?
  categoriaIMC    String?
  
  // Métodos de Gordura (%)
  // Guedes
  guedesDensidade     Float?
  guedesPercentual    Float?
  guedesMassaGordura  Float?
  guedesMassaMagro    Float?
  
  // Pollock3
  pollock3Densidade     Float?
  pollock3Percentual    Float?
  pollock3MassaGordura  Float?
  pollock3MassaMagro    Float?
  
  // Pollock7
  pollock7Densidade     Float?
  pollock7Percentual    Float?
  pollock7MassaGordura  Float?
  pollock7MassaMagro    Float?
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relacionamentos
  aluno           Aluno @relation(fields: [alunoId], references: [id])
  personal        PersonalTrainer @relation(fields: [personalId], references: [id])
  avaliacaoEnergetica AvaliacaoEnergetica?
  volumeLoads     VolumeLoad[]
  relatorios      Relatorio[]

  @@map("avaliacoes_composicao")
}

model AvaliacaoEnergetica {
  id                      String   @id @default(cuid())
  avaliacaoComposicaoId   String   @unique
  
  // TMB (kcal)
  tmbFAO_OMS              Float?
  tmbHarrisBenedict       Float?
  tmbMifflin              Float?
  tmbCunningham           Float?
  tmbTinsleyMLG           Float?
  tmbTinsleyPeso          Float?
  
  // GET (kcal)
  fatorAtividade          Float?   // 1.2, 1.4, 1.6, 1.8, 2.0
  valorGET                Float?   // TMB × fatorAtividade
  
  // Consumo (kcal)
  percentualDeficitSuperavit Float? // -30% a +30%
  valorConsumo            Float?   // GET × (1 ± percentual)
  
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  // Relacionamentos
  avaliacaoComposicao     AvaliacaoComposicao @relation(fields: [avaliacaoComposicaoId], references: [id])

  @@map("avaliacoes_energeticas")
}

model VolumeLoad {
  id                      String   @id @default(cuid())
  avaliacaoComposicaoId   String
  exercicio               Int      // 1 a 15
  semana                  Int      // 1 a 8
  
  // Séries (1-10)
  serie1Repeticoes        Int?
  serie1Carga             Float?
  serie1VL                Float?   // repetições × carga
  
  serie2Repeticoes        Int?
  serie2Carga             Float?
  serie2VL                Float?
  
  serie3Repeticoes        Int?
  serie3Carga             Float?
  serie3VL                Float?
  
  serie4Repeticoes        Int?
  serie4Carga             Float?
  serie4VL                Float?
  
  serie5Repeticoes        Int?
  serie5Carga             Float?
  serie5VL                Float?
  
  serie6Repeticoes        Int?
  serie6Carga             Float?
  serie6VL                Float?
  
  serie7Repeticoes        Int?
  serie7Carga             Float?
  serie7VL                Float?
  
  serie8Repeticoes        Int?
  serie8Carga             Float?
  serie8VL                Float?
  
  serie9Repeticoes        Int?
  serie9Carga             Float?
  serie9VL                Float?
  
  serie10Repeticoes       Int?
  serie10Carga            Float?
  serie10VL               Float?
  
  vlExercicio             Float?   // soma de todas as séries
  vlMesociclo             Float?   // soma de todas as semanas
  
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  // Relacionamentos
  avaliacaoComposicao     AvaliacaoComposicao @relation(fields: [avaliacaoComposicaoId], references: [id])

  @@map("volume_loads")
}

model Relatorio {
  id                      String   @id @default(cuid())
  avaliacaoComposicaoId   String
  
  textoAnalise            String?  // Gerado por LLM
  graficosEvolucao        String?  // JSON com dados dos gráficos
  dataGeracao             DateTime @default(now())
  arquivoPDF              String?  // URL ou path do PDF
  
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  // Relacionamentos
  avaliacaoComposicao     AvaliacaoComposicao @relation(fields: [avaliacaoComposicaoId], references: [id])

  @@map("relatorios")
}

model ChatMessage {
  id          String   @id @default(cuid())
  alunoId     String?
  personalId  String?
  message     String
  sender      String   // 'aluno', 'personal', 'bot'
  timestamp   DateTime @default(now())
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("chat_messages")
}
