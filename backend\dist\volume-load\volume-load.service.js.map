{"version": 3, "file": "volume-load.service.js", "sourceRoot": "", "sources": ["../../src/volume-load/volume-load.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmF;AACnF,6DAAyD;AAKlD,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IACR;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAGrC,eAAe,CAAC,UAAmB,EAAE,KAAc;QACzD,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK;YAAE,OAAO,CAAC,CAAC;QACpC,OAAO,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAGO,mBAAmB,CAAC,UAAe;QACzC,MAAM,MAAM,GAAG;YACb,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,WAAW,CAAC;YACzE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,WAAW,CAAC;YACzE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,WAAW,CAAC;YACzE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,WAAW,CAAC;YACzE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,WAAW,CAAC;YACzE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,WAAW,CAAC;YACzE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,WAAW,CAAC;YACzE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,WAAW,CAAC;YACzE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,WAAW,CAAC;YACzE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,iBAAiB,EAAE,UAAU,CAAC,YAAY,CAAC;SAC5E,CAAC;QAEF,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,mBAAwC,EAAE,UAAkB;QAEvE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC;YACjE,KAAK,EAAE,EAAE,EAAE,EAAE,mBAAmB,CAAC,qBAAqB,EAAE;YACxD,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;SACzB,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,SAAS,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;YACxC,MAAM,IAAI,2BAAkB,CAAC,gCAAgC,CAAC,CAAC;QACjE,CAAC;QAGD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7G,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7G,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7G,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7G,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7G,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7G,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7G,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7G,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7G,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,YAAY,CAAC,CAAC;QAGhH,MAAM,WAAW,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;YACrD,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;QAGzE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACrD,IAAI,EAAE;gBACJ,GAAG,mBAAmB;gBACtB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,SAAS;gBACpB,WAAW;aACZ;SACF,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAEzG,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,mBAAwC,EAAE,UAAkB;QAEnF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC;YACzD,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,OAAO,EAAE;gBACP,mBAAmB,EAAE,IAAI;aAC1B;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,UAAU,CAAC,mBAAmB,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;YAC7D,MAAM,IAAI,2BAAkB,CAAC,kCAAkC,CAAC,CAAC;QACnE,CAAC;QAGD,MAAM,gBAAgB,GAAG,EAAE,GAAG,UAAU,EAAE,GAAG,mBAAmB,EAAE,CAAC;QACnE,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAE/D,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC5D,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,IAAI,EAAE;gBACJ,GAAG,mBAAmB;gBACtB,WAAW;aACZ;SACF,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,qBAAqB,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;QAEvF,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,qBAA6B,EAAE,SAAiB;QAEhF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACxD,KAAK,EAAE;gBACL,qBAAqB;gBACrB,SAAS;aACV;SACF,CAAC,CAAC;QAGH,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAGxF,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC;YACtC,KAAK,EAAE;gBACL,qBAAqB;gBACrB,SAAS;aACV;YACD,IAAI,EAAE;gBACJ,WAAW;aACZ;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,qBAA6B,EAAE,UAAmB;QAEtE,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC;gBACjE,KAAK,EAAE,EAAE,EAAE,EAAE,qBAAqB,EAAE;aACrC,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;gBACtD,MAAM,IAAI,2BAAkB,CAAC,gCAAgC,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACrC,KAAK,EAAE,EAAE,qBAAqB,EAAE;YAChC,OAAO,EAAE;gBACP,EAAE,SAAS,EAAE,KAAK,EAAE;gBACpB,EAAE,MAAM,EAAE,KAAK,EAAE;aAClB;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,qBAA6B,EAAE,UAAmB;QACzE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,UAAU,CAAC,CAAC;QAGlF,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE;YAClD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;gBACvB,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG;oBAClB,SAAS,EAAE,EAAE,CAAC,SAAS;oBACvB,OAAO,EAAE,EAAE;oBACX,OAAO,EAAE,CAAC;iBACX,CAAC;YACJ,CAAC;YAED,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC7B,MAAM,EAAE,EAAE,CAAC,MAAM;gBACjB,WAAW,EAAE,EAAE,CAAC,WAAW,IAAI,CAAC;aACjC,CAAC,CAAC;YAEH,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,OAAO,IAAI,EAAE,CAAC,WAAW,IAAI,CAAC,CAAC;YAEjD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAS,CAAC,CAAC;QAGd,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE;YAC/C,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpB,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG;oBACf,MAAM,EAAE,EAAE,CAAC,MAAM;oBACjB,UAAU,EAAE,EAAE;oBACd,OAAO,EAAE,CAAC;iBACX,CAAC;YACJ,CAAC;YAED,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC;gBAC7B,SAAS,EAAE,EAAE,CAAC,SAAS;gBACvB,WAAW,EAAE,EAAE,CAAC,WAAW,IAAI,CAAC;aACjC,CAAC,CAAC;YAEH,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,OAAO,IAAI,EAAE,CAAC,WAAW,IAAI,CAAC,CAAC;YAE9C,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAS,CAAC,CAAC;QAEd,OAAO;YACL,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YACzC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YACnC,gBAAgB,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;SACtF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,UAAkB;QACzC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC;YACzD,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,OAAO,EAAE;gBACP,mBAAmB,EAAE,IAAI;aAC1B;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,UAAU,CAAC,mBAAmB,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;YAC7D,MAAM,IAAI,2BAAkB,CAAC,kCAAkC,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAClC,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,qBAAqB,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;QAEvF,OAAO,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;IACzD,CAAC;CACF,CAAA;AA7OY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,iBAAiB,CA6O7B"}