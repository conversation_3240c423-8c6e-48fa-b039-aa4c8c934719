import { useState } from 'react'
import Head from 'next/head'
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Calendar,
  Download,
  Filter,
  FileText,
  PieChart,
  Activity
} from 'lucide-react'
import Layout from '../components/Layout'

export default function Reports() {
  const [selectedPeriod, setSelectedPeriod] = useState('30')
  const [selectedModule, setSelectedModule] = useState('all')

  // Mock data
  const stats = {
    totalPatients: 156,
    activeSessions: 89,
    completedWorkouts: 234,
    revenue: 15420
  }

  const recentReports = [
    {
      id: 1,
      title: 'Relatório Mensal - Personal Trainer',
      type: 'personal',
      date: '2024-01-15',
      patients: 45,
      status: 'completed'
    },
    {
      id: 2,
      title: 'Análise Nutricional - Janeiro',
      type: 'nutrition',
      date: '2024-01-10',
      patients: 32,
      status: 'completed'
    },
    {
      id: 3,
      title: 'Sessões de Psicologia - Dezembro',
      type: 'psychology',
      date: '2024-01-05',
      patients: 28,
      status: 'completed'
    }
  ]

  const getModuleIcon = (type: string) => {
    switch (type) {
      case 'personal':
        return <Activity className="h-4 w-4 text-blue-600" />
      case 'nutrition':
        return <PieChart className="h-4 w-4 text-green-600" />
      case 'psychology':
        return <BarChart3 className="h-4 w-4 text-purple-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getModuleName = (type: string) => {
    switch (type) {
      case 'personal':
        return 'Personal Trainer'
      case 'nutrition':
        return 'Nutrição'
      case 'psychology':
        return 'Psicologia'
      default:
        return 'Geral'
    }
  }

  return (
    <Layout title="Relatórios">
      <Head>
        <title>Relatórios | Hypatium</title>
      </Head>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <BarChart3 className="h-6 w-6 text-indigo-600 mr-2" />
              Relatórios e Analytics
            </h1>
            <p className="text-gray-600 mt-1">
              Acompanhe o desempenho e gere relatórios detalhados
            </p>
          </div>

          {/* Filters */}
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <div className="flex flex-wrap items-center gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Período
                </label>
                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="7">Últimos 7 dias</option>
                  <option value="30">Últimos 30 dias</option>
                  <option value="90">Últimos 3 meses</option>
                  <option value="365">Último ano</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Módulo
                </label>
                <select
                  value={selectedModule}
                  onChange={(e) => setSelectedModule(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">Todos os módulos</option>
                  <option value="personal">Personal Trainer</option>
                  <option value="nutrition">Nutrição</option>
                  <option value="psychology">Psicologia</option>
                </select>
              </div>
              <div className="flex items-end">
                <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                  <Filter className="h-4 w-4 mr-2" />
                  Aplicar Filtros
                </button>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total de Pacientes</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalPatients}</p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Calendar className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Sessões Ativas</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeSessions}</p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Activity className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Treinos Concluídos</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.completedWorkouts}</p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-8 w-8 text-indigo-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Receita (R$)</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.revenue.toLocaleString('pt-BR')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Placeholder */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Evolução de Pacientes
              </h3>
              <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Gráfico de evolução</p>
                  <p className="text-sm text-gray-400">Em desenvolvimento</p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Distribuição por Módulo
              </h3>
              <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <PieChart className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Gráfico de pizza</p>
                  <p className="text-sm text-gray-400">Em desenvolvimento</p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Reports */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">
                  Relatórios Recentes
                </h3>
                <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                  <Download className="h-4 w-4 mr-2" />
                  Gerar Novo Relatório
                </button>
              </div>
            </div>
            <div className="divide-y divide-gray-200">
              {recentReports.map((report) => (
                <div key={report.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {getModuleIcon(report.type)}
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          {report.title}
                        </p>
                        <p className="text-sm text-gray-500">
                          {getModuleName(report.type)} • {report.patients} pacientes
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-500">
                        {new Date(report.date).toLocaleDateString('pt-BR')}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Concluído
                      </span>
                      <button className="text-indigo-600 hover:text-indigo-900">
                        <Download className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white shadow rounded-lg p-6 text-center">
              <Activity className="h-8 w-8 text-blue-600 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Relatório de Treinos
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                Análise detalhada dos treinos realizados
              </p>
              <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                Gerar Relatório
              </button>
            </div>

            <div className="bg-white shadow rounded-lg p-6 text-center">
              <PieChart className="h-8 w-8 text-green-600 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Análise Nutricional
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                Relatório de planos nutricionais e evolução
              </p>
              <button className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                Gerar Relatório
              </button>
            </div>

            <div className="bg-white shadow rounded-lg p-6 text-center">
              <BarChart3 className="h-8 w-8 text-purple-600 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Sessões Psicológicas
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                Relatório de sessões e progresso dos pacientes
              </p>
              <button className="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                Gerar Relatório
              </button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
