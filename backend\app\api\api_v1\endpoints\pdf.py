"""
Endpoints para geração de PDFs
"""

from typing import Any, List, Dict
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.models.models import User, AutomaticReport, PhysicalAssessment, NutritionalPlan, Workout, Patient
from app.services.pdf_service import PDFService

router = APIRouter()

@router.post("/report/{report_id}")
def generate_report_pdf(
    *,
    db: Session = Depends(get_db),
    report_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Gerar PDF de relatório
    """
    # Buscar relatório
    report = db.query(AutomaticReport).filter(AutomaticReport.id == report_id).first()
    if not report:
        raise HTTPException(status_code=404, detail="Relatório não encontrado")
    
    # Buscar dados do paciente
    patient = db.query(Patient).filter(Patient.id == report.paciente_id).first()
    if not patient:
        raise HTTPException(status_code=404, detail="Paciente não encontrado")
    
    # Verificar permissões
    if current_user.tipo not in ["admin"] and current_user.id != report.profissional_id:
        raise HTTPException(status_code=403, detail="Sem permissão para gerar PDF deste relatório")
    
    pdf_service = PDFService()
    
    report_data = {
        'tipo_relatorio': report.tipo_relatorio,
        'conteudo_gerado': report.conteudo_gerado,
        'data_geracao': report.data_geracao.isoformat() if report.data_geracao else None,
        'profissional': current_user.nome if hasattr(current_user, 'nome') else 'Não informado'
    }
    
    result = pdf_service.generate_report_pdf(report_data, patient.nome)
    
    if not result['success']:
        raise HTTPException(status_code=500, detail=f"Erro ao gerar PDF: {result['error']}")
    
    return result

@router.post("/assessment/{assessment_id}")
def generate_assessment_pdf(
    *,
    db: Session = Depends(get_db),
    assessment_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Gerar PDF de avaliação física
    """
    # Buscar avaliação
    assessment = db.query(PhysicalAssessment).filter(PhysicalAssessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(status_code=404, detail="Avaliação não encontrada")
    
    # Buscar dados do paciente
    patient = db.query(Patient).filter(Patient.id == assessment.paciente_id).first()
    if not patient:
        raise HTTPException(status_code=404, detail="Paciente não encontrado")
    
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"] and current_user.id != assessment.profissional_id:
        raise HTTPException(status_code=403, detail="Sem permissão para gerar PDF desta avaliação")
    
    pdf_service = PDFService()
    
    assessment_data = {
        'data_avaliacao': assessment.data_avaliacao.strftime('%d/%m/%Y') if assessment.data_avaliacao else None,
        'peso': assessment.peso,
        'altura': assessment.altura,
        'imc': assessment.imc,
        'gordura_percentual': assessment.gordura_percentual,
        'massa_gorda': assessment.massa_gorda,
        'massa_magra': assessment.massa_magra,
        'metodo_dobras': assessment.metodo_dobras,
        'dobra_peitoral': assessment.dobra_peitoral,
        'dobra_abdominal': assessment.dobra_abdominal,
        'dobra_coxa': assessment.dobra_coxa,
        'dobra_triceps': assessment.dobra_triceps,
        'dobra_subescapular': assessment.dobra_subescapular,
        'dobra_suprailiaca': assessment.dobra_suprailiaca,
        'dobra_axilar_media': assessment.dobra_axilar_media,
        'observacoes': assessment.observacoes,
        'profissional': current_user.nome if hasattr(current_user, 'nome') else 'Não informado'
    }
    
    result = pdf_service.generate_assessment_pdf(assessment_data, patient.nome)
    
    if not result['success']:
        raise HTTPException(status_code=500, detail=f"Erro ao gerar PDF: {result['error']}")
    
    return result

@router.post("/nutrition/{plan_id}")
def generate_nutrition_pdf(
    *,
    db: Session = Depends(get_db),
    plan_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Gerar PDF de plano nutricional
    """
    # Buscar plano nutricional
    plan = db.query(NutritionalPlan).filter(NutritionalPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plano nutricional não encontrado")
    
    # Buscar dados do paciente
    patient = db.query(Patient).filter(Patient.id == plan.paciente_id).first()
    if not patient:
        raise HTTPException(status_code=404, detail="Paciente não encontrado")
    
    # Verificar permissões
    if current_user.tipo not in ["nutricionista", "admin"] and current_user.id != plan.nutricionista_id:
        raise HTTPException(status_code=403, detail="Sem permissão para gerar PDF deste plano")
    
    pdf_service = PDFService()
    
    plan_data = {
        'data_criacao': plan.data_criacao.strftime('%d/%m/%Y') if plan.data_criacao else None,
        'tmb_calculado': plan.tmb_calculado,
        'get_calculado': plan.get_calculado,
        'calorias_alvo': plan.calorias_alvo,
        'proteinas_g': plan.proteinas_g,
        'carboidratos_g': plan.carboidratos_g,
        'gorduras_g': plan.gorduras_g,
        'observacoes': plan.observacoes,
        'nutricionista': current_user.nome if hasattr(current_user, 'nome') else 'Não informado'
    }
    
    result = pdf_service.generate_nutrition_plan_pdf(plan_data, patient.nome)
    
    if not result['success']:
        raise HTTPException(status_code=500, detail=f"Erro ao gerar PDF: {result['error']}")
    
    return result

@router.post("/workout/{workout_id}")
def generate_workout_pdf(
    *,
    db: Session = Depends(get_db),
    workout_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Gerar PDF de treino
    """
    # Buscar treino
    workout = db.query(Workout).filter(Workout.id == workout_id).first()
    if not workout:
        raise HTTPException(status_code=404, detail="Treino não encontrado")
    
    # Buscar dados do paciente
    patient = db.query(Patient).filter(Patient.id == workout.paciente_id).first()
    if not patient:
        raise HTTPException(status_code=404, detail="Paciente não encontrado")
    
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"] and current_user.id != workout.personal_id:
        raise HTTPException(status_code=403, detail="Sem permissão para gerar PDF deste treino")
    
    # Buscar exercícios do treino
    from app.models.models import WorkoutExercise
    exercises = db.query(WorkoutExercise).filter(WorkoutExercise.treino_id == workout_id).order_by(WorkoutExercise.ordem).all()
    
    pdf_service = PDFService()
    
    workout_data = {
        'nome': workout.nome,
        'data_inicio': workout.data_inicio.strftime('%d/%m/%Y') if workout.data_inicio else None,
        'descricao': workout.descricao,
        'observacoes': workout.observacoes,
        'exercicios': [
            {
                'nome_exercicio': ex.nome_exercicio,
                'grupo_muscular': ex.grupo_muscular,
                'series': ex.series,
                'repeticoes': ex.repeticoes,
                'carga': ex.carga,
                'descanso': ex.descanso,
                'observacoes': ex.observacoes
            } for ex in exercises
        ],
        'personal': current_user.nome if hasattr(current_user, 'nome') else 'Não informado'
    }
    
    result = pdf_service.generate_workout_pdf(workout_data, patient.nome)
    
    if not result['success']:
        raise HTTPException(status_code=500, detail=f"Erro ao gerar PDF: {result['error']}")
    
    return result

@router.get("/download/{filename}")
def download_pdf(
    *,
    filename: str,
    current_user: User = Depends(deps.get_current_active_user),
) -> FileResponse:
    """
    Download de PDF gerado
    """
    pdf_service = PDFService()
    file_path = pdf_service.output_dir / filename
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Arquivo não encontrado")
    
    return FileResponse(
        path=str(file_path),
        filename=filename,
        media_type='application/pdf'
    )

@router.get("/list")
def list_pdfs(
    *,
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Listar PDFs gerados
    """
    pdf_service = PDFService()
    pdfs = pdf_service.list_generated_pdfs(limit=limit)
    
    # Filtrar por usuário se não for admin
    if current_user.tipo != "admin":
        # TODO: Implementar filtro por usuário baseado no nome do arquivo
        pass
    
    return pdfs

@router.delete("/{filename}")
def delete_pdf(
    *,
    filename: str,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Deletar PDF
    """
    # Verificar permissões
    if current_user.tipo != "admin":
        # TODO: Verificar se o usuário pode deletar este PDF
        pass
    
    pdf_service = PDFService()
    success = pdf_service.delete_pdf(filename)
    
    if not success:
        raise HTTPException(status_code=404, detail="Arquivo não encontrado")
    
    return {"message": "PDF deletado com sucesso"}

@router.get("/stats")
def get_pdf_stats(
    *,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter estatísticas de PDFs
    """
    pdf_service = PDFService()
    stats = pdf_service.get_pdf_stats()
    return stats

@router.post("/cleanup")
def cleanup_old_pdfs(
    *,
    days_old: int = Query(30, ge=1, le=365),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Limpar PDFs antigos (apenas admin)
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Apenas administradores podem executar limpeza")
    
    pdf_service = PDFService()
    result = pdf_service.cleanup_old_pdfs(days_old=days_old)
    
    return result
