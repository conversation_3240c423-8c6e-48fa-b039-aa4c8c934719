# 🚀 G<PERSON>a de Início <PERSON>pido - Hypatium

## 📋 Pré-requisitos

Antes de começar, certifique-se de ter instalado:

- **Python 3.11+** - [Download](https://python.org)
- **Node.js 18+** - [Download](https://nodejs.org)
- **PostgreSQL 15+** - [Download](https://postgresql.org) (ou use Docker)
- **Git** - [Download](https://git-scm.com)

## 🛠️ Configuração Automática (Recomendado)

### 1. Clone o repositório
```bash
git clone https://github.com/seu-usuario/hypatium.git
cd hypatium
```

### 2. Execute o script de configuração
```bash
python setup.py
```

### 3. Configure as variáveis de ambiente
```bash
# Copie o arquivo de exemplo
cp .env.example .env

# Edite o arquivo .env com suas configurações
# Pelo menos configure a SECRET_KEY para produção
```

### 4. Inicie o sistema com Docker (mais fácil)
```bash
docker-compose up -d
```

**OU** inicie manualmente:

```bash
# Terminal 1 - Backend
cd backend
source venv/bin/activate  # Linux/Mac
# ou venv\Scripts\activate  # Windows
uvicorn main:app --reload

# Terminal 2 - Frontend
cd frontend
npm run dev
```

## 🧪 Testando o Sistema

### 1. Teste a API
```bash
python test_system.py
```

### 2. Acesse o frontend
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Documentação API**: http://localhost:8000/docs

### 3. Faça login
- **Email**: <EMAIL>
- **Senha**: admin123

## 📁 Estrutura do Projeto

```
hypatium/
├── backend/                 # API FastAPI
│   ├── app/
│   │   ├── api/            # Rotas da API
│   │   ├── core/           # Configurações
│   │   ├── models/         # Modelos SQLAlchemy
│   │   ├── schemas/        # Schemas Pydantic
│   │   └── services/       # Lógica de negócio
│   ├── requirements.txt
│   └── main.py
├── frontend/               # App Next.js
│   ├── components/         # Componentes React
│   ├── pages/             # Páginas Next.js
│   ├── styles/            # Estilos CSS
│   └── package.json
├── docker-compose.yml      # Orquestração Docker
└── README.md
```

## 🔧 Configuração Manual

### Backend

1. **Criar ambiente virtual**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou venv\Scripts\activate  # Windows
```

2. **Instalar dependências**
```bash
pip install -r requirements.txt
```

3. **Configurar banco de dados**
```bash
# Com Docker
docker run --name hypatium-postgres \
  -e POSTGRES_PASSWORD=hypatium123 \
  -e POSTGRES_USER=hypatium \
  -e POSTGRES_DB=hypatium \
  -p 5432:5432 -d postgres:15

# Ou instale PostgreSQL localmente e crie o banco
createdb hypatium
```

4. **Criar usuário admin**
```bash
python create_admin.py
```

5. **Iniciar servidor**
```bash
uvicorn main:app --reload
```

### Frontend

1. **Instalar dependências**
```bash
cd frontend
npm install
```

2. **Iniciar servidor de desenvolvimento**
```bash
npm run dev
```

## 🌐 URLs Importantes

| Serviço | URL | Descrição |
|---------|-----|-----------|
| Frontend | http://localhost:3000 | Interface do usuário |
| Backend API | http://localhost:8000 | API REST |
| Docs API | http://localhost:8000/docs | Documentação Swagger |
| Redoc | http://localhost:8000/redoc | Documentação alternativa |

## 👥 Usuários de Teste

### Administrador
- **Email**: <EMAIL>
- **Senha**: admin123
- **Tipo**: Administrador

### Criando Novos Usuários
1. Acesse http://localhost:3000/register
2. Preencha os dados
3. Escolha o tipo: Personal Trainer, Nutricionista ou Psicólogo

## 🧪 Testando Funcionalidades

### 1. Personal Trainer
- Cadastrar pacientes
- Criar avaliações físicas
- Visualizar cálculos automáticos (IMC, TMB, etc.)
- Acompanhar progresso

### 2. Nutricionista
- Calcular necessidades calóricas
- Criar planos alimentares
- Gerar relatórios nutricionais

### 3. Psicólogo
- Registrar sessões
- Fazer anotações
- Acompanhar evolução emocional

## 🐛 Solução de Problemas

### Erro de Conexão com Banco
```bash
# Verificar se PostgreSQL está rodando
docker ps  # Se usando Docker
# ou
pg_isready  # Se instalado localmente
```

### Erro de Dependências Python
```bash
# Reinstalar dependências
pip install --upgrade -r requirements.txt
```

### Erro de Dependências Node.js
```bash
# Limpar cache e reinstalar
rm -rf node_modules package-lock.json
npm install
```

### Erro de CORS
- Verifique se o frontend está rodando na porta 3000
- Verifique as configurações de CORS no backend

## 📚 Próximos Passos

1. **Explore a documentação da API**: http://localhost:8000/docs
2. **Teste as funcionalidades** no frontend
3. **Configure APIs externas** (OpenAI, Google Speech-to-Text)
4. **Personalize** conforme suas necessidades

## 🆘 Suporte

- **Issues**: [GitHub Issues](https://github.com/seu-usuario/hypatium/issues)
- **Documentação**: [Wiki](https://github.com/seu-usuario/hypatium/wiki)
- **Email**: <EMAIL>

---

**🎉 Parabéns! Você configurou o Hypatium com sucesso!**
