"""
Endpoints de psicologia
"""

from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.models.models import User
from app.schemas.psychology import (
    PsychologySession,
    PsychologySessionCreate,
    PsychologySessionUpdate,
    AudioTranscription,
    PsychologicalAssessment,
    TherapeuticGoal,
    PsychologyReport
)
from app.services.psychology_service import PsychologyService
from app.services.ai_service import AIService

router = APIRouter()

@router.post("/", response_model=PsychologySession)
def create_psychology_session(
    *,
    db: Session = Depends(get_db),
    session_in: PsychologySessionCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Criar nova sessão psicológica
    """
    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas psicólogos podem criar sessões")

    psychology_service = PsychologyService(db)

    # Definir psicólogo como usuário atual se não especificado
    if not session_in.psicologo_id:
        session_in.psicologo_id = current_user.id

    try:
        session = psychology_service.create_session(session_in)
        return session
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/patient/{patient_id}", response_model=List[PsychologySession])
def read_sessions_by_patient(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter sessões psicológicas de um paciente
    """
    psychology_service = PsychologyService(db)
    sessions = psychology_service.get_sessions_by_patient(patient_id, skip=skip, limit=limit)
    return sessions

@router.get("/psychologist/{psychologist_id}", response_model=List[PsychologySession])
def read_sessions_by_psychologist(
    *,
    db: Session = Depends(get_db),
    psychologist_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter sessões criadas por um psicólogo
    """
    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"] and current_user.id != psychologist_id:
        raise HTTPException(status_code=403, detail="Sem permissão para acessar estas sessões")

    psychology_service = PsychologyService(db)
    sessions = psychology_service.get_sessions_by_psychologist(psychologist_id, skip=skip, limit=limit)
    return sessions

@router.get("/{session_id}", response_model=PsychologySession)
def read_psychology_session(
    *,
    db: Session = Depends(get_db),
    session_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter sessão psicológica por ID
    """
    psychology_service = PsychologyService(db)
    session = psychology_service.get_session(session_id)

    if not session:
        raise HTTPException(status_code=404, detail="Sessão não encontrada")

    return session

@router.put("/{session_id}", response_model=PsychologySession)
def update_psychology_session(
    *,
    db: Session = Depends(get_db),
    session_id: int,
    session_in: PsychologySessionUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Atualizar sessão psicológica
    """
    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas psicólogos podem atualizar sessões")

    psychology_service = PsychologyService(db)
    session = psychology_service.update_session(session_id, session_in)

    if not session:
        raise HTTPException(status_code=404, detail="Sessão não encontrada")

    return session

@router.delete("/{session_id}")
def delete_psychology_session(
    *,
    db: Session = Depends(get_db),
    session_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Deletar sessão psicológica
    """
    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas psicólogos podem deletar sessões")

    psychology_service = PsychologyService(db)
    success = psychology_service.delete_session(session_id)

    if not success:
        raise HTTPException(status_code=404, detail="Sessão não encontrada")

    return {"message": "Sessão deletada com sucesso"}

# Endpoints de análise e estatísticas
@router.get("/patient/{patient_id}/progress", response_model=Dict[str, Any])
def get_patient_progress(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter progresso psicológico do paciente
    """
    psychology_service = PsychologyService(db)
    progress = psychology_service.get_patient_progress(patient_id, limit=limit)
    return progress

@router.get("/psychologist/{psychologist_id}/statistics", response_model=Dict[str, Any])
def get_psychologist_statistics(
    *,
    db: Session = Depends(get_db),
    psychologist_id: int,
    period_days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter estatísticas do psicólogo
    """
    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"] and current_user.id != psychologist_id:
        raise HTTPException(status_code=403, detail="Sem permissão para acessar estas estatísticas")

    psychology_service = PsychologyService(db)
    stats = psychology_service.get_session_statistics(psychologist_id, period_days=period_days)
    return stats

@router.get("/search", response_model=List[PsychologySession])
def search_sessions(
    *,
    db: Session = Depends(get_db),
    query: str = Query(..., min_length=3),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Buscar sessões por conteúdo
    """
    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas psicólogos podem buscar sessões")

    psychology_service = PsychologyService(db)
    sessions = psychology_service.search_sessions_by_content(current_user.id, query, limit=limit)
    return sessions

@router.get("/upcoming", response_model=List[PsychologySession])
def get_upcoming_sessions(
    *,
    db: Session = Depends(get_db),
    days_ahead: int = Query(7, ge=1, le=30),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter próximas sessões agendadas
    """
    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas psicólogos podem ver sessões agendadas")

    psychology_service = PsychologyService(db)
    sessions = psychology_service.get_upcoming_sessions(current_user.id, days_ahead=days_ahead)
    return sessions

@router.get("/{session_id}/summary", response_model=Dict[str, Any])
def get_session_summary(
    *,
    db: Session = Depends(get_db),
    session_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Gerar resumo da sessão
    """
    psychology_service = PsychologyService(db)
    summary = psychology_service.generate_session_summary(session_id)

    if not summary:
        raise HTTPException(status_code=404, detail="Sessão não encontrada")

    return summary

@router.post("/{session_id}/transcription")
def add_transcription(
    *,
    db: Session = Depends(get_db),
    session_id: int,
    transcription_text: str,
    confidence_score: Optional[float] = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Adicionar transcrição de áudio à sessão
    """
    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas psicólogos podem adicionar transcrições")

    psychology_service = PsychologyService(db)
    session = psychology_service.add_audio_transcription(session_id, transcription_text, confidence_score)

    if not session:
        raise HTTPException(status_code=404, detail="Sessão não encontrada")

    return {"message": "Transcrição adicionada com sucesso"}

@router.get("/patient/{patient_id}/history", response_model=Dict[str, Any])
def get_patient_history(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter histórico completo de sessões do paciente
    """
    psychology_service = PsychologyService(db)
    history = psychology_service.get_patient_session_history(patient_id)
    return history

@router.post("/{session_id}/transcribe")
async def transcribe_session_audio(
    *,
    db: Session = Depends(get_db),
    session_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Transcrever áudio da sessão psicológica automaticamente
    """
    psychology_service = PsychologyService(db)
    session = psychology_service.get_session(session_id)

    if not session:
        raise HTTPException(status_code=404, detail="Sessão não encontrada")

    if not session.audio_url:
        raise HTTPException(status_code=400, detail="Sessão não possui áudio para transcrever")

    if session.transcricao_processada:
        raise HTTPException(status_code=400, detail="Áudio já foi transcrito")

    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"] and current_user.id != session.psicologo_id:
        raise HTTPException(status_code=403, detail="Sem permissão para transcrever esta sessão")

    try:
        # Usar serviço de IA para transcrever
        ai_service = AIService()
        transcription_result = await ai_service.transcribe_audio(session.audio_url)

        if transcription_result.get("error"):
            raise HTTPException(status_code=500, detail=f"Erro na transcrição: {transcription_result['error']}")

        # Atualizar sessão com transcrição
        import json
        update_data = {
            "transcricao_texto": transcription_result["text"],
            "transcricao_processada": True,
            "transcricao_confianca": transcription_result.get("confidence", 0.0),
            "transcricao_resumo": transcription_result.get("summary", ""),
            "transcricao_metadados": json.dumps({
                "model_used": transcription_result.get("model_used"),
                "language": transcription_result.get("language"),
                "duration": transcription_result.get("duration"),
                "segments": transcription_result.get("segments", []),
                "generated_at": transcription_result.get("generated_at")
            })
        }

        updated_session = psychology_service.update_session(session_id, update_data)

        return {
            "success": True,
            "session_id": session_id,
            "transcription": {
                "text": transcription_result["text"],
                "confidence": transcription_result.get("confidence"),
                "summary": transcription_result.get("summary"),
                "language": transcription_result.get("language"),
                "duration": transcription_result.get("duration"),
                "segments_count": len(transcription_result.get("segments", []))
            },
            "session": updated_session
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao processar transcrição: {str(e)}")

@router.get("/{session_id}/transcription")
def get_session_transcription(
    *,
    db: Session = Depends(get_db),
    session_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter transcrição da sessão
    """
    psychology_service = PsychologyService(db)
    session = psychology_service.get_session(session_id)

    if not session:
        raise HTTPException(status_code=404, detail="Sessão não encontrada")

    if not session.transcricao_processada:
        raise HTTPException(status_code=404, detail="Sessão não possui transcrição")

    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"] and current_user.id != session.psicologo_id:
        raise HTTPException(status_code=403, detail="Sem permissão para acessar esta transcrição")

    import json
    metadata = {}
    if session.transcricao_metadados:
        try:
            metadata = json.loads(session.transcricao_metadados)
        except:
            pass

    return {
        "session_id": session_id,
        "text": session.transcricao_texto,
        "summary": session.transcricao_resumo,
        "confidence": session.transcricao_confianca,
        "processed": session.transcricao_processada,
        "metadata": metadata
    }
