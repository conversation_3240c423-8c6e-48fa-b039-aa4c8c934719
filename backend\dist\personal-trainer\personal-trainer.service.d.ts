import { PrismaService } from '../prisma/prisma.service';
import { UpdatePersonalTrainerDto } from './dto/update-personal-trainer.dto';
export declare class PersonalTrainerService {
    private prisma;
    constructor(prisma: PrismaService);
    findOne(id: string): Promise<any>;
    update(id: string, updatePersonalTrainerDto: UpdatePersonalTrainerDto): Promise<any>;
    getDashboardStats(personalId: string): Promise<{
        totalAlunos: any;
        totalAvaliacoes: any;
        avaliacoesRecentes: any;
    }>;
}
