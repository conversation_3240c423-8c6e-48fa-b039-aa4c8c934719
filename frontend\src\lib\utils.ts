// Função simples para combinar classes CSS
export function cn(...inputs: (string | undefined | null | boolean)[]) {
  return inputs.filter(Boolean).join(' ');
}

// Função para formatar datas
export function formatDate(date: Date | string): string {
  const d = new Date(date);
  return d.toLocaleDateString('pt-BR');
}

// Função para formatar números
export function formatNumber(num: number, decimals: number = 2): string {
  return num.toFixed(decimals).replace('.', ',');
}

// Função para calcular IMC
export function calculateIMC(weight: number, height: number): number {
  const heightInMeters = height / 100;
  return weight / (heightInMeters * heightInMeters);
}

// Função para classificar IMC
export function classifyIMC(imc: number): string {
  if (imc < 18.5) return 'Abaixo do peso';
  if (imc < 25) return 'Peso normal';
  if (imc < 30) return 'Sobrepeso';
  if (imc < 35) return 'Obesidade grau I';
  if (imc < 40) return 'Obesidade grau II';
  return 'Obesidade grau III';
}

// Função para validar email
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Função para formatar telefone
export function formatPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  }
  if (cleaned.length === 10) {
    return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }
  return phone;
}

// Função para gerar cores de status
export function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'ativo':
    case 'concluído':
    case 'aprovado':
      return 'text-green-600 bg-green-100';
    case 'pendente':
    case 'em andamento':
      return 'text-yellow-600 bg-yellow-100';
    case 'inativo':
    case 'cancelado':
    case 'rejeitado':
      return 'text-red-600 bg-red-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
}
