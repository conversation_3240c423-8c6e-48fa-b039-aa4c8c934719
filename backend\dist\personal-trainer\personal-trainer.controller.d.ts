import { PersonalTrainerService } from './personal-trainer.service';
import { UpdatePersonalTrainerDto } from './dto/update-personal-trainer.dto';
export declare class PersonalTrainerController {
    private readonly personalTrainerService;
    constructor(personalTrainerService: PersonalTrainerService);
    findProfile(req: any): Promise<any>;
    findOne(id: string): Promise<any>;
    update(id: string, updatePersonalTrainerDto: UpdatePersonalTrainerDto): Promise<any>;
    getDashboardStats(req: any): Promise<{
        totalAlunos: any;
        totalAvaliacoes: any;
        avaliacoesRecentes: any;
    }>;
}
