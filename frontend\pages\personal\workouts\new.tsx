import { useState } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { ArrowLeft, Save, Activity, Plus, Trash2 } from 'lucide-react'
import Layout from '../../../components/Layout'

interface Exercise {
  id: string
  nome: string
  series: number
  repeticoes: string
  peso: string
  descanso: string
}

interface WorkoutForm {
  nome: string
  descricao: string
  tipo: string
  nivel_dificuldade: string
  duracao_estimada: number
  exercicios: Exercise[]
}

export default function NewWorkout() {
  const router = useRouter()
  const { patient } = router.query
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<WorkoutForm>({
    nome: '',
    descricao: '',
    tipo: '',
    nivel_dificuldade: '',
    duracao_estimada: 60,
    exercicios: []
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      console.log('Dados do treino:', formData)
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (patient) {
        router.push(`/personal/patients/${patient}/workouts`)
      } else {
        router.push('/personal/workouts')
      }
    } catch (error) {
      console.error('Erro ao salvar treino:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const addExercise = () => {
    const newExercise: Exercise = {
      id: Date.now().toString(),
      nome: '',
      series: 3,
      repeticoes: '',
      peso: '',
      descanso: '60'
    }
    setFormData({
      ...formData,
      exercicios: [...formData.exercicios, newExercise]
    })
  }

  const removeExercise = (id: string) => {
    setFormData({
      ...formData,
      exercicios: formData.exercicios.filter(ex => ex.id !== id)
    })
  }

  const updateExercise = (id: string, field: keyof Exercise, value: string | number) => {
    setFormData({
      ...formData,
      exercicios: formData.exercicios.map(ex => 
        ex.id === id ? { ...ex, [field]: value } : ex
      )
    })
  }

  return (
    <Layout title="Novo Treino">
      <Head>
        <title>Novo Treino | Hypatium</title>
      </Head>

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <Link
                href={patient ? `/personal/patients/${patient}` : '/personal/workouts'}
                className="mr-4 p-2 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Activity className="h-6 w-6 text-blue-600 mr-2" />
                  Novo Treino
                </h1>
                <p className="text-gray-600">Crie um novo treino personalizado</p>
              </div>
            </div>
          </div>

          <div className="bg-white shadow rounded-lg">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Informações do Treino</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nome do Treino *
                    </label>
                    <input
                      type="text"
                      name="nome"
                      required
                      value={formData.nome}
                      onChange={handleChange}
                      placeholder="Ex: Treino A - Peito e Tríceps"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tipo de Treino *
                    </label>
                    <select
                      name="tipo"
                      required
                      value={formData.tipo}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Selecione</option>
                      <option value="Hipertrofia">Hipertrofia</option>
                      <option value="Força">Força</option>
                      <option value="Resistência">Resistência</option>
                      <option value="Funcional">Funcional</option>
                      <option value="Cardio">Cardio</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nível de Dificuldade *
                    </label>
                    <select
                      name="nivel_dificuldade"
                      required
                      value={formData.nivel_dificuldade}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Selecione</option>
                      <option value="Iniciante">Iniciante</option>
                      <option value="Intermediário">Intermediário</option>
                      <option value="Avançado">Avançado</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Duração Estimada (minutos) *
                    </label>
                    <input
                      type="number"
                      name="duracao_estimada"
                      required
                      min="15"
                      max="180"
                      value={formData.duracao_estimada}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Descrição
                  </label>
                  <textarea
                    name="descricao"
                    rows={3}
                    value={formData.descricao}
                    onChange={handleChange}
                    placeholder="Descreva os objetivos e características do treino..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Exercícios</h3>
                  <button
                    type="button"
                    onClick={addExercise}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Adicionar Exercício
                  </button>
                </div>

                {formData.exercicios.length === 0 ? (
                  <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                    <Activity className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum exercício adicionado</h3>
                    <p className="mt-1 text-sm text-gray-500">Comece adicionando exercícios ao treino.</p>
                    <div className="mt-6">
                      <button
                        type="button"
                        onClick={addExercise}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Primeiro Exercício
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {formData.exercicios.map((exercise, index) => (
                      <div key={exercise.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-center mb-3">
                          <h4 className="text-sm font-medium text-gray-900">Exercício {index + 1}</h4>
                          <button
                            type="button"
                            onClick={() => removeExercise(exercise.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div className="md:col-span-2">
                            <input
                              type="text"
                              placeholder="Nome do exercício"
                              value={exercise.nome}
                              onChange={(e) => updateExercise(exercise.id, 'nome', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <input
                              type="number"
                              placeholder="Séries"
                              min="1"
                              value={exercise.series}
                              onChange={(e) => updateExercise(exercise.id, 'series', parseInt(e.target.value))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <input
                              type="text"
                              placeholder="Repetições"
                              value={exercise.repeticoes}
                              onChange={(e) => updateExercise(exercise.id, 'repeticoes', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <input
                              type="text"
                              placeholder="Peso/Carga"
                              value={exercise.peso}
                              onChange={(e) => updateExercise(exercise.id, 'peso', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <input
                              type="text"
                              placeholder="Descanso (segundos)"
                              value={exercise.descanso}
                              onChange={(e) => updateExercise(exercise.id, 'descanso', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-4 pt-6 border-t">
                <Link
                  href={patient ? `/personal/patients/${patient}` : '/personal/workouts'}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancelar
                </Link>
                <button
                  type="submit"
                  disabled={loading || formData.exercicios.length === 0}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {loading ? 'Salvando...' : 'Salvar Treino'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  )
}
