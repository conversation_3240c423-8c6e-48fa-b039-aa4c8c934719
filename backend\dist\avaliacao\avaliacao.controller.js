"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AvaliacaoController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const avaliacao_service_1 = require("./avaliacao.service");
const create_avaliacao_composicao_dto_1 = require("./dto/create-avaliacao-composicao.dto");
const create_avaliacao_energetica_dto_1 = require("./dto/create-avaliacao-energetica.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
let AvaliacaoController = class AvaliacaoController {
    avaliacaoService;
    constructor(avaliacaoService) {
        this.avaliacaoService = avaliacaoService;
    }
    createComposicao(createAvaliacaoDto, req) {
        return this.avaliacaoService.createAvaliacaoComposicao(createAvaliacaoDto, req.user.id);
    }
    createEnergetica(avaliacaoComposicaoId, createAvaliacaoEnergeticaDto, req) {
        return this.avaliacaoService.createAvaliacaoEnergetica(avaliacaoComposicaoId, createAvaliacaoEnergeticaDto, req.user.id);
    }
    findByAluno(alunoId, req) {
        const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
        return this.avaliacaoService.findAvaliacoesByAluno(alunoId, personalId);
    }
    findOne(id, req) {
        const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
        return this.avaliacaoService.findAvaliacaoById(id, personalId);
    }
};
exports.AvaliacaoController = AvaliacaoController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Criar avaliação de composição corporal (apenas Personal Trainer)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Avaliação criada com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Post)('composicao'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_avaliacao_composicao_dto_1.CreateAvaliacaoComposicaoDto, Object]),
    __metadata("design:returntype", void 0)
], AvaliacaoController.prototype, "createComposicao", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Criar avaliação energética (apenas Personal Trainer)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Avaliação energética criada com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Post)('composicao/:id/energetica'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_avaliacao_energetica_dto_1.CreateAvaliacaoEnergeticaDto, Object]),
    __metadata("design:returntype", void 0)
], AvaliacaoController.prototype, "createEnergetica", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar avaliações por aluno' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Avaliações encontradas' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, common_1.Get)('aluno/:alunoId'),
    __param(0, (0, common_1.Param)('alunoId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AvaliacaoController.prototype, "findByAluno", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar avaliação por ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Avaliação encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Avaliação não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AvaliacaoController.prototype, "findOne", null);
exports.AvaliacaoController = AvaliacaoController = __decorate([
    (0, swagger_1.ApiTags)('avaliacoes'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('avaliacoes'),
    __metadata("design:paramtypes", [avaliacao_service_1.AvaliacaoService])
], AvaliacaoController);
//# sourceMappingURL=avaliacao.controller.js.map