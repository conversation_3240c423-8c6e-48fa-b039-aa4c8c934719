import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAvaliacaoComposicaoDto } from './dto/create-avaliacao-composicao.dto';
import { UpdateAvaliacaoComposicaoDto } from './dto/update-avaliacao-composicao.dto';
import { CreateAvaliacaoEnergeticaDto } from './dto/create-avaliacao-energetica.dto';

@Injectable()
export class AvaliacaoService {
  constructor(private prisma: PrismaService) {}

  // Métodos de cálculo de IMC
  private calcularIMC(peso: number, altura: number): { valor: number; categoria: string } {
    const alturaMetros = altura / 100;
    const imc = peso / (alturaMetros * alturaMetros);

    let categoria: string;
    if (imc < 18.5) categoria = 'Abaixo do peso';
    else if (imc < 25) categoria = 'Peso normal';
    else if (imc < 30) categoria = 'Sobrepeso';
    else if (imc < 35) categoria = 'Obesidade grau I';
    else if (imc < 40) categoria = 'Obesidade grau II';
    else categoria = 'Obesidade grau III';

    return { valor: parseFloat(imc.toFixed(2)), categoria };
  }

  // Método Guedes para cálculo de % de gordura
  private calcularGorduraGuedes(
    dobras: { subescapular?: number; tricipital?: number; suprailíaca?: number; abdominal?: number },
    idade: number,
    sexo: 'M' | 'F'
  ) {
    const { subescapular = 0, tricipital = 0, suprailíaca = 0, abdominal = 0 } = dobras;
    const somaDobras = subescapular + tricipital + suprailíaca + abdominal;

    let densidade: number;
    if (sexo === 'M') {
      densidade = 1.1714 - (0.0671 * Math.log10(somaDobras));
    } else {
      densidade = 1.1549 - (0.0678 * Math.log10(somaDobras));
    }

    const percentualGordura = ((4.95 / densidade) - 4.5) * 100;

    return {
      densidade: parseFloat(densidade.toFixed(4)),
      percentual: parseFloat(percentualGordura.toFixed(2)),
    };
  }

  // Método Pollock 3 dobras
  private calcularGorduraPollock3(
    dobras: { tricipital?: number; suprailíaca?: number; coxa?: number },
    idade: number,
    sexo: 'M' | 'F'
  ) {
    const { tricipital = 0, suprailíaca = 0, coxa = 0 } = dobras;
    const somaDobras = tricipital + suprailíaca + coxa;

    let densidade: number;
    if (sexo === 'M') {
      densidade = 1.1125025 - (0.0013125 * somaDobras) + (0.0000055 * Math.pow(somaDobras, 2)) - (0.000244 * idade);
    } else {
      densidade = 1.0994921 - (0.0009929 * somaDobras) + (0.0000023 * Math.pow(somaDobras, 2)) - (0.0001392 * idade);
    }

    const percentualGordura = ((4.95 / densidade) - 4.5) * 100;

    return {
      densidade: parseFloat(densidade.toFixed(4)),
      percentual: parseFloat(percentualGordura.toFixed(2)),
    };
  }

  // Método Pollock 7 dobras
  private calcularGorduraPollock7(
    dobras: {
      subescapular?: number;
      tricipital?: number;
      toracica?: number;
      axilarMedia?: number;
      suprailíaca?: number;
      abdominal?: number;
      coxa?: number
    },
    idade: number,
    sexo: 'M' | 'F'
  ) {
    const {
      subescapular = 0,
      tricipital = 0,
      toracica = 0,
      axilarMedia = 0,
      suprailíaca = 0,
      abdominal = 0,
      coxa = 0
    } = dobras;

    const somaDobras = subescapular + tricipital + toracica + axilarMedia + suprailíaca + abdominal + coxa;

    let densidade: number;
    if (sexo === 'M') {
      densidade = 1.112 - (0.00043499 * somaDobras) + (0.00000055 * Math.pow(somaDobras, 2)) - (0.00028826 * idade);
    } else {
      densidade = 1.097 - (0.00046971 * somaDobras) + (0.00000056 * Math.pow(somaDobras, 2)) - (0.00012828 * idade);
    }

    const percentualGordura = ((4.95 / densidade) - 4.5) * 100;

    return {
      densidade: parseFloat(densidade.toFixed(4)),
      percentual: parseFloat(percentualGordura.toFixed(2)),
    };
  }

  // Calcular massa gorda e magra
  private calcularMassas(peso: number, percentualGordura: number) {
    const massaGordura = (peso * percentualGordura) / 100;
    const massaMagro = peso - massaGordura;

    return {
      massaGordura: parseFloat(massaGordura.toFixed(2)),
      massaMagro: parseFloat(massaMagro.toFixed(2)),
    };
  }

  async createAvaliacaoComposicao(
    createAvaliacaoDto: CreateAvaliacaoComposicaoDto,
    personalId: string
  ) {
    // Verificar se o aluno pertence ao personal
    const aluno = await this.prisma.aluno.findUnique({
      where: { id: createAvaliacaoDto.alunoId },
    });

    if (!aluno) {
      throw new NotFoundException('Aluno não encontrado');
    }

    if (aluno.personalId !== personalId) {
      throw new ForbiddenException('Acesso negado a este aluno');
    }

    // Calcular soma das dobras
    const dobras = {
      subescapular: createAvaliacaoDto.subescapular || 0,
      tricipital: createAvaliacaoDto.tricipital || 0,
      bicipital: createAvaliacaoDto.bicipital || 0,
      toracica: createAvaliacaoDto.toracica || 0,
      abdominal: createAvaliacaoDto.abdominal || 0,
      axilarMedia: createAvaliacaoDto.axilarMedia || 0,
      suprailíaca: createAvaliacaoDto.suprailíaca || 0,
      coxa: createAvaliacaoDto.coxa || 0,
      panturrilha: createAvaliacaoDto.panturrilha || 0,
    };

    const somaDobras = Object.values(dobras).reduce((sum, value) => sum + value, 0);

    // Calcular IMC
    const imc = this.calcularIMC(aluno.pesoAtual, aluno.estatura);

    // Assumindo sexo masculino por padrão (deveria ser um campo no aluno)
    const sexo: 'M' | 'F' = 'M'; // TODO: Adicionar campo sexo no modelo Aluno

    // Calcular % de gordura pelos diferentes métodos
    const guedes = this.calcularGorduraGuedes(dobras, aluno.idade, sexo);
    const guedesMassas = this.calcularMassas(aluno.pesoAtual, guedes.percentual);

    const pollock3 = this.calcularGorduraPollock3(dobras, aluno.idade, sexo);
    const pollock3Massas = this.calcularMassas(aluno.pesoAtual, pollock3.percentual);

    const pollock7 = this.calcularGorduraPollock7(dobras, aluno.idade, sexo);
    const pollock7Massas = this.calcularMassas(aluno.pesoAtual, pollock7.percentual);

    // Criar avaliação com cálculos automáticos
    const avaliacao = await this.prisma.avaliacaoComposicao.create({
      data: {
        ...createAvaliacaoDto,
        personalId,
        somaDobras,
        valorIMC: imc.valor,
        categoriaIMC: imc.categoria,

        // Guedes
        guedesDensidade: guedes.densidade,
        guedesPercentual: guedes.percentual,
        guedesMassaGordura: guedesMassas.massaGordura,
        guedesMassaMagro: guedesMassas.massaMagro,

        // Pollock 3
        pollock3Densidade: pollock3.densidade,
        pollock3Percentual: pollock3.percentual,
        pollock3MassaGordura: pollock3Massas.massaGordura,
        pollock3MassaMagro: pollock3Massas.massaMagro,

        // Pollock 7
        pollock7Densidade: pollock7.densidade,
        pollock7Percentual: pollock7.percentual,
        pollock7MassaGordura: pollock7Massas.massaGordura,
        pollock7MassaMagro: pollock7Massas.massaMagro,
      },
      include: {
        aluno: {
          select: {
            nome: true,
            pesoAtual: true,
            estatura: true,
            idade: true,
          },
        },
      },
    });

    return avaliacao;
  }

  // Métodos de cálculo de TMB (Taxa Metabólica Basal)
  private calcularTMB(peso: number, altura: number, idade: number, sexo: 'M' | 'F') {
    const alturaMetros = altura / 100;

    // FAO/OMS
    let faoOMS: number;
    if (sexo === 'M') {
      if (idade >= 18 && idade <= 30) faoOMS = (15.3 * peso) + 679;
      else if (idade >= 31 && idade <= 60) faoOMS = (11.6 * peso) + 879;
      else faoOMS = (13.5 * peso) + 487;
    } else {
      if (idade >= 18 && idade <= 30) faoOMS = (14.7 * peso) + 496;
      else if (idade >= 31 && idade <= 60) faoOMS = (8.7 * peso) + 829;
      else faoOMS = (10.5 * peso) + 596;
    }

    // Harris-Benedict
    let harrisBenedict: number;
    if (sexo === 'M') {
      harrisBenedict = 88.362 + (13.397 * peso) + (4.799 * altura) - (5.677 * idade);
    } else {
      harrisBenedict = 447.593 + (9.247 * peso) + (3.098 * altura) - (4.330 * idade);
    }

    // Mifflin-St Jeor
    let mifflin: number;
    if (sexo === 'M') {
      mifflin = (10 * peso) + (6.25 * altura) - (5 * idade) + 5;
    } else {
      mifflin = (10 * peso) + (6.25 * altura) - (5 * idade) - 161;
    }

    // Cunningham (requer % de gordura para calcular massa magra)
    // Usando uma estimativa padrão de 15% para homens e 25% para mulheres
    const percentualGorduraEstimado = sexo === 'M' ? 15 : 25;
    const massaMagra = peso * (1 - percentualGorduraEstimado / 100);
    const cunningham = 500 + (22 * massaMagra);

    // Tinsley MLG (Massa Livre de Gordura)
    const tinsleyMLG = 25.9 * massaMagra + 284;

    // Tinsley Peso
    let tinsleyPeso: number;
    if (sexo === 'M') {
      tinsleyPeso = (10 * peso) + (6.25 * altura) - (5 * idade) + 5;
    } else {
      tinsleyPeso = (10 * peso) + (6.25 * altura) - (5 * idade) - 161;
    }

    return {
      faoOMS: parseFloat(faoOMS.toFixed(0)),
      harrisBenedict: parseFloat(harrisBenedict.toFixed(0)),
      mifflin: parseFloat(mifflin.toFixed(0)),
      cunningham: parseFloat(cunningham.toFixed(0)),
      tinsleyMLG: parseFloat(tinsleyMLG.toFixed(0)),
      tinsleyPeso: parseFloat(tinsleyPeso.toFixed(0)),
    };
  }

  async createAvaliacaoEnergetica(
    avaliacaoComposicaoId: string,
    createAvaliacaoEnergeticaDto: CreateAvaliacaoEnergeticaDto,
    personalId: string
  ) {
    // Verificar se a avaliação de composição existe e pertence ao personal
    const avaliacaoComposicao = await this.prisma.avaliacaoComposicao.findUnique({
      where: { id: avaliacaoComposicaoId },
      include: {
        aluno: true,
      },
    });

    if (!avaliacaoComposicao) {
      throw new NotFoundException('Avaliação de composição não encontrada');
    }

    if (avaliacaoComposicao.personalId !== personalId) {
      throw new ForbiddenException('Acesso negado a esta avaliação');
    }

    // Calcular TMB pelos diferentes métodos
    const sexo: 'M' | 'F' = 'M'; // TODO: Adicionar campo sexo no modelo Aluno
    const tmb = this.calcularTMB(
      avaliacaoComposicao.aluno.pesoAtual,
      avaliacaoComposicao.aluno.estatura,
      avaliacaoComposicao.aluno.idade,
      sexo
    );

    // Calcular GET (Gasto Energético Total)
    const fatorAtividade = createAvaliacaoEnergeticaDto.fatorAtividade || 1.4;
    const tmbSelecionado = createAvaliacaoEnergeticaDto.tmbMifflin || tmb.mifflin;
    const valorGET = tmbSelecionado * fatorAtividade;

    // Calcular Consumo (com déficit/superávit)
    const percentualDeficitSuperavit = createAvaliacaoEnergeticaDto.percentualDeficitSuperavit || 0;
    const valorConsumo = valorGET * (1 + percentualDeficitSuperavit / 100);

    const avaliacaoEnergetica = await this.prisma.avaliacaoEnergetica.create({
      data: {
        avaliacaoComposicaoId,
        tmbFAO_OMS: tmb.faoOMS,
        tmbHarrisBenedict: tmb.harrisBenedict,
        tmbMifflin: tmb.mifflin,
        tmbCunningham: tmb.cunningham,
        tmbTinsleyMLG: tmb.tinsleyMLG,
        tmbTinsleyPeso: tmb.tinsleyPeso,
        fatorAtividade,
        valorGET: parseFloat(valorGET.toFixed(0)),
        percentualDeficitSuperavit,
        valorConsumo: parseFloat(valorConsumo.toFixed(0)),
      },
    });

    return avaliacaoEnergetica;
  }

  async findAvaliacoesByAluno(alunoId: string, personalId?: string) {
    // Verificar se o aluno pertence ao personal (se personalId fornecido)
    if (personalId) {
      const aluno = await this.prisma.aluno.findUnique({
        where: { id: alunoId },
      });

      if (!aluno || aluno.personalId !== personalId) {
        throw new ForbiddenException('Acesso negado a este aluno');
      }
    }

    return this.prisma.avaliacaoComposicao.findMany({
      where: { alunoId },
      include: {
        avaliacaoEnergetica: true,
        aluno: {
          select: {
            nome: true,
            pesoAtual: true,
            estatura: true,
            idade: true,
          },
        },
      },
      orderBy: {
        dataAvaliacao: 'desc',
      },
    });
  }

  async findAvaliacaoById(id: string, personalId?: string) {
    const avaliacao = await this.prisma.avaliacaoComposicao.findUnique({
      where: { id },
      include: {
        avaliacaoEnergetica: true,
        aluno: {
          select: {
            nome: true,
            pesoAtual: true,
            estatura: true,
            idade: true,
          },
        },
        volumeLoads: {
          orderBy: [
            { exercicio: 'asc' },
            { semana: 'asc' },
          ],
        },
      },
    });

    if (!avaliacao) {
      throw new NotFoundException('Avaliação não encontrada');
    }

    if (personalId && avaliacao.personalId !== personalId) {
      throw new ForbiddenException('Acesso negado a esta avaliação');
    }

    return avaliacao;
  }
}
