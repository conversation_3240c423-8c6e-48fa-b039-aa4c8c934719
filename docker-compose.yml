version: '3.8'

services:
  # Banco de dados PostgreSQL
  postgres:
    image: postgres:15
    container_name: hypatium_postgres
    environment:
      POSTGRES_USER: hypatium
      POSTGRES_PASSWORD: hypatium123
      POSTGRES_DB: hypatium
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - hypatium_network

  # Backend FastAPI
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: hypatium_backend
    environment:
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=hypatium
      - POSTGRES_PASSWORD=hypatium123
      - POSTGRES_DB=hypatium
      - POSTGRES_PORT=5432
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
    networks:
      - hypatium_network
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Frontend Next.js
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: hypatium_frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - hypatium_network
    command: npm run dev

volumes:
  postgres_data:

networks:
  hypatium_network:
    driver: bridge
