"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VolumeLoadController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const volume_load_service_1 = require("./volume-load.service");
const create_volume_load_dto_1 = require("./dto/create-volume-load.dto");
const update_volume_load_dto_1 = require("./dto/update-volume-load.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
let VolumeLoadController = class VolumeLoadController {
    volumeLoadService;
    constructor(volumeLoadService) {
        this.volumeLoadService = volumeLoadService;
    }
    create(createVolumeLoadDto, req) {
        return this.volumeLoadService.create(createVolumeLoadDto, req.user.id);
    }
    findByAvaliacao(avaliacaoId, req) {
        const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
        return this.volumeLoadService.findByAvaliacao(avaliacaoId, personalId);
    }
    getStats(avaliacaoId, req) {
        const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
        return this.volumeLoadService.getVolumeLoadStats(avaliacaoId, personalId);
    }
    update(id, updateVolumeLoadDto, req) {
        return this.volumeLoadService.update(id, updateVolumeLoadDto, req.user.id);
    }
    remove(id, req) {
        return this.volumeLoadService.remove(id, req.user.id);
    }
};
exports.VolumeLoadController = VolumeLoadController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Criar registro de Volume Load (apenas Personal Trainer)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Volume Load criado com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_volume_load_dto_1.CreateVolumeLoadDto, Object]),
    __metadata("design:returntype", void 0)
], VolumeLoadController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Volume Loads por avaliação' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Volume Loads encontrados' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, common_1.Get)('avaliacao/:avaliacaoId'),
    __param(0, (0, common_1.Param)('avaliacaoId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], VolumeLoadController.prototype, "findByAvaliacao", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar estatísticas de Volume Load por avaliação' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Estatísticas encontradas' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, common_1.Get)('avaliacao/:avaliacaoId/stats'),
    __param(0, (0, common_1.Param)('avaliacaoId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], VolumeLoadController.prototype, "getStats", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Volume Load (apenas Personal Trainer)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Volume Load atualizado com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Volume Load não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_volume_load_dto_1.UpdateVolumeLoadDto, Object]),
    __metadata("design:returntype", void 0)
], VolumeLoadController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Remover Volume Load (apenas Personal Trainer)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Volume Load removido com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Volume Load não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], VolumeLoadController.prototype, "remove", null);
exports.VolumeLoadController = VolumeLoadController = __decorate([
    (0, swagger_1.ApiTags)('volume-load'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('volume-load'),
    __metadata("design:paramtypes", [volume_load_service_1.VolumeLoadService])
], VolumeLoadController);
//# sourceMappingURL=volume-load.controller.js.map