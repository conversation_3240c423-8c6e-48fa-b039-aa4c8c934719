import { PrismaService } from '../prisma/prisma.service';
import { CreateAvaliacaoComposicaoDto } from './dto/create-avaliacao-composicao.dto';
import { CreateAvaliacaoEnergeticaDto } from './dto/create-avaliacao-energetica.dto';
export declare class AvaliacaoService {
    private prisma;
    constructor(prisma: PrismaService);
    private calcularIMC;
    private calcularGorduraGuedes;
    private calcularGorduraPollock3;
    private calcularGorduraPollock7;
    private calcularMassas;
    createAvaliacaoComposicao(createAvaliacaoDto: CreateAvaliacaoComposicaoDto, personalId: string): Promise<any>;
    private calcularTMB;
    createAvaliacaoEnergetica(avaliacaoComposicaoId: string, createAvaliacaoEnergeticaDto: CreateAvaliacaoEnergeticaDto, personalId: string): Promise<any>;
    findAvaliacoesByAluno(alunoId: string, personalId?: string): Promise<any>;
    findAvaliacaoById(id: string, personalId?: string): Promise<any>;
}
