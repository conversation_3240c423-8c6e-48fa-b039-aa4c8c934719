"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonalTrainerController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const personal_trainer_service_1 = require("./personal-trainer.service");
const update_personal_trainer_dto_1 = require("./dto/update-personal-trainer.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
let PersonalTrainerController = class PersonalTrainerController {
    personalTrainerService;
    constructor(personalTrainerService) {
        this.personalTrainerService = personalTrainerService;
    }
    findProfile(req) {
        return this.personalTrainerService.findOne(req.user.id);
    }
    findOne(id) {
        return this.personalTrainerService.findOne(id);
    }
    update(id, updatePersonalTrainerDto) {
        return this.personalTrainerService.update(id, updatePersonalTrainerDto);
    }
    getDashboardStats(req) {
        return this.personalTrainerService.getDashboardStats(req.user.id);
    }
};
exports.PersonalTrainerController = PersonalTrainerController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar perfil do Personal Trainer' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Perfil encontrado com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Personal Trainer não encontrado' }),
    (0, common_1.Get)('profile'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], PersonalTrainerController.prototype, "findProfile", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Personal Trainer por ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Personal Trainer encontrado com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Personal Trainer não encontrado' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PersonalTrainerController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar dados do Personal Trainer' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Personal Trainer atualizado com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Personal Trainer não encontrado' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_personal_trainer_dto_1.UpdatePersonalTrainerDto]),
    __metadata("design:returntype", void 0)
], PersonalTrainerController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar estatísticas do dashboard' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Estatísticas encontradas com sucesso' }),
    (0, common_1.Get)('dashboard/stats'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], PersonalTrainerController.prototype, "getDashboardStats", null);
exports.PersonalTrainerController = PersonalTrainerController = __decorate([
    (0, swagger_1.ApiTags)('personal-trainer'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Controller)('personal-trainer'),
    __metadata("design:paramtypes", [personal_trainer_service_1.PersonalTrainerService])
], PersonalTrainerController);
//# sourceMappingURL=personal-trainer.controller.js.map