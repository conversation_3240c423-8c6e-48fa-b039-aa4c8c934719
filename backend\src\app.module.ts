import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { PersonalTrainerModule } from './personal-trainer/personal-trainer.module';
import { AlunoModule } from './aluno/aluno.module';
import { AvaliacaoModule } from './avaliacao/avaliacao.module';
import { RelatorioModule } from './relatorio/relatorio.module';
import { ChatModule } from './chat/chat.module';
import { VolumeLoadModule } from './volume-load/volume-load.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    PrismaModule,
    AuthModule,
    PersonalTrainerModule,
    AlunoModule,
    AvaliacaoModule,
    VolumeLoadModule,
    RelatorioModule,
    ChatModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
