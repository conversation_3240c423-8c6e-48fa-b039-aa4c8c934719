"""
Configuração do banco de dados PostgreSQL
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

# Criar engine do SQLAlchemy
database_url = getattr(settings, 'DATABASE_URL', getattr(settings, 'SQLALCHEMY_DATABASE_URI', 'sqlite:///./hypatium.db'))

if database_url.startswith("sqlite"):
    engine = create_engine(
        database_url,
        connect_args={"check_same_thread": False},
        echo=False  # Mudar para True para debug SQL
    )
else:
    engine = create_engine(
        database_url,
        pool_pre_ping=True,
        echo=False  # Mudar para True para debug SQL
    )

# Criar SessionLocal
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base para modelos
Base = declarative_base()

# Dependency para obter sessão do banco
def get_db():
    """Dependency para obter sessão do banco de dados"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
