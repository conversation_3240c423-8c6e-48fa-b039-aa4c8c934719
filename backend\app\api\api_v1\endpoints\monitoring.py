"""
Endpoints de monitoramento e saúde do sistema
"""

from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.models.models import User
from app.core.monitoring import system_monitor, health_checker, get_system_status

router = APIRouter()

@router.get("/health")
def health_check(
    *,
    db: Session = Depends(get_db),
) -> Any:
    """
    Health check básico - público
    """
    try:
        # Teste simples de conexão com banco
        db.execute("SELECT 1")
        
        return {
            "status": "healthy",
            "message": "System is operational",
            "timestamp": system_monitor.get_system_metrics()["timestamp"]
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"System unhealthy: {str(e)}")

@router.get("/health/detailed")
def detailed_health_check(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Health check detalhado - requer autenticação
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Acesso restrito a administradores")
    
    return health_checker.run_health_checks(db)

@router.get("/metrics/system")
def get_system_metrics(
    *,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Métricas do sistema (CPU, memória, disco)
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Acesso restrito a administradores")
    
    return system_monitor.get_system_metrics()

@router.get("/metrics/database")
def get_database_metrics(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Métricas do banco de dados
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Acesso restrito a administradores")
    
    return system_monitor.get_database_metrics(db)

@router.get("/metrics/application")
def get_application_metrics(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Métricas da aplicação
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Acesso restrito a administradores")
    
    return system_monitor.get_application_metrics(db)

@router.get("/status")
def get_full_system_status(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Status completo do sistema
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Acesso restrito a administradores")
    
    return get_system_status(db)

@router.get("/logs/performance")
def get_performance_logs(
    *,
    lines: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Logs de performance
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Acesso restrito a administradores")
    
    try:
        import os
        log_file = "logs/performance.log"
        
        if not os.path.exists(log_file):
            return {"logs": [], "message": "Log file not found"}
        
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        return {
            "logs": [line.strip() for line in recent_lines],
            "total_lines": len(all_lines),
            "showing_lines": len(recent_lines)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao ler logs: {str(e)}")

@router.get("/logs/application")
def get_application_logs(
    *,
    lines: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Logs da aplicação
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Acesso restrito a administradores")
    
    try:
        import os
        log_file = "logs/hypatium.log"
        
        if not os.path.exists(log_file):
            return {"logs": [], "message": "Log file not found"}
        
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        return {
            "logs": [line.strip() for line in recent_lines],
            "total_lines": len(all_lines),
            "showing_lines": len(recent_lines)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao ler logs: {str(e)}")

@router.post("/maintenance/cleanup")
def run_maintenance_cleanup(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Executar limpeza de manutenção
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Acesso restrito a administradores")
    
    try:
        from app.services.pdf_service import PDFService
        import os
        
        results = {
            "timestamp": system_monitor.get_system_metrics()["timestamp"],
            "actions": []
        }
        
        # Limpar PDFs antigos
        pdf_service = PDFService()
        pdf_cleanup = pdf_service.cleanup_old_pdfs(days_old=30)
        results["actions"].append({
            "action": "cleanup_old_pdfs",
            "deleted_count": pdf_cleanup["deleted_count"],
            "errors": pdf_cleanup["errors"]
        })
        
        # Limpar logs antigos (manter últimos 7 dias)
        log_files = ["logs/hypatium.log", "logs/performance.log"]
        for log_file in log_files:
            if os.path.exists(log_file):
                # Rotacionar log se muito grande (>100MB)
                file_size = os.path.getsize(log_file)
                if file_size > 100 * 1024 * 1024:  # 100MB
                    backup_name = f"{log_file}.backup"
                    os.rename(log_file, backup_name)
                    results["actions"].append({
                        "action": f"rotate_log_{log_file}",
                        "old_size_mb": round(file_size / (1024*1024), 2)
                    })
        
        # Vacuum do banco de dados
        try:
            db.execute("VACUUM ANALYZE")
            results["actions"].append({
                "action": "database_vacuum",
                "status": "completed"
            })
        except Exception as e:
            results["actions"].append({
                "action": "database_vacuum",
                "status": "failed",
                "error": str(e)
            })
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro na manutenção: {str(e)}")

@router.get("/info")
def get_system_info(
    *,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Informações gerais do sistema
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Acesso restrito a administradores")
    
    import platform
    import sys
    from app.core.config import settings
    
    return {
        "application": {
            "name": settings.PROJECT_NAME,
            "version": getattr(settings, "VERSION", "1.0.0"),
            "environment": getattr(settings, "ENVIRONMENT", "development")
        },
        "system": {
            "platform": platform.platform(),
            "python_version": sys.version,
            "architecture": platform.architecture()[0]
        },
        "runtime": {
            "uptime_hours": round((system_monitor.get_system_metrics()["uptime"]["hours"]), 2),
            "process_id": os.getpid()
        }
    }
