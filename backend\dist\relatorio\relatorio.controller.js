"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RelatorioController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const relatorio_service_1 = require("./relatorio.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
let RelatorioController = class RelatorioController {
    relatorioService;
    constructor(relatorioService) {
        this.relatorioService = relatorioService;
    }
    gerarRelatorio(avaliacaoId, req) {
        return this.relatorioService.gerarRelatorio(avaliacaoId, req.user.id);
    }
    gerarRelatorioVolumeLoad(avaliacaoId, req) {
        return this.relatorioService.gerarRelatorioVolumeLoad(avaliacaoId, req.user.id);
    }
    findByAluno(alunoId, req) {
        const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
        return this.relatorioService.findRelatoriosByAluno(alunoId, personalId);
    }
    findOne(id, req) {
        const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
        return this.relatorioService.findRelatorioById(id, personalId);
    }
};
exports.RelatorioController = RelatorioController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Gerar relatório de avaliação física (apenas Personal Trainer)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Relatório gerado com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Avaliação não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Post)('avaliacao/:avaliacaoId'),
    __param(0, (0, common_1.Param)('avaliacaoId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], RelatorioController.prototype, "gerarRelatorio", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Gerar relatório de Volume Load (apenas Personal Trainer)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Relatório de Volume Load gerado' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Avaliação não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Post)('volume-load/:avaliacaoId'),
    __param(0, (0, common_1.Param)('avaliacaoId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], RelatorioController.prototype, "gerarRelatorioVolumeLoad", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar relatórios por aluno' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Relatórios encontrados' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, common_1.Get)('aluno/:alunoId'),
    __param(0, (0, common_1.Param)('alunoId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], RelatorioController.prototype, "findByAluno", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar relatório por ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Relatório encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Relatório não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], RelatorioController.prototype, "findOne", null);
exports.RelatorioController = RelatorioController = __decorate([
    (0, swagger_1.ApiTags)('relatorios'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('relatorios'),
    __metadata("design:paramtypes", [relatorio_service_1.RelatorioService])
], RelatorioController);
//# sourceMappingURL=relatorio.controller.js.map