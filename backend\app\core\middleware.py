"""
Middleware personalizado para logging e monitoramento
"""

import time
import json
from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.monitoring import performance_logger

class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware para logging de performance"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Registrar início da requisição
        start_time = time.time()
        
        # Obter informações da requisição
        method = request.method
        path = request.url.path
        user_id = None
        
        # Tentar obter user_id do token (se disponível)
        try:
            if hasattr(request.state, 'user'):
                user_id = request.state.user.id
        except:
            pass
        
        # Processar requisição
        try:
            response = await call_next(request)
            
            # Calcular duração
            duration = time.time() - start_time
            
            # Log da requisição
            performance_logger.log_request(
                method=method,
                path=path,
                duration=duration,
                status_code=response.status_code,
                user_id=user_id
            )
            
            # Adicionar headers de performance
            response.headers["X-Process-Time"] = str(round(duration * 1000, 2))
            
            return response
            
        except Exception as e:
            # Log de erro
            duration = time.time() - start_time
            performance_logger.log_request(
                method=method,
                path=path,
                duration=duration,
                status_code=500,
                user_id=user_id
            )
            
            # Retornar erro estruturado
            return JSONResponse(
                status_code=500,
                content={
                    "detail": "Internal server error",
                    "error_type": type(e).__name__,
                    "timestamp": time.time()
                }
            )

class SecurityMiddleware(BaseHTTPMiddleware):
    """Middleware de segurança"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Headers de segurança
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # CSP para produção
        if hasattr(request.app.state, 'environment') and request.app.state.environment == 'production':
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self' https:; "
                "frame-ancestors 'none';"
            )
        
        return response

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware básico de rate limiting"""
    
    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Obter IP do cliente
        client_ip = request.client.host
        current_time = time.time()
        
        # Limpar entradas antigas
        self.clients = {
            ip: requests for ip, requests in self.clients.items()
            if any(req_time > current_time - self.period for req_time in requests)
        }
        
        # Verificar rate limit
        if client_ip in self.clients:
            # Filtrar requisições dentro do período
            recent_requests = [
                req_time for req_time in self.clients[client_ip]
                if req_time > current_time - self.period
            ]
            
            if len(recent_requests) >= self.calls:
                return JSONResponse(
                    status_code=429,
                    content={
                        "detail": "Rate limit exceeded",
                        "retry_after": self.period
                    }
                )
            
            self.clients[client_ip] = recent_requests + [current_time]
        else:
            self.clients[client_ip] = [current_time]
        
        return await call_next(request)

class CORSMiddleware(BaseHTTPMiddleware):
    """Middleware CORS personalizado"""
    
    def __init__(self, app, allow_origins: list = None, allow_methods: list = None):
        super().__init__(app)
        self.allow_origins = allow_origins or ["*"]
        self.allow_methods = allow_methods or ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Verificar se é preflight request
        if request.method == "OPTIONS":
            response = Response()
        else:
            response = await call_next(request)
        
        # Adicionar headers CORS
        origin = request.headers.get("origin")
        if origin and (origin in self.allow_origins or "*" in self.allow_origins):
            response.headers["Access-Control-Allow-Origin"] = origin
        
        response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
        response.headers["Access-Control-Allow-Headers"] = (
            "Authorization, Content-Type, X-Requested-With, Accept, Origin"
        )
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Max-Age"] = "86400"
        
        return response

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware para logging detalhado de requisições"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        import logging
        
        logger = logging.getLogger("requests")
        
        # Log da requisição
        request_info = {
            "method": request.method,
            "url": str(request.url),
            "headers": dict(request.headers),
            "client": request.client.host if request.client else None,
            "timestamp": time.time()
        }
        
        # Remover headers sensíveis do log
        sensitive_headers = ["authorization", "cookie", "x-api-key"]
        for header in sensitive_headers:
            if header in request_info["headers"]:
                request_info["headers"][header] = "[REDACTED]"
        
        logger.info(f"REQUEST: {json.dumps(request_info)}")
        
        # Processar requisição
        start_time = time.time()
        response = await call_next(request)
        duration = time.time() - start_time
        
        # Log da resposta
        response_info = {
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2),
            "response_size": len(response.body) if hasattr(response, 'body') else 0,
            "timestamp": time.time()
        }
        
        logger.info(f"RESPONSE: {json.dumps(response_info)}")
        
        return response

class HealthCheckMiddleware(BaseHTTPMiddleware):
    """Middleware para health checks automáticos"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Verificar se é um health check
        if request.url.path in ["/health", "/monitoring/health"]:
            # Adicionar informações básicas de saúde
            response = await call_next(request)
            
            if response.status_code == 200:
                response.headers["X-Health-Status"] = "healthy"
                response.headers["X-Health-Timestamp"] = str(int(time.time()))
            
            return response
        
        return await call_next(request)

# Função para configurar todos os middlewares
def setup_middlewares(app):
    """Configurar todos os middlewares na aplicação"""
    
    # Middleware de health check (primeiro)
    app.add_middleware(HealthCheckMiddleware)
    
    # Middleware de segurança
    app.add_middleware(SecurityMiddleware)
    
    # Middleware de performance (para logging)
    app.add_middleware(PerformanceMiddleware)
    
    # Rate limiting básico (apenas em produção)
    import os
    if os.getenv("ENVIRONMENT") == "production":
        app.add_middleware(RateLimitMiddleware, calls=100, period=60)
    
    # Request logging (apenas em desenvolvimento/debug)
    if os.getenv("DEBUG", "false").lower() == "true":
        app.add_middleware(RequestLoggingMiddleware)
    
    return app
