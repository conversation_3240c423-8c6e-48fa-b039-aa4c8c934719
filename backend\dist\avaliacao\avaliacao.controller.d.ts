import { AvaliacaoService } from './avaliacao.service';
import { CreateAvaliacaoComposicaoDto } from './dto/create-avaliacao-composicao.dto';
import { CreateAvaliacaoEnergeticaDto } from './dto/create-avaliacao-energetica.dto';
export declare class AvaliacaoController {
    private readonly avaliacaoService;
    constructor(avaliacaoService: AvaliacaoService);
    createComposicao(createAvaliacaoDto: CreateAvaliacaoComposicaoDto, req: any): Promise<any>;
    createEnergetica(avaliacaoComposicaoId: string, createAvaliacaoEnergeticaDto: CreateAvaliacaoEnergeticaDto, req: any): Promise<any>;
    findByAluno(alunoId: string, req: any): Promise<any>;
    findOne(id: string, req: any): Promise<any>;
}
