2025-05-23 21:45:21,098 - REQUEST: {'method': 'GET', 'path': '/health', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T00:45:21.098318'}
2025-05-23 21:51:25,749 - REQUEST: {'method': 'GET', 'path': '/docs', 'duration_ms': 1.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T00:51:25.749727'}
2025-05-23 21:51:27,626 - REQUEST: {'method': 'GET', 'path': '/api/v1/openapi.json', 'duration_ms': 250.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T00:51:27.626796'}
2025-05-23 22:16:12,553 - REQUEST: {'method': 'GET', 'path': '/', 'duration_ms': 3.01, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:16:12.553701'}
2025-05-23 22:17:58,095 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 112.08, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:17:58.095312'}
2025-05-23 22:18:10,324 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 23.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:18:10.324181'}
2025-05-23 22:18:13,760 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 9.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:18:13.760478'}
2025-05-23 22:21:01,430 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 66.51, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:21:01.430274'}
2025-05-23 22:21:45,969 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 7.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:21:45.969267'}
2025-05-23 22:21:46,765 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 7.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:21:46.765443'}
2025-05-23 22:21:48,006 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 6.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:21:48.006676'}
2025-05-23 22:21:48,722 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 7.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:21:48.722860'}
2025-05-23 22:21:49,123 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 8.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:21:49.123932'}
2025-05-23 22:21:49,281 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 9.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:21:49.281970'}
2025-05-23 22:22:01,982 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 6.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:22:01.982424'}
2025-05-23 22:22:08,993 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 8.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:22:08.993081'}
2025-05-23 22:29:37,579 - REQUEST: {'method': 'GET', 'path': '/health', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:29:37.579555'}
2025-05-23 22:34:42,233 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 679.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:34:42.233534'}
2025-05-23 22:34:45,178 - REQUEST: {'method': 'GET', 'path': '/health', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:34:45.178532'}
2025-05-23 22:34:55,996 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 319.01, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:34:55.996248'}
2025-05-23 22:36:07,153 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 6.0, 'status_code': 401, 'user_id': None, 'timestamp': '2025-05-24T01:36:07.153241'}
2025-05-23 22:36:15,168 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 323.72, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:36:15.168499'}
2025-05-23 22:37:02,208 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 491.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:37:02.208192'}
2025-05-23 22:37:22,578 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 484.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:37:22.578094'}
2025-05-23 22:38:54,859 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 478.61, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:38:54.859526'}
2025-05-23 22:38:56,007 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 15.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:38:56.007405'}
2025-05-23 22:38:56,012 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 18.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:38:56.011406'}
2025-05-23 22:38:56,090 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 55.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:38:56.090407'}
2025-05-23 22:38:56,359 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:38:56.359442'}
2025-05-23 22:39:14,097 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:14.097655'}
2025-05-23 22:39:14,100 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:14.100658'}
2025-05-23 22:39:14,120 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 13.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:14.120662'}
2025-05-23 22:39:14,139 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 13.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:14.139658'}
2025-05-23 22:39:17,100 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:17.100675'}
2025-05-23 22:39:17,113 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:17.113677'}
2025-05-23 22:39:33,321 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:33.321519'}
2025-05-23 22:39:33,334 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:33.334518'}
2025-05-23 22:39:36,049 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 13.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:36.049474'}
2025-05-23 22:39:36,061 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:36.061475'}
2025-05-23 22:39:38,573 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 9.52, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:38.573457'}
2025-05-23 22:39:38,588 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:38.588459'}
2025-05-23 22:39:39,711 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:39.711289'}
2025-05-23 22:39:39,725 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.52, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:39:39.725814'}
2025-05-23 22:40:12,892 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 385.03, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:40:12.892014'}
2025-05-23 22:40:12,942 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 15.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:40:12.942019'}
2025-05-23 22:40:12,956 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:40:12.956017'}
2025-05-23 22:40:49,963 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:40:49.963476'}
2025-05-23 22:40:49,981 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:40:49.981476'}
2025-05-23 22:40:53,585 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 15.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:40:53.585725'}
2025-05-23 22:40:53,602 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:40:53.602726'}
2025-05-23 22:42:28,828 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 326.52, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:42:28.828138'}
2025-05-23 22:42:28,873 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:42:28.873131'}
2025-05-23 22:42:28,886 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:42:28.886131'}
2025-05-23 22:42:40,255 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:42:40.255289'}
2025-05-23 22:42:40,272 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:42:40.272287'}
2025-05-23 22:42:44,587 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:42:44.587321'}
2025-05-23 22:42:44,601 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:42:44.601321'}
2025-05-23 22:42:50,318 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:42:50.318939'}
2025-05-23 22:43:14,208 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:43:14.208437'}
2025-05-23 22:43:14,221 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:43:14.221439'}
2025-05-23 22:43:23,105 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 17.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:43:23.105156'}
2025-05-23 22:43:23,136 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 12.01, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T01:43:23.136162'}
2025-05-24 00:10:21,477 - REQUEST: {'method': 'GET', 'path': '/health', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:10:21.477562'}
2025-05-24 00:11:08,184 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 468.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:08.184980'}
2025-05-24 00:11:08,991 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 4.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:08.991981'}
2025-05-24 00:11:08,994 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 5.99, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:08.994981'}
2025-05-24 00:11:09,046 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 41.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:09.046980'}
2025-05-24 00:11:09,060 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:09.060981'}
2025-05-24 00:11:12,975 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:12.975998'}
2025-05-24 00:11:12,981 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 1.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:12.981998'}
2025-05-24 00:11:12,999 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:12.999996'}
2025-05-24 00:11:13,018 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:13.016999'}
2025-05-24 00:11:14,384 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.54, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:14.384705'}
2025-05-24 00:11:14,398 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.99, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:14.398844'}
2025-05-24 00:11:18,679 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:18.679587'}
2025-05-24 00:11:18,689 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:18.689590'}
2025-05-24 00:11:24,461 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:24.461180'}
2025-05-24 00:11:24,475 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:24.475185'}
2025-05-24 00:11:25,162 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:25.162187'}
2025-05-24 00:11:25,178 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:25.178189'}
2025-05-24 00:11:26,752 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.36, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:26.752734'}
2025-05-24 00:11:26,772 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 19.81, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:26.772548'}
2025-05-24 00:11:32,311 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:32.311257'}
2025-05-24 00:11:32,328 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:11:32.328256'}
2025-05-24 00:12:38,496 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:38.496675'}
2025-05-24 00:12:38,507 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.01, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:38.507405'}
2025-05-24 00:12:38,518 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:38.518405'}
2025-05-24 00:12:38,523 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 1.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:38.523404'}
2025-05-24 00:12:38,539 - REQUEST: {'method': 'GET', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 13.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:38.539404'}
2025-05-24 00:12:38,554 - REQUEST: {'method': 'GET', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 7.99, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:38.554409'}
2025-05-24 00:12:42,851 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 13.81, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:42.851394'}
2025-05-24 00:12:42,865 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.6, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:42.864331'}
2025-05-24 00:12:45,007 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:45.007607'}
2025-05-24 00:12:45,022 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 13.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:45.022607'}
2025-05-24 00:12:47,257 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:47.257611'}
2025-05-24 00:12:47,315 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 16.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:47.315609'}
2025-05-24 00:12:50,529 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.09, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:50.529447'}
2025-05-24 00:12:50,540 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 3.16, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:50.540612'}
2025-05-24 00:12:50,579 - REQUEST: {'method': 'GET', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 10.9, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:50.579255'}
2025-05-24 00:12:50,598 - REQUEST: {'method': 'GET', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 15.03, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:50.598122'}
2025-05-24 00:12:52,592 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:52.592516'}
2025-05-24 00:12:52,609 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:52.609515'}
2025-05-24 00:12:56,194 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:56.194905'}
2025-05-24 00:12:56,206 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:56.205903'}
2025-05-24 00:12:56,210 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:56.210903'}
2025-05-24 00:12:56,231 - REQUEST: {'method': 'GET', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 16.02, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:56.231924'}
2025-05-24 00:12:56,252 - REQUEST: {'method': 'GET', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 18.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:56.252906'}
2025-05-24 00:12:58,264 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.72, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:58.264710'}
2025-05-24 00:12:58,276 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.81, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:12:58.276429'}
2025-05-24 00:13:26,734 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:13:26.734778'}
2025-05-24 00:13:26,853 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:13:26.853783'}
2025-05-24 00:13:29,889 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 14.5, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:13:29.889945'}
2025-05-24 00:13:29,906 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 12.59, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:13:29.906289'}
2025-05-24 00:13:33,152 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.59, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:13:33.152548'}
2025-05-24 00:13:33,171 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 15.25, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:13:33.171151'}
2025-05-24 00:13:33,179 - REQUEST: {'method': 'GET', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 18.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:13:33.179148'}
2025-05-24 00:13:33,206 - REQUEST: {'method': 'GET', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:13:33.206367'}
2025-05-24 00:13:35,909 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:13:35.909856'}
2025-05-24 00:13:35,922 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:13:35.922858'}
2025-05-24 00:15:39,977 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 18.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:15:39.977102'}
2025-05-24 00:15:39,994 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:15:39.994101'}
2025-05-24 00:15:42,380 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:15:42.380287'}
2025-05-24 00:15:42,393 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.01, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:15:42.393293'}
2025-05-24 00:15:45,668 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:15:45.668140'}
2025-05-24 00:15:45,682 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:15:45.682140'}
2025-05-24 00:15:48,000 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:15:48.000463'}
2025-05-24 00:15:48,013 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:15:48.013467'}
2025-05-24 00:16:42,858 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:42.857857'}
2025-05-24 00:16:42,870 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:42.870857'}
2025-05-24 00:16:46,304 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 9.52, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:46.304555'}
2025-05-24 00:16:46,320 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:46.320561'}
2025-05-24 00:16:47,784 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:47.784212'}
2025-05-24 00:16:47,800 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:47.800214'}
2025-05-24 00:16:53,621 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 16.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:53.621420'}
2025-05-24 00:16:53,639 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 13.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:53.639419'}
2025-05-24 00:16:53,669 - REQUEST: {'method': 'GET', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:53.669420'}
2025-05-24 00:16:53,698 - REQUEST: {'method': 'GET', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 22.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:53.698421'}
2025-05-24 00:16:54,988 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:54.988528'}
2025-05-24 00:16:55,004 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:55.004527'}
2025-05-24 00:16:59,287 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:59.287811'}
2025-05-24 00:16:59,311 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:59.310810'}
2025-05-24 00:16:59,318 - REQUEST: {'method': 'GET', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 18.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:59.318810'}
2025-05-24 00:16:59,335 - REQUEST: {'method': 'GET', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:16:59.335811'}
2025-05-24 00:17:02,820 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:17:02.820706'}
2025-05-24 00:17:02,832 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:17:02.832705'}
2025-05-24 00:17:03,736 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:17:03.736696'}
2025-05-24 00:17:03,755 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 16.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:17:03.755697'}
2025-05-24 00:17:03,766 - REQUEST: {'method': 'GET', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 20.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:17:03.766697'}
2025-05-24 00:17:03,797 - REQUEST: {'method': 'GET', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 24.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:17:03.797701'}
2025-05-24 00:17:08,022 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:17:08.022027'}
2025-05-24 00:17:08,033 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:17:08.033022'}
2025-05-24 00:19:23,039 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 316.99, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:19:23.039023'}
2025-05-24 00:19:23,719 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:19:23.718025'}
2025-05-24 00:19:23,732 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:19:23.732026'}
2025-05-24 00:20:28,212 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:20:28.212477'}
2025-05-24 00:20:28,222 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:20:28.222478'}
2025-05-24 00:21:07,524 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 367.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:07.524670'}
2025-05-24 00:21:07,623 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:07.623669'}
2025-05-24 00:21:07,640 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:07.640672'}
2025-05-24 00:21:24,861 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 4.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:24.861431'}
2025-05-24 00:21:24,864 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:24.864434'}
2025-05-24 00:21:24,881 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:24.881432'}
2025-05-24 00:21:24,930 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 20.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:24.930433'}
2025-05-24 00:21:27,115 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:27.114048'}
2025-05-24 00:21:27,127 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/notifications/preferences/1', 'duration_ms': 5.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:27.127047'}
2025-05-24 00:21:27,135 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 18.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:27.135046'}
2025-05-24 00:21:27,147 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/preferences/1', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:27.147047'}
2025-05-24 00:21:27,162 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/notifications/status', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:27.161046'}
2025-05-24 00:21:27,166 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/preferences/1', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:27.166046'}
2025-05-24 00:21:27,183 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/status', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:27.183046'}
2025-05-24 00:21:27,196 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/status', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:27.196049'}
2025-05-24 00:21:31,656 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.99, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:31.656193'}
2025-05-24 00:21:31,671 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:31.671195'}
2025-05-24 00:21:41,827 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:41.827217'}
2025-05-24 00:21:41,841 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/reports/templates', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:41.841217'}
2025-05-24 00:21:41,848 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:41.848216'}
2025-05-24 00:21:41,854 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/reports/templates', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:41.854215'}
2025-05-24 00:21:41,869 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:41.869217'}
2025-05-24 00:21:41,896 - REQUEST: {'method': 'GET', 'path': '/api/v1/reports/templates', 'duration_ms': 10.0, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:21:41.896216'}
2025-05-24 00:21:41,952 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:41.952216'}
2025-05-24 00:21:41,966 - REQUEST: {'method': 'GET', 'path': '/api/v1/reports/templates', 'duration_ms': 6.0, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:21:41.965216'}
2025-05-24 00:21:46,213 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:46.213630'}
2025-05-24 00:21:46,224 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:21:46.224629'}
2025-05-24 00:23:37,098 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 358.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:23:37.098062'}
2025-05-24 00:23:37,422 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:23:37.422583'}
2025-05-24 00:23:37,437 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:23:37.437585'}
2025-05-24 00:24:25,922 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:24:25.922534'}
2025-05-24 00:24:25,934 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:24:25.934533'}
2025-05-24 00:24:44,945 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 313.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:24:44.945333'}
2025-05-24 00:24:44,977 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:24:44.977333'}
2025-05-24 00:24:44,989 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:24:44.989337'}
2025-05-24 00:24:54,095 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:24:54.094125'}
2025-05-24 00:24:54,107 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:24:54.107128'}
2025-05-24 00:24:56,827 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:24:56.827985'}
2025-05-24 00:24:56,837 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:24:56.837988'}
2025-05-24 00:26:23,944 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 5.99, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:26:23.944180'}
2025-05-24 00:26:23,956 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:26:23.956184'}
2025-05-24 00:26:26,862 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:26:26.862577'}
2025-05-24 00:26:26,876 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:26:26.876579'}
2025-05-24 00:26:36,005 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:26:36.005414'}
2025-05-24 00:26:36,020 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:26:36.020414'}
2025-05-24 00:33:17,441 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 4.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:17.441503'}
2025-05-24 00:33:17,442 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 5.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:17.442503'}
2025-05-24 00:33:17,519 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 72.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:17.519502'}
2025-05-24 00:33:17,531 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:17.531504'}
2025-05-24 00:33:19,480 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:19.480819'}
2025-05-24 00:33:19,485 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 5.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:19.485818'}
2025-05-24 00:33:19,501 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:19.501818'}
2025-05-24 00:33:19,513 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:19.513818'}
2025-05-24 00:33:42,706 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 27.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:42.706386'}
2025-05-24 00:33:42,758 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 13.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:42.758387'}
2025-05-24 00:33:48,195 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:48.195336'}
2025-05-24 00:33:48,207 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:48.207337'}
2025-05-24 00:33:59,251 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:59.251780'}
2025-05-24 00:33:59,263 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:59.263776'}
2025-05-24 00:33:59,271 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:59.271779'}
2025-05-24 00:33:59,277 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:59.277778'}
2025-05-24 00:33:59,294 - REQUEST: {'method': 'GET', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 13.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:59.294775'}
2025-05-24 00:33:59,316 - REQUEST: {'method': 'GET', 'path': '/api/v1/nutrition/nutritionist/1', 'duration_ms': 17.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:33:59.316778'}
2025-05-24 00:34:01,374 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:01.374352'}
2025-05-24 00:34:01,384 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:01.384353'}
2025-05-24 00:34:08,657 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:08.657388'}
2025-05-24 00:34:08,669 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:08.668388'}
2025-05-24 00:34:16,844 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:16.844004'}
2025-05-24 00:34:16,864 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:16.864005'}
2025-05-24 00:34:16,876 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 1.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:16.876002'}
2025-05-24 00:34:16,883 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 1.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:16.882003'}
2025-05-24 00:34:16,899 - REQUEST: {'method': 'GET', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:16.899004'}
2025-05-24 00:34:16,915 - REQUEST: {'method': 'GET', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:16.915006'}
2025-05-24 00:34:20,051 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:20.051835'}
2025-05-24 00:34:20,064 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:20.064835'}
2025-05-24 00:34:22,993 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/reports/templates', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:22.993712'}
2025-05-24 00:34:22,999 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/reports/templates', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:22.999712'}
2025-05-24 00:34:23,030 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 22.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:23.030711'}
2025-05-24 00:34:23,035 - REQUEST: {'method': 'GET', 'path': '/api/v1/reports/templates', 'duration_ms': 22.0, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:34:23.035710'}
2025-05-24 00:34:23,061 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 27.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:23.061711'}
2025-05-24 00:34:23,097 - REQUEST: {'method': 'GET', 'path': '/api/v1/reports/templates', 'duration_ms': 7.01, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:34:23.097717'}
2025-05-24 00:34:24,018 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:24.018081'}
2025-05-24 00:34:24,032 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:24.032084'}
2025-05-24 00:34:26,089 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:26.089966'}
2025-05-24 00:34:26,116 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 15.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:26.116970'}
2025-05-24 00:34:34,683 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:34.683017'}
2025-05-24 00:34:34,701 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:34.701017'}
2025-05-24 00:34:34,712 - REQUEST: {'method': 'GET', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 20.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:34.712019'}
2025-05-24 00:34:34,727 - REQUEST: {'method': 'GET', 'path': '/api/v1/psychology/psychologist/1', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:34.727018'}
2025-05-24 00:34:35,700 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:35.700006'}
2025-05-24 00:34:35,712 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:35.712006'}
2025-05-24 00:34:37,521 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:37.521372'}
2025-05-24 00:34:37,542 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/notifications/preferences/1', 'duration_ms': 4.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:37.542372'}
2025-05-24 00:34:37,548 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 15.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:37.548373'}
2025-05-24 00:34:37,560 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/preferences/1', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:37.560370'}
2025-05-24 00:34:37,572 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/notifications/status', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:37.572373'}
2025-05-24 00:34:37,580 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/preferences/1', 'duration_ms': 16.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:37.580373'}
2025-05-24 00:34:37,620 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/status', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:37.620370'}
2025-05-24 00:34:37,632 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/status', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:37.632369'}
2025-05-24 00:34:40,684 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:40.684966'}
2025-05-24 00:34:40,695 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:40.694967'}
2025-05-24 00:34:45,861 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:45.861930'}
2025-05-24 00:34:45,874 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:45.874928'}
2025-05-24 00:34:45,891 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/preferences/1', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:45.891929'}
2025-05-24 00:34:45,903 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/preferences/1', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:45.903927'}
2025-05-24 00:34:45,913 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/status', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:45.913925'}
2025-05-24 00:34:45,924 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/status', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:45.924930'}
2025-05-24 00:34:49,015 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:49.015907'}
2025-05-24 00:34:49,030 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:34:49.030909'}
2025-05-24 00:35:40,998 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:35:40.998532'}
2025-05-24 00:35:41,009 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:35:41.009534'}
2025-05-24 00:35:42,641 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:35:42.641544'}
2025-05-24 00:35:42,656 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:35:42.656546'}
2025-05-24 00:36:02,318 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:36:02.318046'}
2025-05-24 00:36:02,332 - REQUEST: {'method': 'POST', 'path': '/api/v1/patients/', 'duration_ms': 9.0, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:36:02.332044'}
2025-05-24 00:36:33,326 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:36:33.326824'}
2025-05-24 00:36:33,338 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:36:33.338823'}
2025-05-24 00:38:35,286 - REQUEST: {'method': 'GET', 'path': '/api/v1/reports/templates', 'duration_ms': 10.0, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:38:35.286268'}
2025-05-24 00:38:35,289 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:35.289266'}
2025-05-24 00:38:35,306 - REQUEST: {'method': 'GET', 'path': '/api/v1/reports/templates', 'duration_ms': 11.0, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:38:35.306266'}
2025-05-24 00:38:35,311 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 15.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:35.311265'}
2025-05-24 00:38:38,523 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:38.523368'}
2025-05-24 00:38:38,538 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:38.538372'}
2025-05-24 00:38:56,207 - REQUEST: {'method': 'GET', 'path': '/api/v1/reports/templates', 'duration_ms': 14.0, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:38:56.207312'}
2025-05-24 00:38:56,212 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 21.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:56.212313'}
2025-05-24 00:38:56,230 - REQUEST: {'method': 'GET', 'path': '/api/v1/reports/templates', 'duration_ms': 19.0, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:38:56.230311'}
2025-05-24 00:38:56,237 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 17.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:56.237310'}
2025-05-24 00:38:58,028 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:58.028088'}
2025-05-24 00:38:58,040 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:58.040090'}
2025-05-24 00:38:59,554 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:59.554379'}
2025-05-24 00:38:59,570 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 13.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:59.570381'}
2025-05-24 00:38:59,576 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/preferences/1', 'duration_ms': 15.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:59.576380'}
2025-05-24 00:38:59,594 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/preferences/1', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:59.594379'}
2025-05-24 00:38:59,599 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/status', 'duration_ms': 14.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:59.599379'}
2025-05-24 00:38:59,617 - REQUEST: {'method': 'GET', 'path': '/api/v1/notifications/status', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:38:59.617379'}
2025-05-24 00:39:03,873 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/notifications/test/1', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:39:03.873711'}
2025-05-24 00:39:03,886 - REQUEST: {'method': 'POST', 'path': '/api/v1/notifications/test/1', 'duration_ms': 7.0, 'status_code': 403, 'user_id': None, 'timestamp': '2025-05-24T03:39:03.886712'}
2025-05-24 00:39:04,706 - REQUEST: {'method': 'POST', 'path': '/api/v1/notifications/test/1', 'duration_ms': 8.0, 'status_code': 403, 'user_id': None, 'timestamp': '2025-05-24T03:39:04.706019'}
2025-05-24 00:39:08,806 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:39:08.806083'}
2025-05-24 00:39:08,819 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:39:08.819083'}
2025-05-24 00:42:30,011 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 383.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:42:30.011188'}
2025-05-24 00:42:30,693 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:42:30.693192'}
2025-05-24 00:42:30,708 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:42:30.708190'}
2025-05-24 00:42:36,546 - REQUEST: {'method': 'GET', 'path': '/api/v1/reports/templates', 'duration_ms': 12.0, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:42:36.546488'}
2025-05-24 00:42:36,548 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 15.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:42:36.548489'}
2025-05-24 00:42:36,586 - REQUEST: {'method': 'GET', 'path': '/api/v1/reports/templates', 'duration_ms': 10.01, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T03:42:36.586492'}
2025-05-24 00:42:36,595 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 15.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:42:36.595487'}
2025-05-24 00:42:37,058 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:42:37.058488'}
2025-05-24 00:42:37,070 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:42:37.070487'}
2025-05-24 00:44:30,488 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 326.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:44:30.488540'}
2025-05-24 00:44:31,138 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 5.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:44:31.138542'}
2025-05-24 00:44:31,143 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:44:31.143540'}
2025-05-24 00:44:31,155 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:44:31.155541'}
2025-05-24 00:44:31,168 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:44:31.168542'}
2025-05-24 00:45:20,935 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:45:20.935084'}
2025-05-24 00:45:20,947 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:45:20.947084'}
2025-05-24 00:45:39,793 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:45:39.793348'}
2025-05-24 00:45:39,805 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:45:39.805350'}
2025-05-24 00:50:20,596 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:50:20.596347'}
2025-05-24 00:50:20,609 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:50:20.609348'}
2025-05-24 00:58:18,487 - REQUEST: {'method': 'GET', 'path': '/docs', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:18.487341'}
2025-05-24 00:58:25,904 - REQUEST: {'method': 'GET', 'path': '/api/v1/openapi.json', 'duration_ms': 182.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:25.904384'}
2025-05-24 00:58:28,728 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 4.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:28.728444'}
2025-05-24 00:58:28,763 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/patients/', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:28.763443'}
2025-05-24 00:58:28,872 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 104.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:28.872441'}
2025-05-24 00:58:28,889 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:28.889442'}
2025-05-24 00:58:37,481 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 7.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:37.481190'}
2025-05-24 00:58:37,515 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:37.515184'}
2025-05-24 00:58:41,771 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:41.771351'}
2025-05-24 00:58:41,787 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:41.787352'}
2025-05-24 00:58:45,667 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 18.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:45.667937'}
2025-05-24 00:58:45,681 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 11.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:45.681940'}
2025-05-24 00:58:57,462 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 3.99, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:57.462544'}
2025-05-24 00:58:57,464 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 5.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:57.464544'}
2025-05-24 00:58:57,566 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 97.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:57.566543'}
2025-05-24 00:58:57,581 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:57.581547'}
2025-05-24 00:58:59,767 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 15.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:59.767902'}
2025-05-24 00:58:59,782 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 12.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:58:59.782903'}
2025-05-24 00:59:58,274 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 795.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:59:58.274556'}
2025-05-24 00:59:58,382 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 18.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:59:58.382555'}
2025-05-24 00:59:58,401 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T03:59:58.401556'}
2025-05-24 01:03:14,622 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 73.0, 'status_code': 404, 'user_id': None, 'timestamp': '2025-05-24T04:03:14.622053'}
2025-05-24 01:03:14,635 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 9.0, 'status_code': 404, 'user_id': None, 'timestamp': '2025-05-24T04:03:14.635054'}
2025-05-24 01:03:17,673 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 7.0, 'status_code': 404, 'user_id': None, 'timestamp': '2025-05-24T04:03:17.673415'}
2025-05-24 01:03:17,689 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 13.0, 'status_code': 404, 'user_id': None, 'timestamp': '2025-05-24T04:03:17.689416'}
2025-05-24 01:04:50,356 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 84.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:04:50.356202'}
2025-05-24 01:04:50,364 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 5.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:04:50.364206'}
2025-05-24 01:04:50,371 - REQUEST: {'method': 'GET', 'path': '/api/v1/patients/', 'duration_ms': 3.99, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:04:50.371202'}
2025-05-24 01:04:50,816 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:04:50.816353'}
2025-05-24 01:04:50,823 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 5.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:04:50.823355'}
2025-05-24 01:04:50,832 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:04:50.832355'}
2025-05-24 01:05:01,254 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 61.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:05:01.254683'}
2025-05-24 01:06:32,767 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 67.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:06:32.767492'}
2025-05-24 01:07:14,622 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 72.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:07:14.622282'}
2025-05-24 01:07:15,212 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 5.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:07:15.212284'}
2025-05-24 01:07:15,902 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 8.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:07:15.902285'}
2025-05-24 01:07:16,549 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 8.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:07:16.549285'}
2025-05-24 01:07:21,862 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 98.01, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:07:21.861294'}
2025-05-24 01:07:26,356 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 5.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:07:26.356577'}
2025-05-24 01:08:08,816 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/auth/register', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:08:08.816837'}
2025-05-24 01:08:08,838 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/register', 'duration_ms': 17.0, 'status_code': 422, 'user_id': None, 'timestamp': '2025-05-24T04:08:08.838833'}
2025-05-24 01:12:04,674 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 70.99, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:12:04.674525'}
2025-05-24 01:14:37,276 - REQUEST: {'method': 'GET', 'path': '/docs', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:14:37.276325'}
2025-05-24 01:14:38,944 - REQUEST: {'method': 'GET', 'path': '/api/v1/openapi.json', 'duration_ms': 229.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:14:38.944323'}
2025-05-24 01:15:03,228 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 84.0, 'status_code': 500, 'user_id': None, 'timestamp': '2025-05-24T04:15:03.228419'}
2025-05-24 01:21:37,180 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/auth/register', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:21:37.179259'}
2025-05-24 01:21:37,895 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/register', 'duration_ms': 707.08, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:21:37.895341'}
2025-05-24 01:21:45,637 - REQUEST: {'method': 'POST', 'path': '/api/v1/auth/login', 'duration_ms': 314.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:21:45.637656'}
2025-05-24 01:21:46,642 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 2.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:21:46.642657'}
2025-05-24 01:21:46,653 - REQUEST: {'method': 'OPTIONS', 'path': '/api/v1/users/me', 'duration_ms': 3.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:21:46.653656'}
2025-05-24 01:21:46,965 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 10.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:21:46.965655'}
2025-05-24 01:21:46,975 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 8.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:21:46.975658'}
2025-05-24 01:21:57,340 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 9.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:21:57.340394'}
2025-05-24 01:21:57,350 - REQUEST: {'method': 'GET', 'path': '/api/v1/users/me', 'duration_ms': 6.0, 'status_code': 200, 'user_id': None, 'timestamp': '2025-05-24T04:21:57.350394'}
