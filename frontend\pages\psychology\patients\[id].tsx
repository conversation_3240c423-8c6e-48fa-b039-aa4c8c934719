import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  ArrowLeft,
  Edit,
  Brain,
  Calendar,
  Phone,
  Mail,
  User,
  FileText,
  Plus
} from 'lucide-react'
import Layout from '../../../components/Layout'

interface Patient {
  id: number
  nome: string
  email: string
  telefone: string
  data_nascimento: string
  sexo: string
  estado_civil: string
  profissao: string
  motivo_consulta: string
  historico_familiar: string
  medicamentos: string
  tratamentos_anteriores: string
  observacoes: string
  created_at: string
}

export default function PatientDetails() {
  const router = useRouter()
  const { id } = router.query
  const [patient, setPatient] = useState<Patient | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (id) {
      fetchPatient()
    }
  }, [id])

  const fetchPatient = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Mock data - em produção viria da API
      setPatient({
        id: Number(id),
        nome: 'Ana Costa',
        email: '<EMAIL>',
        telefone: '(11) 99999-9999',
        data_nascimento: '1988-03-20',
        sexo: 'feminino',
        estado_civil: 'solteiro',
        profissao: 'Engenheira',
        motivo_consulta: 'Ansiedade generalizada e episódios de pânico',
        historico_familiar: 'Mãe com histórico de depressão',
        medicamentos: 'Sertralina 50mg - 1x ao dia',
        tratamentos_anteriores: 'Terapia cognitivo-comportamental em 2020',
        observacoes: 'Paciente muito colaborativa, responde bem às técnicas de relaxamento',
        created_at: '2024-01-10'
      })
    } catch (error) {
      console.error('Erro ao carregar paciente:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const calculateAge = (birthDate: string) => {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age
  }

  if (loading) {
    return (
      <Layout title="Carregando...">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      </Layout>
    )
  }

  if (!patient) {
    return (
      <Layout title="Paciente não encontrado">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">Paciente não encontrado</h2>
          <Link href="/psychology/patients" className="text-purple-600 hover:text-purple-800">
            Voltar para lista de pacientes
          </Link>
        </div>
      </Layout>
    )
  }

  return (
    <Layout title={`${patient.nome} - Psicologia`}>
      <Head>
        <title>{patient.nome} - Psicologia | Hypatium</title>
      </Head>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Link
                  href="/psychology/patients"
                  className="mr-4 p-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    <Brain className="h-6 w-6 text-purple-600 mr-2" />
                    {patient.nome}
                  </h1>
                  <p className="text-gray-600">Paciente de Psicologia</p>
                </div>
              </div>
              <div className="flex space-x-3">
                <Link
                  href={`/psychology/patients/${patient.id}/sessions`}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Ver Sessões
                </Link>
                <Link
                  href={`/psychology/sessions/new?patient=${patient.id}`}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Nova Sessão
                </Link>
                <Link
                  href={`/psychology/patients/${patient.id}/edit`}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </Link>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Informações Pessoais */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <User className="h-5 w-5 text-purple-600 mr-2" />
                  Informações Pessoais
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Nome</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.nome}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Idade</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {calculateAge(patient.data_nascimento)} anos
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Sexo</label>
                    <p className="mt-1 text-sm text-gray-900 capitalize">{patient.sexo}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Estado Civil</label>
                    <p className="mt-1 text-sm text-gray-900 capitalize">{patient.estado_civil}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Profissão</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.profissao}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Data de Nascimento</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(patient.data_nascimento)}</p>
                  </div>
                </div>
              </div>

              {/* Informações Clínicas */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Brain className="h-5 w-5 text-purple-600 mr-2" />
                  Informações Clínicas
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Motivo da Consulta</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.motivo_consulta}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Histórico Familiar</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.historico_familiar}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Medicamentos em Uso</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.medicamentos}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Tratamentos Anteriores</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.tratamentos_anteriores}</p>
                  </div>
                  {patient.observacoes && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Observações</label>
                      <p className="mt-1 text-sm text-gray-900">{patient.observacoes}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Contato */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Contato</h3>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-gray-400 mr-3" />
                    <span className="text-sm text-gray-900">{patient.email}</span>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 text-gray-400 mr-3" />
                    <span className="text-sm text-gray-900">{patient.telefone}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-400 mr-3" />
                    <span className="text-sm text-gray-900">
                      Cadastrado em {formatDate(patient.created_at)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Ações Rápidas */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Ações Rápidas</h3>
                <div className="space-y-3">
                  <Link
                    href={`/psychology/sessions/new?patient=${patient.id}`}
                    className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Nova Sessão
                  </Link>
                  <Link
                    href={`/psychology/patients/${patient.id}/sessions`}
                    className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Ver Histórico
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
