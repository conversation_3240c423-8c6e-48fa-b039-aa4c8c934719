"""
Serviço para geração de relatórios automáticos
"""

import json
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.models.models import AutomaticReport, Patient, PhysicalAssessment, PsychologySession, NutritionalPlan
from app.schemas.report import (
    AutomaticReportCreate,
    AutomaticReportUpdate,
    ReportTemplate,
    ReportData
)

class ReportService:
    def __init__(self, db: Session):
        self.db = db

    def get_report(self, report_id: int) -> Optional[AutomaticReport]:
        """Obter relatório por ID"""
        return self.db.query(AutomaticReport).filter(
            AutomaticReport.id == report_id
        ).first()

    def get_reports_by_patient(self, patient_id: int, skip: int = 0, limit: int = 100) -> List[AutomaticReport]:
        """Obter relatórios de um paciente"""
        return self.db.query(AutomaticReport).filter(
            AutomaticReport.paciente_id == patient_id
        ).order_by(AutomaticReport.data_geracao.desc()).offset(skip).limit(limit).all()

    def get_reports_by_professional(self, professional_id: int, skip: int = 0, limit: int = 100) -> List[AutomaticReport]:
        """Obter relatórios criados por um profissional"""
        return self.db.query(AutomaticReport).filter(
            AutomaticReport.profissional_id == professional_id
        ).order_by(AutomaticReport.data_geracao.desc()).offset(skip).limit(limit).all()

    def create_report(self, report_in: AutomaticReportCreate) -> AutomaticReport:
        """Criar novo relatório"""
        # Verificar se paciente existe
        patient = self.db.query(Patient).filter(Patient.id == report_in.paciente_id).first()
        if not patient:
            raise ValueError("Paciente não encontrado")

        db_report = AutomaticReport(**report_in.dict())
        self.db.add(db_report)
        self.db.commit()
        self.db.refresh(db_report)
        return db_report

    def update_report(self, report_id: int, report_in: AutomaticReportUpdate) -> Optional[AutomaticReport]:
        """Atualizar relatório"""
        db_report = self.get_report(report_id)
        if not db_report:
            return None

        update_data = report_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_report, field, value)

        self.db.commit()
        self.db.refresh(db_report)
        return db_report

    def delete_report(self, report_id: int) -> bool:
        """Deletar relatório"""
        db_report = self.get_report(report_id)
        if not db_report:
            return False

        self.db.delete(db_report)
        self.db.commit()
        return True

    def collect_patient_data(self, patient_id: int, period_days: int = 30) -> Dict[str, Any]:
        """Coletar dados do paciente para geração de relatório"""
        cutoff_date = datetime.utcnow() - timedelta(days=period_days)

        # Dados do paciente
        patient = self.db.query(Patient).filter(Patient.id == patient_id).first()
        if not patient:
            raise ValueError("Paciente não encontrado")

        # Avaliações físicas
        assessments = self.db.query(PhysicalAssessment).filter(
            PhysicalAssessment.paciente_id == patient_id,
            PhysicalAssessment.data_avaliacao >= cutoff_date
        ).order_by(PhysicalAssessment.data_avaliacao.desc()).all()

        # Sessões psicológicas
        psychology_sessions = self.db.query(PsychologySession).filter(
            PsychologySession.paciente_id == patient_id,
            PsychologySession.data_sessao >= cutoff_date
        ).order_by(PsychologySession.data_sessao.desc()).all()

        # Planos nutricionais
        nutrition_plans = self.db.query(NutritionalPlan).filter(
            NutritionalPlan.paciente_id == patient_id,
            NutritionalPlan.data_criacao >= cutoff_date
        ).order_by(NutritionalPlan.data_criacao.desc()).all()

        return {
            "patient": {
                "id": patient.id,
                "nome": patient.nome,
                "email": patient.email,
                "nascimento": patient.nascimento.isoformat() if patient.nascimento else None,
                "sexo": patient.sexo.value if patient.sexo else None,
                "objetivo": patient.objetivo,
                "observacoes": patient.observacoes
            },
            "assessments": [
                {
                    "id": a.id,
                    "data": a.data_avaliacao.isoformat(),
                    "peso": a.peso,
                    "altura": a.altura,
                    "imc": a.imc,
                    "gordura_percentual": a.gordura_percentual,
                    "massa_gorda": a.massa_gorda,
                    "massa_magra": a.massa_magra,
                    "observacoes": a.observacoes
                } for a in assessments
            ],
            "psychology_sessions": [
                {
                    "id": s.id,
                    "data": s.data_sessao.isoformat(),
                    "duracao": s.duracao_minutos,
                    "humor_escala": s.humor_escala,
                    "ansiedade_escala": s.ansiedade_escala,
                    "motivacao_escala": s.motivacao_escala,
                    "objetivos": s.objetivos_sessao,
                    "anotacoes": s.anotacoes[:200] if s.anotacoes else None  # Limitar tamanho
                } for s in psychology_sessions
            ],
            "nutrition_plans": [
                {
                    "id": p.id,
                    "data": p.data_criacao.isoformat(),
                    "calorias_alvo": p.calorias_alvo,
                    "proteinas_g": p.proteinas_g,
                    "carboidratos_g": p.carboidratos_g,
                    "gorduras_g": p.gorduras_g,
                    "ativo": p.ativo
                } for p in nutrition_plans
            ],
            "period_days": period_days,
            "report_date": datetime.utcnow().isoformat()
        }

    def generate_summary_statistics(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """Gerar estatísticas resumidas dos dados do paciente"""
        assessments = patient_data.get("assessments", [])
        psychology_sessions = patient_data.get("psychology_sessions", [])
        nutrition_plans = patient_data.get("nutrition_plans", [])

        stats = {
            "total_assessments": len(assessments),
            "total_psychology_sessions": len(psychology_sessions),
            "total_nutrition_plans": len(nutrition_plans),
        }

        # Estatísticas de avaliações físicas
        if assessments:
            weights = [a["peso"] for a in assessments if a["peso"]]
            if len(weights) >= 2:
                stats["weight_change"] = weights[0] - weights[-1]  # Mais recente - mais antigo

            imcs = [a["imc"] for a in assessments if a["imc"]]
            if imcs:
                stats["average_imc"] = sum(imcs) / len(imcs)

            body_fats = [a["gordura_percentual"] for a in assessments if a["gordura_percentual"]]
            if len(body_fats) >= 2:
                stats["body_fat_change"] = body_fats[0] - body_fats[-1]

        # Estatísticas psicológicas
        if psychology_sessions:
            humor_scores = [s["humor_escala"] for s in psychology_sessions if s["humor_escala"]]
            if humor_scores:
                stats["average_humor"] = sum(humor_scores) / len(humor_scores)

            anxiety_scores = [s["ansiedade_escala"] for s in psychology_sessions if s["ansiedade_escala"]]
            if anxiety_scores:
                stats["average_anxiety"] = sum(anxiety_scores) / len(anxiety_scores)

            motivation_scores = [s["motivacao_escala"] for s in psychology_sessions if s["motivacao_escala"]]
            if motivation_scores:
                stats["average_motivation"] = sum(motivation_scores) / len(motivation_scores)

        # Estatísticas nutricionais
        active_plans = [p for p in nutrition_plans if p["ativo"]]
        if active_plans:
            stats["current_calories"] = active_plans[0]["calorias_alvo"]
            stats["current_protein"] = active_plans[0]["proteinas_g"]

        return stats

    def create_report_prompt(self, patient_data: Dict[str, Any], report_type: str = "comprehensive") -> str:
        """Criar prompt para IA baseado nos dados do paciente"""

        patient = patient_data["patient"]
        stats = self.generate_summary_statistics(patient_data)

        prompt = f"""
Gere um relatório profissional de {patient_data['period_days']} dias para o paciente {patient['nome']}.

DADOS DO PACIENTE:
- Nome: {patient['nome']}
- Objetivo: {patient.get('objetivo', 'Não especificado')}
- Sexo: {patient.get('sexo', 'Não especificado')}

ESTATÍSTICAS DO PERÍODO:
- Avaliações físicas: {stats.get('total_assessments', 0)}
- Sessões psicológicas: {stats.get('total_psychology_sessions', 0)}
- Planos nutricionais: {stats.get('total_nutrition_plans', 0)}
"""

        if stats.get('weight_change'):
            prompt += f"- Variação de peso: {stats['weight_change']:.1f}kg\n"

        if stats.get('body_fat_change'):
            prompt += f"- Variação de gordura corporal: {stats['body_fat_change']:.1f}%\n"

        if stats.get('average_humor'):
            prompt += f"- Humor médio: {stats['average_humor']:.1f}/10\n"

        if stats.get('average_anxiety'):
            prompt += f"- Ansiedade média: {stats['average_anxiety']:.1f}/10\n"

        prompt += f"""
AVALIAÇÕES FÍSICAS ({len(patient_data.get('assessments', []))} registros):
"""
        for assessment in patient_data.get('assessments', [])[:3]:  # Últimas 3
            prompt += f"- {assessment['data'][:10]}: Peso {assessment['peso']}kg, IMC {assessment.get('imc', 'N/A')}\n"

        prompt += f"""
SESSÕES PSICOLÓGICAS ({len(patient_data.get('psychology_sessions', []))} registros):
"""
        for session in patient_data.get('psychology_sessions', [])[:3]:  # Últimas 3
            prompt += f"- {session['data'][:10]}: Humor {session.get('humor_escala', 'N/A')}/10, Ansiedade {session.get('ansiedade_escala', 'N/A')}/10\n"

        prompt += """
INSTRUÇÕES:
1. Analise os dados apresentados
2. Identifique tendências e padrões
3. Destaque progressos e áreas de atenção
4. Forneça recomendações específicas
5. Use linguagem profissional mas acessível
6. Estruture o relatório em seções claras
7. Limite a 800 palavras

ESTRUTURA SUGERIDA:
- Resumo Executivo
- Análise Física
- Análise Psicológica (se aplicável)
- Análise Nutricional (se aplicável)
- Recomendações
- Próximos Passos
"""

        return prompt

    async def generate_ai_report(self, patient_id: int, professional_id: int, report_type: str = "comprehensive", period_days: int = 30) -> AutomaticReport:
        """Gerar relatório usando IA"""

        # Coletar dados do paciente
        patient_data = self.collect_patient_data(patient_id, period_days)

        # Gerar prompt
        prompt = self.create_report_prompt(patient_data, report_type)

        # Usar serviço de IA
        from app.services.ai_service import AIService
        ai_service = AIService()

        try:
            # Gerar relatório com IA
            ai_response = await ai_service.generate_report(prompt, patient_data)
            ai_content = ai_response["content"]

            # Metadados da IA
            ai_metadata = {
                "model_used": ai_response.get("model_used"),
                "tokens_used": ai_response.get("tokens_used"),
                "generated_at": ai_response.get("generated_at"),
                "simulation": ai_response.get("simulation", False)
            }

        except Exception as e:
            # Fallback para relatório básico
            ai_content = self.generate_basic_report(patient_data)
            ai_metadata = {
                "model_used": "fallback",
                "error": str(e),
                "generated_at": datetime.utcnow().isoformat()
            }

        # Criar relatório no banco
        report_data = {
            "tipo_relatorio": report_type,
            "conteudo_gerado": ai_content,
            "dados_utilizados": json.dumps(patient_data),
            "prompt_utilizado": prompt,
            "metadados_ia": json.dumps(ai_metadata),
            "data_geracao": datetime.utcnow(),
            "paciente_id": patient_id,
            "profissional_id": professional_id,
            "status": "concluido"
        }

        return self.create_report(AutomaticReportCreate(**report_data))

    def generate_basic_report(self, patient_data: Dict[str, Any]) -> str:
        """Gerar relatório básico sem IA (fallback)"""
        patient = patient_data["patient"]
        stats = self.generate_summary_statistics(patient_data)

        report = f"""# RELATÓRIO DE ACOMPANHAMENTO - {patient['nome'].upper()}

## RESUMO EXECUTIVO
Relatório de acompanhamento de {patient_data['period_days']} dias para {patient['nome']}.

## DADOS COLETADOS
- **Avaliações Físicas**: {stats.get('total_assessments', 0)} registros
- **Sessões Psicológicas**: {stats.get('total_psychology_sessions', 0)} registros
- **Planos Nutricionais**: {stats.get('total_nutrition_plans', 0)} registros

## ANÁLISE FÍSICA
"""

        if stats.get('weight_change'):
            change_text = "ganhou" if stats['weight_change'] > 0 else "perdeu"
            report += f"- O paciente {change_text} {abs(stats['weight_change']):.1f}kg no período\n"

        if stats.get('average_imc'):
            report += f"- IMC médio: {stats['average_imc']:.1f}\n"

        if stats.get('body_fat_change'):
            change_text = "aumento" if stats['body_fat_change'] > 0 else "redução"
            report += f"- {change_text.capitalize()} de {abs(stats['body_fat_change']):.1f}% na gordura corporal\n"

        report += "\n## ANÁLISE PSICOLÓGICA\n"

        if stats.get('average_humor'):
            report += f"- Humor médio: {stats['average_humor']:.1f}/10\n"

        if stats.get('average_anxiety'):
            report += f"- Nível de ansiedade médio: {stats['average_anxiety']:.1f}/10\n"

        if stats.get('average_motivation'):
            report += f"- Motivação média: {stats['average_motivation']:.1f}/10\n"

        report += "\n## RECOMENDAÇÕES\n"
        report += "- Continuar acompanhamento regular\n"
        report += "- Manter consistência nos hábitos estabelecidos\n"
        report += "- Ajustar estratégias conforme evolução\n"

        report += f"\n---\n*Relatório gerado automaticamente em {datetime.utcnow().strftime('%d/%m/%Y às %H:%M')}*"

        return report

    def get_report_templates(self) -> List[Dict[str, Any]]:
        """Obter templates de relatórios disponíveis"""
        return [
            {
                "id": "comprehensive",
                "name": "Relatório Abrangente",
                "description": "Análise completa incluindo aspectos físicos, psicológicos e nutricionais",
                "sections": ["resumo", "fisico", "psicologico", "nutricional", "recomendacoes"]
            },
            {
                "id": "physical_only",
                "name": "Relatório Físico",
                "description": "Foco apenas em avaliações físicas e composição corporal",
                "sections": ["resumo", "fisico", "recomendacoes"]
            },
            {
                "id": "psychological_only",
                "name": "Relatório Psicológico",
                "description": "Análise do progresso psicológico e bem-estar emocional",
                "sections": ["resumo", "psicologico", "recomendacoes"]
            },
            {
                "id": "nutritional_only",
                "name": "Relatório Nutricional",
                "description": "Avaliação dos planos nutricionais e aderência",
                "sections": ["resumo", "nutricional", "recomendacoes"]
            }
        ]
