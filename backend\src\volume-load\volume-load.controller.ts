import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards, 
  Request 
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { VolumeLoadService } from './volume-load.service';
import { CreateVolumeLoadDto } from './dto/create-volume-load.dto';
import { UpdateVolumeLoadDto } from './dto/update-volume-load.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('volume-load')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('volume-load')
export class VolumeLoadController {
  constructor(private readonly volumeLoadService: VolumeLoadService) {}

  @ApiOperation({ summary: 'Criar registro de Volume Load (apenas Personal Trainer)' })
  @ApiResponse({ status: 201, description: 'Volume Load criado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Roles('personal')
  @Post()
  create(@Body() createVolumeLoadDto: CreateVolumeLoadDto, @Request() req) {
    return this.volumeLoadService.create(createVolumeLoadDto, req.user.id);
  }

  @ApiOperation({ summary: 'Buscar Volume Loads por avaliação' })
  @ApiResponse({ status: 200, description: 'Volume Loads encontrados' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Get('avaliacao/:avaliacaoId')
  findByAvaliacao(@Param('avaliacaoId') avaliacaoId: string, @Request() req) {
    const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
    return this.volumeLoadService.findByAvaliacao(avaliacaoId, personalId);
  }

  @ApiOperation({ summary: 'Buscar estatísticas de Volume Load por avaliação' })
  @ApiResponse({ status: 200, description: 'Estatísticas encontradas' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Get('avaliacao/:avaliacaoId/stats')
  getStats(@Param('avaliacaoId') avaliacaoId: string, @Request() req) {
    const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
    return this.volumeLoadService.getVolumeLoadStats(avaliacaoId, personalId);
  }

  @ApiOperation({ summary: 'Atualizar Volume Load (apenas Personal Trainer)' })
  @ApiResponse({ status: 200, description: 'Volume Load atualizado com sucesso' })
  @ApiResponse({ status: 404, description: 'Volume Load não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Roles('personal')
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateVolumeLoadDto: UpdateVolumeLoadDto, @Request() req) {
    return this.volumeLoadService.update(id, updateVolumeLoadDto, req.user.id);
  }

  @ApiOperation({ summary: 'Remover Volume Load (apenas Personal Trainer)' })
  @ApiResponse({ status: 200, description: 'Volume Load removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Volume Load não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Roles('personal')
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.volumeLoadService.remove(id, req.user.id);
  }
}
