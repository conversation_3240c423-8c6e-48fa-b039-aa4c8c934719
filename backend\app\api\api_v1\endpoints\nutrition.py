"""
Endpoints de nutrição
"""

from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.models.models import User
from app.schemas.nutrition import (
    NutritionalPlan,
    NutritionalPlanCreate,
    NutritionalPlanUpdate,
    NutritionalCalculation,
    MacronutrientDistribution
)
from app.services.nutrition_service import NutritionService

router = APIRouter()

@router.post("/", response_model=NutritionalPlan)
def create_nutritional_plan(
    *,
    db: Session = Depends(get_db),
    plan_in: NutritionalPlanCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Criar novo plano nutricional
    """
    # Verificar permissões
    if current_user.tipo not in ["nutricionista", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas nutricionistas podem criar planos nutricionais")

    nutrition_service = NutritionService(db)

    # Definir nutricionista como usuário atual se não especificado
    if not plan_in.nutricionista_id:
        plan_in.nutricionista_id = current_user.id

    try:
        plan = nutrition_service.create_plan(plan_in)
        return plan
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/patient/{patient_id}", response_model=List[NutritionalPlan])
def read_plans_by_patient(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active_only: bool = Query(False),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter planos nutricionais de um paciente
    """
    nutrition_service = NutritionService(db)

    if active_only:
        active_plan = nutrition_service.get_active_plan_by_patient(patient_id)
        return [active_plan] if active_plan else []
    else:
        plans = nutrition_service.get_plans_by_patient(patient_id, skip=skip, limit=limit)
        return plans

@router.get("/nutritionist/{nutritionist_id}", response_model=List[NutritionalPlan])
def read_plans_by_nutritionist(
    *,
    db: Session = Depends(get_db),
    nutritionist_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter planos criados por um nutricionista
    """
    # Verificar permissões
    if current_user.tipo not in ["nutricionista", "admin"] and current_user.id != nutritionist_id:
        raise HTTPException(status_code=403, detail="Sem permissão para acessar estes planos")

    nutrition_service = NutritionService(db)
    plans = nutrition_service.get_plans_by_nutritionist(nutritionist_id, skip=skip, limit=limit)
    return plans

@router.get("/{plan_id}", response_model=NutritionalPlan)
def read_nutritional_plan(
    *,
    db: Session = Depends(get_db),
    plan_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter plano nutricional por ID
    """
    nutrition_service = NutritionService(db)
    plan = nutrition_service.get_plan(plan_id)

    if not plan:
        raise HTTPException(status_code=404, detail="Plano nutricional não encontrado")

    return plan

@router.put("/{plan_id}", response_model=NutritionalPlan)
def update_nutritional_plan(
    *,
    db: Session = Depends(get_db),
    plan_id: int,
    plan_in: NutritionalPlanUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Atualizar plano nutricional
    """
    # Verificar permissões
    if current_user.tipo not in ["nutricionista", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas nutricionistas podem atualizar planos nutricionais")

    nutrition_service = NutritionService(db)
    plan = nutrition_service.update_plan(plan_id, plan_in)

    if not plan:
        raise HTTPException(status_code=404, detail="Plano nutricional não encontrado")

    return plan

@router.delete("/{plan_id}")
def delete_nutritional_plan(
    *,
    db: Session = Depends(get_db),
    plan_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Deletar plano nutricional
    """
    # Verificar permissões
    if current_user.tipo not in ["nutricionista", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas nutricionistas podem deletar planos nutricionais")

    nutrition_service = NutritionService(db)
    success = nutrition_service.delete_plan(plan_id)

    if not success:
        raise HTTPException(status_code=404, detail="Plano nutricional não encontrado")

    return {"message": "Plano nutricional deletado com sucesso"}

@router.post("/{plan_id}/deactivate", response_model=NutritionalPlan)
def deactivate_nutritional_plan(
    *,
    db: Session = Depends(get_db),
    plan_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Desativar plano nutricional
    """
    # Verificar permissões
    if current_user.tipo not in ["nutricionista", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas nutricionistas podem desativar planos nutricionais")

    nutrition_service = NutritionService(db)
    plan = nutrition_service.deactivate_plan(plan_id)

    if not plan:
        raise HTTPException(status_code=404, detail="Plano nutricional não encontrado")

    return plan

# Endpoints de cálculo
@router.post("/calculate/needs", response_model=Dict[str, Any])
def calculate_nutritional_needs(
    *,
    db: Session = Depends(get_db),
    calculation_data: NutritionalCalculation,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Calcular necessidades nutricionais
    """
    nutrition_service = NutritionService(db)
    result = nutrition_service.calculate_nutritional_needs(calculation_data)
    return result

@router.post("/calculate/macros", response_model=Dict[str, Any])
def calculate_macronutrients(
    *,
    distribution: MacronutrientDistribution,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Calcular distribuição de macronutrientes
    """
    nutrition_service = NutritionService(None)  # Não precisa de DB para cálculos
    try:
        result = nutrition_service.calculate_macronutrients(distribution)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/patient/{patient_id}/auto-create", response_model=NutritionalPlan)
def create_plan_from_assessment(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    objetivo: str = Query("manutencao", pattern="^(deficit|manutencao|superavit)$"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Criar plano nutricional automaticamente baseado na última avaliação física
    """
    # Verificar permissões
    if current_user.tipo not in ["nutricionista", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas nutricionistas podem criar planos nutricionais")

    nutrition_service = NutritionService(db)

    try:
        plan = nutrition_service.create_plan_from_assessment(patient_id, current_user.id, objetivo)
        return plan
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{plan_id}/statistics", response_model=Dict[str, Any])
def get_plan_statistics(
    *,
    db: Session = Depends(get_db),
    plan_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter estatísticas do plano nutricional
    """
    nutrition_service = NutritionService(db)
    stats = nutrition_service.get_plan_statistics(plan_id)

    if not stats:
        raise HTTPException(status_code=404, detail="Plano nutricional não encontrado")

    return stats

@router.post("/meal-suggestions", response_model=Dict[str, Any])
def generate_meal_suggestions(
    *,
    calorias_alvo: float = Query(..., gt=0),
    num_refeicoes: int = Query(5, ge=3, le=6),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Gerar sugestões de distribuição de refeições
    """
    nutrition_service = NutritionService(None)  # Não precisa de DB
    result = nutrition_service.generate_meal_suggestions(calorias_alvo, num_refeicoes)
    return result