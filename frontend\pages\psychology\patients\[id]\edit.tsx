import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { ArrowLeft, Save, Brain } from 'lucide-react'
import Layout from '../../../../components/Layout'

interface PatientForm {
  nome: string
  email: string
  telefone: string
  data_nascimento: string
  sexo: string
  estado_civil: string
  profissao: string
  motivo_consulta: string
  historico_familiar: string
  medicamentos: string
  tratamentos_anteriores: string
  observacoes: string
}

export default function EditPsychologyPatient() {
  const router = useRouter()
  const { id } = router.query
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<PatientForm>({
    nome: '',
    email: '',
    telefone: '',
    data_nascimento: '',
    sexo: '',
    estado_civil: '',
    profissao: '',
    motivo_consulta: '',
    historico_familiar: '',
    medicamentos: '',
    tratamentos_anteriores: '',
    observacoes: ''
  })

  useEffect(() => {
    if (id) {
      fetchPatient()
    }
  }, [id])

  const fetchPatient = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Mock data - em produção viria da API
      setFormData({
        nome: 'Ana Costa',
        email: '<EMAIL>',
        telefone: '(11) 99999-9999',
        data_nascimento: '1988-03-20',
        sexo: 'feminino',
        estado_civil: 'solteiro',
        profissao: 'Engenheira',
        motivo_consulta: 'Ansiedade generalizada e episódios de pânico',
        historico_familiar: 'Mãe com histórico de depressão',
        medicamentos: 'Sertralina 50mg - 1x ao dia',
        tratamentos_anteriores: 'Terapia cognitivo-comportamental em 2020',
        observacoes: 'Paciente muito colaborativa, responde bem às técnicas de relaxamento'
      })
    } catch (error) {
      console.error('Erro ao carregar paciente:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Aqui você faria a chamada para a API
      console.log('Dados atualizados do paciente:', formData)
      
      // Simular salvamento
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Redirecionar para detalhes do paciente
      router.push(`/psychology/patients/${id}`)
    } catch (error) {
      console.error('Erro ao atualizar paciente:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <Layout title="Editar Paciente - Psicologia">
      <Head>
        <title>Editar Paciente - Psicologia | Hypatium</title>
      </Head>

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Page Header */}
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <Link
                href={`/psychology/patients/${id}`}
                className="mr-4 p-2 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Brain className="h-6 w-6 text-purple-600 mr-2" />
                  Editar Paciente - Psicologia
                </h1>
                <p className="text-gray-600">Atualize as informações do paciente</p>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="bg-white shadow rounded-lg">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Dados Pessoais */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Dados Pessoais</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nome Completo *
                    </label>
                    <input
                      type="text"
                      name="nome"
                      required
                      value={formData.nome}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email *
                    </label>
                    <input
                      type="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Telefone
                    </label>
                    <input
                      type="tel"
                      name="telefone"
                      value={formData.telefone}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Data de Nascimento *
                    </label>
                    <input
                      type="date"
                      name="data_nascimento"
                      required
                      value={formData.data_nascimento}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sexo *
                    </label>
                    <select
                      name="sexo"
                      required
                      value={formData.sexo}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value="">Selecione</option>
                      <option value="masculino">Masculino</option>
                      <option value="feminino">Feminino</option>
                      <option value="outro">Outro</option>
                      <option value="nao_informar">Prefiro não informar</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Estado Civil
                    </label>
                    <select
                      name="estado_civil"
                      value={formData.estado_civil}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value="">Selecione</option>
                      <option value="solteiro">Solteiro(a)</option>
                      <option value="casado">Casado(a)</option>
                      <option value="divorciado">Divorciado(a)</option>
                      <option value="viuvo">Viúvo(a)</option>
                      <option value="uniao_estavel">União Estável</option>
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Profissão
                    </label>
                    <input
                      type="text"
                      name="profissao"
                      value={formData.profissao}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                </div>
              </div>

              {/* Informações Clínicas */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Informações Clínicas</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Motivo da Consulta *
                    </label>
                    <textarea
                      name="motivo_consulta"
                      required
                      rows={3}
                      value={formData.motivo_consulta}
                      onChange={handleChange}
                      placeholder="Descreva o motivo principal da consulta..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Histórico Familiar
                    </label>
                    <textarea
                      name="historico_familiar"
                      rows={3}
                      value={formData.historico_familiar}
                      onChange={handleChange}
                      placeholder="Histórico de transtornos mentais na família..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Medicamentos em Uso
                    </label>
                    <textarea
                      name="medicamentos"
                      rows={3}
                      value={formData.medicamentos}
                      onChange={handleChange}
                      placeholder="Liste os medicamentos que o paciente está tomando..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tratamentos Anteriores
                    </label>
                    <textarea
                      name="tratamentos_anteriores"
                      rows={3}
                      value={formData.tratamentos_anteriores}
                      onChange={handleChange}
                      placeholder="Descreva tratamentos psicológicos ou psiquiátricos anteriores..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Observações Gerais
                    </label>
                    <textarea
                      name="observacoes"
                      rows={3}
                      value={formData.observacoes}
                      onChange={handleChange}
                      placeholder="Informações adicionais relevantes..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-4 pt-6 border-t">
                <Link
                  href={`/psychology/patients/${id}`}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancelar
                </Link>
                <button
                  type="submit"
                  disabled={loading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {loading ? 'Salvando...' : 'Salvar Alterações'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  )
}
