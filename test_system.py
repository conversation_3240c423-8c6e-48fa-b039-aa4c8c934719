#!/usr/bin/env python3
"""
Script para testar o sistema Hypatium
"""

import requests
import json
import sys
from datetime import datetime

# Configurações
API_BASE_URL = "http://localhost:8000"
API_V1_URL = f"{API_BASE_URL}/api/v1"

def test_api_health():
    """Testar se a API está funcionando"""
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            print("✅ API está funcionando")
            return True
        else:
            print(f"❌ API retornou status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Não foi possível conectar à API")
        return False

def test_user_registration():
    """Testar registro de usuário"""
    user_data = {
        "nome": "Dr. <PERSON>",
        "email": "<EMAIL>",
        "tipo": "personal",
        "telefone": "(11) 99999-9999",
        "senha": "123456"
    }

    try:
        response = requests.post(f"{API_V1_URL}/auth/register", json=user_data)
        if response.status_code == 200:
            print("✅ Registro de usuário funcionando")
            return True
        else:
            print(f"❌ Erro no registro: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro na requisição de registro: {e}")
        return False

def test_user_login():
    """Testar login de usuário"""
    login_data = {
        "username": "<EMAIL>",
        "password": "admin123"
    }

    try:
        response = requests.post(
            f"{API_V1_URL}/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        if response.status_code == 200:
            token_data = response.json()
            print("✅ Login funcionando")
            return token_data.get("access_token")
        else:
            print(f"❌ Erro no login: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Erro na requisição de login: {e}")
        return None

def test_protected_endpoint(token):
    """Testar endpoint protegido"""
    headers = {"Authorization": f"Bearer {token}"}

    try:
        response = requests.get(f"{API_V1_URL}/users/me", headers=headers)
        if response.status_code == 200:
            user_data = response.json()
            print(f"✅ Endpoint protegido funcionando - Usuário: {user_data.get('nome')}")
            return True
        else:
            print(f"❌ Erro no endpoint protegido: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erro na requisição protegida: {e}")
        return False

def test_patient_creation(token):
    """Testar criação de paciente"""
    headers = {"Authorization": f"Bearer {token}"}
    patient_data = {
        "nome": "Maria Santos",
        "email": "<EMAIL>",
        "telefone": "(11) 88888-8888",
        "nascimento": "1990-05-15",
        "sexo": "feminino",
        "objetivo": "Emagrecimento e condicionamento físico",
        "responsavel_id": 1
    }

    try:
        response = requests.post(
            f"{API_V1_URL}/patients/",
            json=patient_data,
            headers=headers
        )

        if response.status_code == 200:
            patient = response.json()
            print(f"✅ Criação de paciente funcionando - ID: {patient.get('id')}")
            return patient.get('id')
        else:
            print(f"❌ Erro na criação de paciente: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Erro na requisição de criação de paciente: {e}")
        return None

def test_assessment_creation(token, patient_id):
    """Testar criação de avaliação física"""
    headers = {"Authorization": f"Bearer {token}"}
    assessment_data = {
        "peso": 70.5,
        "altura": 1.65,
        "data_avaliacao": datetime.now().isoformat(),
        "dobras_cutaneas": {
            "peitoral": 12.5,
            "abdominal": 18.0,
            "coxa": 15.5
        },
        "circunferencias": {
            "braço": 32.0,
            "cintura": 78.0,
            "quadril": 95.0
        },
        "metodo_utilizado": "pollock_3",
        "observacoes": "Primeira avaliação do paciente",
        "paciente_id": patient_id,
        "avaliador_id": 1
    }

    try:
        response = requests.post(
            f"{API_V1_URL}/assessments/",
            json=assessment_data,
            headers=headers
        )

        if response.status_code == 200:
            assessment = response.json()
            print(f"✅ Criação de avaliação funcionando - IMC: {assessment.get('imc'):.2f}")
            return assessment.get('id')
        else:
            print(f"❌ Erro na criação de avaliação: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Erro na requisição de criação de avaliação: {e}")
        return None

def test_calculation_endpoints(token):
    """Testar endpoints de cálculo"""
    headers = {"Authorization": f"Bearer {token}"}

    # Teste de cálculo de composição corporal
    body_comp_data = {
        "peso": 70.5,
        "altura": 1.65,
        "idade": 30,
        "sexo": "feminino",
        "dobras_cutaneas": {
            "triceps": 15.0,
            "supra_iliaca": 20.0,
            "coxa": 18.0
        },
        "metodo": "pollock_3"
    }

    try:
        response = requests.post(
            f"{API_V1_URL}/assessments/calculate/body-composition",
            json=body_comp_data,
            headers=headers
        )

        if response.status_code == 200:
            result = response.json()
            print(f"✅ Cálculo de composição corporal funcionando - IMC: {result.get('imc')}")
        else:
            print(f"❌ Erro no cálculo de composição corporal: {response.status_code}")
    except Exception as e:
        print(f"❌ Erro na requisição de cálculo: {e}")

    # Teste de cálculo metabólico
    metabolism_data = {
        "peso": 70.5,
        "altura": 1.65,
        "idade": 30,
        "sexo": "feminino",
        "nivel_atividade": "moderado",
        "objetivo": "deficit"
    }

    try:
        response = requests.post(
            f"{API_V1_URL}/assessments/calculate/metabolism",
            json=metabolism_data,
            headers=headers
        )

        if response.status_code == 200:
            result = response.json()
            print(f"✅ Cálculo metabólico funcionando - TMB: {result.get('tmb')}")
        else:
            print(f"❌ Erro no cálculo metabólico: {response.status_code}")
    except Exception as e:
        print(f"❌ Erro na requisição de cálculo metabólico: {e}")

def test_workout_endpoints(token, patient_id):
    """Testar endpoints de treinos"""
    headers = {"Authorization": f"Bearer {token}"}

    # Teste de criação de treino
    workout_data = {
        "nome": "Treino A - Peito e Tríceps",
        "descricao": "Treino focado em peito e tríceps",
        "data_inicio": datetime.now().isoformat(),
        "observacoes": "Treino para iniciantes",
        "paciente_id": patient_id,
        "personal_id": 1
    }

    try:
        response = requests.post(
            f"{API_V1_URL}/workouts/",
            json=workout_data,
            headers=headers
        )

        if response.status_code == 200:
            workout = response.json()
            print(f"✅ Criação de treino funcionando - ID: {workout.get('id')}")
            return workout.get('id')
        else:
            print(f"❌ Erro na criação de treino: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Erro na requisição de criação de treino: {e}")
        return None

def test_nutrition_endpoints(token, patient_id):
    """Testar endpoints de nutrição"""
    headers = {"Authorization": f"Bearer {token}"}

    # Teste de cálculo nutricional
    nutrition_calc_data = {
        "peso": 70.5,
        "altura": 1.65,
        "idade": 30,
        "sexo": "feminino",
        "nivel_atividade": "moderado",
        "objetivo": "deficit"
    }

    try:
        response = requests.post(
            f"{API_V1_URL}/nutrition/calculate/needs",
            json=nutrition_calc_data,
            headers=headers
        )

        if response.status_code == 200:
            result = response.json()
            print(f"✅ Cálculo nutricional funcionando - TMB: {result.get('tmb')}")
        else:
            print(f"❌ Erro no cálculo nutricional: {response.status_code}")
    except Exception as e:
        print(f"❌ Erro na requisição de cálculo nutricional: {e}")

    # Teste de criação automática de plano
    try:
        response = requests.post(
            f"{API_V1_URL}/nutrition/patient/{patient_id}/auto-create?objetivo=deficit",
            headers=headers
        )

        if response.status_code == 200:
            plan = response.json()
            print(f"✅ Criação automática de plano funcionando - Calorias: {plan.get('calorias_alvo')}")
        else:
            print(f"❌ Erro na criação automática de plano: {response.status_code}")
    except Exception as e:
        print(f"❌ Erro na requisição de criação automática: {e}")

def main():
    """Função principal de teste"""
    print("🧪 Iniciando testes do sistema Hypatium")
    print("=" * 50)

    # Teste 1: Verificar se API está funcionando
    if not test_api_health():
        print("\n❌ API não está funcionando. Verifique se o backend está rodando.")
        sys.exit(1)

    # Teste 2: Testar registro (pode falhar se usuário já existir)
    print("\n📝 Testando registro de usuário...")
    test_user_registration()

    # Teste 3: Testar login
    print("\n🔐 Testando login...")
    token = test_user_login()
    if not token:
        print("\n❌ Login falhou. Verifique se o usuário admin foi criado.")
        sys.exit(1)

    # Teste 4: Testar endpoint protegido
    print("\n🛡️  Testando endpoint protegido...")
    if not test_protected_endpoint(token):
        sys.exit(1)

    # Teste 5: Testar criação de paciente
    print("\n👤 Testando criação de paciente...")
    patient_id = test_patient_creation(token)

    # Teste 6: Testar criação de avaliação física
    if patient_id:
        print("\n📊 Testando criação de avaliação física...")
        test_assessment_creation(token, patient_id)

    # Teste 7: Testar endpoints de cálculo
    print("\n🧮 Testando endpoints de cálculo...")
    test_calculation_endpoints(token)

    # Teste 8: Testar endpoints de treinos
    if patient_id:
        print("\n💪 Testando endpoints de treinos...")
        test_workout_endpoints(token, patient_id)

    # Teste 9: Testar endpoints de nutrição
    if patient_id:
        print("\n🥗 Testando endpoints de nutrição...")
        test_nutrition_endpoints(token, patient_id)

    print("\n" + "=" * 50)
    print("✅ Testes concluídos!")
    print("\n💡 Próximos passos:")
    print("1. Acesse http://localhost:3000 para testar o frontend")
    print("2. Faça <NAME_EMAIL> / admin123")
    print("3. Explore o dashboard e funcionalidades")
    print("4. Teste as páginas de pacientes em /personal/patients")

if __name__ == "__main__":
    main()
