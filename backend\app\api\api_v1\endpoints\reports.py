"""
Endpoints de relatórios automáticos
"""

from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.models.models import User
from app.schemas.report import (
    AutomaticReport,
    AutomaticReportCreate,
    AutomaticReportUpdate,
    ReportTemplate,
    ReportData
)
from app.services.report_service import ReportService

router = APIRouter()

@router.post("/", response_model=AutomaticReport)
def create_report(
    *,
    db: Session = Depends(get_db),
    report_in: AutomaticReportCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Criar novo relatório
    """
    report_service = ReportService(db)

    # Definir profissional como usuário atual se não especificado
    if not report_in.profissional_id:
        report_in.profissional_id = current_user.id

    try:
        report = report_service.create_report(report_in)
        return report
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/patient/{patient_id}", response_model=List[AutomaticReport])
def read_reports_by_patient(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter relatórios de um paciente
    """
    report_service = ReportService(db)
    reports = report_service.get_reports_by_patient(patient_id, skip=skip, limit=limit)
    return reports

@router.get("/{report_id}", response_model=AutomaticReport)
def read_report(
    *,
    db: Session = Depends(get_db),
    report_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter relatório por ID
    """
    report_service = ReportService(db)
    report = report_service.get_report(report_id)

    if not report:
        raise HTTPException(status_code=404, detail="Relatório não encontrado")

    return report

@router.post("/generate/{patient_id}", response_model=AutomaticReport)
async def generate_ai_report(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    report_type: str = Query("comprehensive", pattern="^(comprehensive|physical_only|psychological_only|nutritional_only)$"),
    period_days: int = Query(30, ge=7, le=365),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Gerar relatório automático com IA
    """
    report_service = ReportService(db)

    try:
        report = await report_service.generate_ai_report(
            patient_id=patient_id,
            professional_id=current_user.id,
            report_type=report_type,
            period_days=period_days
        )
        return report
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/templates", response_model=List[Dict[str, Any]])
def get_report_templates(
    *,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter templates de relatórios disponíveis
    """
    report_service = ReportService(None)  # Não precisa de DB para templates
    templates = report_service.get_report_templates()
    return templates
