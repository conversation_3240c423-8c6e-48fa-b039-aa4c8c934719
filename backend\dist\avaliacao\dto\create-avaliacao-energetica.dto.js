"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAvaliacaoEnergeticaDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class CreateAvaliacaoEnergeticaDto {
    tmbFAO_OMS;
    tmbHarrisBenedict;
    tmbMifflin;
    tmbCunningham;
    tmbTinsleyMLG;
    tmbTinsleyPeso;
    fatorAtividade;
    percentualDeficitSuperavit;
}
exports.CreateAvaliacaoEnergeticaDto = CreateAvaliacaoEnergeticaDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TMB FAO/OMS em kcal',
        example: 1800,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(800),
    (0, class_validator_1.Max)(5000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoEnergeticaDto.prototype, "tmbFAO_OMS", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TMB Harris-Benedict em kcal',
        example: 1850,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(800),
    (0, class_validator_1.Max)(5000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoEnergeticaDto.prototype, "tmbHarrisBenedict", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TMB Mifflin em kcal',
        example: 1820,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(800),
    (0, class_validator_1.Max)(5000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoEnergeticaDto.prototype, "tmbMifflin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TMB Cunningham em kcal',
        example: 1900,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(800),
    (0, class_validator_1.Max)(5000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoEnergeticaDto.prototype, "tmbCunningham", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TMB Tinsley MLG em kcal',
        example: 1880,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(800),
    (0, class_validator_1.Max)(5000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoEnergeticaDto.prototype, "tmbTinsleyMLG", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TMB Tinsley Peso em kcal',
        example: 1820,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(800),
    (0, class_validator_1.Max)(5000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoEnergeticaDto.prototype, "tmbTinsleyPeso", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Fator de atividade (1.2 = sedentário, 1.4 = leve, 1.6 = moderado, 1.8 = intenso, 2.0 = muito intenso)',
        example: 1.4,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1.0),
    (0, class_validator_1.Max)(2.5),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoEnergeticaDto.prototype, "fatorAtividade", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentual de déficit (-) ou superávit (+) calórico (-30% a +30%)',
        example: -10,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(-50),
    (0, class_validator_1.Max)(50),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateAvaliacaoEnergeticaDto.prototype, "percentualDeficitSuperavit", void 0);
//# sourceMappingURL=create-avaliacao-energetica.dto.js.map