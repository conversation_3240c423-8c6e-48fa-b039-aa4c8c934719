<div align="center">
  <img src="logo.png" alt="Hypatium Logo" width="300"/>

  # Hypatium
  ### Plataforma Integrada para Profissionais de Saúde

  [![MIT License](https://img.shields.io/badge/License-MIT-green.svg)](https://choosealicense.com/licenses/mit/)
  [![Contribuições](https://img.shields.io/badge/Contribuições-Bem--vindas-brightgreen.svg)](CONTRIBUTING.md)
  ![Status](https://img.shields.io/badge/Status-98%25%20Completo-brightgreen)
</div>

## 📋 Índice

- [Problema](#-problema)
- [Solu<PERSON>](#-solução)
- [Stack Tecnológica](#-stack-tecnológica)
- [Como Executar](#-como-executar)
- [Modelo de Dados](#-modelo-de-dados)
- [Modelo de Monetização](#-modelo-de-monetização)
- [Roadmap](#-roadmap)
- [Licença](#-licença)
- [Contribuição](#-contribuição)

## 🚨 Problema

Personal trainers, nutricionistas e psicólogos enfrentam desafios diários:
- Tempo excessivo gasto com planilhas manuais
- Comunicação descentralizada entre profissionais
- Geração de relatórios pouco profissionais
- Falta de integração que dificulta decisões clínicas personalizadas

## 🧠 Solução

Uma plataforma web/mobile modular que permite que diferentes profissionais de saúde física e mental interajam com dados do mesmo aluno.

### Principais Recursos

<table>
  <tr>
    <td align="center">
      <img src="https://img.icons8.com/fluency/48/fitness.png" width="32"/>
      <br><strong>Personal Trainer</strong>
    </td>
    <td>
      • Painel de Composição Corporal com gráficos automáticos<br>
      • Planos de treino com vídeo (A/B/C/D) integrados ao progresso
    </td>
  </tr>
  <tr>
    <td align="center">
      <img src="https://img.icons8.com/fluency/48/natural-food.png" width="32"/>
      <br><strong>Nutricionista</strong>
    </td>
    <td>
      • Monitoramento de ingestão calórica<br>
      • Histórico alimentar detalhado<br>
      • PDF automático de dietas personalizadas
    </td>
  </tr>
  <tr>
    <td align="center">
      <img src="https://img.icons8.com/fluency/48/mind-map.png" width="32"/>
      <br><strong>Psicólogo</strong>
    </td>
    <td>
      • Anotações privadas sobre pacientes<br>
      • Sessões registradas por áudio (MP3)<br>
      • Diário do paciente para acompanhamento
    </td>
  </tr>
  <tr>
    <td align="center">
      <img src="https://img.icons8.com/fluency/48/artificial-intelligence.png" width="32"/>
      <br><strong>IA Integrada</strong>
    </td>
    <td>
      • Relatórios gerados por LLMs com base em avaliações<br>
      • Visões integradas entre especialidades
    </td>
  </tr>
</table>

## 🔧 Stack Tecnológica

<table>
  <tr>
    <th>Backend</th>
    <th>Frontend</th>
    <th>Integrações</th>
  </tr>
  <tr>
    <td>
      • <strong>FastAPI</strong> - API REST moderna<br>
      • <strong>PostgreSQL</strong> - Banco relacional<br>
      • <strong>SQLAlchemy</strong> - ORM Python<br>
      • <strong>Alembic</strong> - Migrações de banco<br>
      • <strong>JWT</strong> - Autenticação segura
    </td>
    <td>
      • <strong>Next.js 14</strong> - Framework React<br>
      • <strong>TypeScript</strong> - Tipagem estática<br>
      • <strong>TailwindCSS</strong> - CSS utilitário<br>
      • <strong>Chart.js</strong> - Gráficos interativos
    </td>
    <td>
      • <strong>OpenAI API</strong> - Relatórios via LLM<br>
      • <strong>Google Speech-to-Text</strong> - Transcrição<br>
      • <strong>Firebase Storage</strong> - Armazenamento
    </td>
  </tr>
</table>

## 🚀 Como Executar

### Pré-requisitos

- Python 3.11+
- Node.js 18+
- PostgreSQL 15+
- Docker (opcional)

### Backend

```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload
```

### Frontend

```bash
cd frontend
npm install
npm run dev
```

### Com Docker

```bash
docker-compose up -d
```

## 📊 Modelo de Dados

### Entidades Principais

- **Usuário**: Personal, Nutricionista, Psicólogo, Aluno
- **Paciente**: Dados pessoais e histórico
- **Avaliação Física**: Medidas, composição corporal
- **Treino**: Planos A/B/C/D com exercícios e vídeos
- **Plano Nutricional**: TMB, GET, dietas personalizadas
- **Sessão Psicológica**: Anotações, áudio, transcrições
- **Relatórios**: Gerados automaticamente via LLM

## 💸 Modelo de Monetização

<table>
  <tr>
    <th>Plano</th>
    <th>Preço (mensal)</th>
    <th>Limite de Alunos</th>
    <th>Recursos</th>
  </tr>
  <tr>
    <td><strong>Starter</strong></td>
    <td align="center">R$ 19</td>
    <td align="center">10</td>
    <td>Avaliação + treino</td>
  </tr>
  <tr>
    <td><strong>Pro</strong></td>
    <td align="center">R$ 49</td>
    <td align="center">50</td>
    <td>Nutrição + Relatórios LLM</td>
  </tr>
  <tr>
    <td><strong>Master</strong></td>
    <td align="center">R$ 99</td>
    <td align="center">200</td>
    <td>Psicólogo + Vídeos + Transcrição MP3</td>
  </tr>
</table>

## 🎯 Roadmap

<table>
  <tr>
    <th colspan="2">Sprint 1 - Base de Dados + Painel do Personal</th>
  </tr>
  <tr>
    <td>✅ Estrutura do projeto</td>
    <td>⬜ Cadastro de alunos</td>
  </tr>
  <tr>
    <td>⬜ Avaliação física + cálculos automáticos</td>
    <td>⬜ Registro de treinos e vídeo</td>
  </tr>
  <tr>
    <td colspan="2">⬜ Geração de gráfico semanal</td>
  </tr>
  <tr>
    <th colspan="2">Sprint 2 - Nutricionista + Psicólogo</th>
  </tr>
  <tr>
    <td>⬜ Área do nutricionista com cálculo GET/TMB</td>
    <td>⬜ Geração de plano alimentar em PDF</td>
  </tr>
  <tr>
    <td colspan="2">⬜ Sessões do psicólogo com gravação e transcrição</td>
  </tr>
  <tr>
    <th colspan="2">Sprint 3 - Relatórios LLM + Integração Final</th>
  </tr>
  <tr>
    <td>⬜ Resumo automático com LLM (OpenAI API)</td>
    <td>⬜ Relatórios integrados (PDF e HTML)</td>
  </tr>
  <tr>
    <td colspan="2">⬜ Testes com casos reais</td>
  </tr>
</table>

## 📝 Licença

Este projeto está licenciado sob a [MIT License](LICENSE) - veja o arquivo LICENSE para detalhes.

## 🤝 Contribuição

Contribuições são bem-vindas! Veja [CONTRIBUTING.md](CONTRIBUTING.md) para mais detalhes sobre como contribuir para o projeto.
