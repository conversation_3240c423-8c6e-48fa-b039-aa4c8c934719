import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<{
        access_token: any;
        user: {
            id: any;
            email: any;
            nome: any;
            userType: any;
        };
    }>;
    registerPersonal(registerDto: RegisterDto): Promise<any>;
    verify(req: any): Promise<{
        valid: boolean;
        user: any;
    }>;
}
