"""
Serviço para gerenciamento de planos nutricionais
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime

from app.models.models import NutritionalPlan, Patient, PhysicalAssessment
from app.schemas.nutrition import (
    NutritionalPlanCreate, 
    NutritionalPlanUpdate,
    NutritionalCalculation,
    MacronutrientDistribution,
    DailyMealPlan,
    WeeklyMealPlan
)
from app.services.calculation_service import CalculationService

class NutritionService:
    def __init__(self, db: Session):
        self.db = db
        self.calc_service = CalculationService()

    def get_plan(self, plan_id: int) -> Optional[NutritionalPlan]:
        """Obter plano nutricional por ID"""
        return self.db.query(NutritionalPlan).filter(
            NutritionalPlan.id == plan_id
        ).first()

    def get_plans_by_patient(self, patient_id: int, skip: int = 0, limit: int = 100) -> List[NutritionalPlan]:
        """Obter planos nutricionais de um paciente"""
        return self.db.query(NutritionalPlan).filter(
            NutritionalPlan.paciente_id == patient_id
        ).order_by(NutritionalPlan.data_criacao.desc()).offset(skip).limit(limit).all()

    def get_active_plan_by_patient(self, patient_id: int) -> Optional[NutritionalPlan]:
        """Obter plano nutricional ativo de um paciente"""
        return self.db.query(NutritionalPlan).filter(
            NutritionalPlan.paciente_id == patient_id,
            NutritionalPlan.ativo == True
        ).order_by(NutritionalPlan.data_criacao.desc()).first()

    def get_plans_by_nutritionist(self, nutritionist_id: int, skip: int = 0, limit: int = 100) -> List[NutritionalPlan]:
        """Obter planos criados por um nutricionista"""
        return self.db.query(NutritionalPlan).filter(
            NutritionalPlan.nutricionista_id == nutritionist_id
        ).order_by(NutritionalPlan.data_criacao.desc()).offset(skip).limit(limit).all()

    def create_plan(self, plan_in: NutritionalPlanCreate) -> NutritionalPlan:
        """Criar novo plano nutricional"""
        # Verificar se paciente existe
        patient = self.db.query(Patient).filter(Patient.id == plan_in.paciente_id).first()
        if not patient:
            raise ValueError("Paciente não encontrado")

        # Desativar planos anteriores se necessário
        if plan_in.ativo:
            self.db.query(NutritionalPlan).filter(
                NutritionalPlan.paciente_id == plan_in.paciente_id,
                NutritionalPlan.ativo == True
            ).update({"ativo": False})

        db_plan = NutritionalPlan(**plan_in.dict())
        self.db.add(db_plan)
        self.db.commit()
        self.db.refresh(db_plan)
        return db_plan

    def update_plan(self, plan_id: int, plan_in: NutritionalPlanUpdate) -> Optional[NutritionalPlan]:
        """Atualizar plano nutricional"""
        db_plan = self.get_plan(plan_id)
        if not db_plan:
            return None

        update_data = plan_in.dict(exclude_unset=True)
        
        # Se ativando este plano, desativar outros do mesmo paciente
        if update_data.get("ativo") == True:
            self.db.query(NutritionalPlan).filter(
                NutritionalPlan.paciente_id == db_plan.paciente_id,
                NutritionalPlan.id != plan_id,
                NutritionalPlan.ativo == True
            ).update({"ativo": False})

        for field, value in update_data.items():
            setattr(db_plan, field, value)

        self.db.commit()
        self.db.refresh(db_plan)
        return db_plan

    def delete_plan(self, plan_id: int) -> bool:
        """Deletar plano nutricional"""
        db_plan = self.get_plan(plan_id)
        if not db_plan:
            return False

        self.db.delete(db_plan)
        self.db.commit()
        return True

    def deactivate_plan(self, plan_id: int) -> Optional[NutritionalPlan]:
        """Desativar plano nutricional"""
        db_plan = self.get_plan(plan_id)
        if not db_plan:
            return None

        db_plan.ativo = False
        self.db.commit()
        self.db.refresh(db_plan)
        return db_plan

    def calculate_nutritional_needs(self, calculation_data: NutritionalCalculation) -> Dict[str, Any]:
        """Calcular necessidades nutricionais"""
        
        # Calcular TMB
        tmb = self.calc_service.calcular_tmb_mifflin_st_jeor(
            calculation_data.peso,
            calculation_data.altura,
            calculation_data.idade,
            calculation_data.sexo
        )

        # Calcular GET
        get = self.calc_service.calcular_get(tmb, calculation_data.nivel_atividade)

        # Calcular calorias para objetivo
        if calculation_data.percentual_ajuste is not None:
            calorias_alvo = get * (1 + calculation_data.percentual_ajuste / 100)
        else:
            calorias_alvo = self.calc_service.calcular_calorias_objetivo(
                get,
                calculation_data.objetivo
            )

        return {
            "tmb": round(tmb, 0),
            "get": round(get, 0),
            "calorias_alvo": round(calorias_alvo, 0),
            "nivel_atividade": calculation_data.nivel_atividade,
            "objetivo": calculation_data.objetivo,
            "percentual_ajuste": calculation_data.percentual_ajuste
        }

    def calculate_macronutrients(self, distribution: MacronutrientDistribution) -> Dict[str, Any]:
        """Calcular distribuição de macronutrientes"""
        
        # Verificar se percentuais somam 100%
        total_pct = distribution.proteinas_percentual + distribution.carboidratos_percentual + distribution.gorduras_percentual
        if abs(total_pct - 100) > 0.1:
            raise ValueError("Percentuais de macronutrientes devem somar 100%")

        macros = self.calc_service.calcular_macronutrientes(
            distribution.calorias_totais,
            distribution.proteinas_percentual,
            distribution.carboidratos_percentual,
            distribution.gorduras_percentual
        )

        return macros

    def create_plan_from_assessment(self, patient_id: int, nutritionist_id: int, objetivo: str = "manutencao") -> Optional[NutritionalPlan]:
        """Criar plano nutricional baseado na última avaliação física"""
        
        # Buscar última avaliação física
        latest_assessment = self.db.query(PhysicalAssessment).filter(
            PhysicalAssessment.paciente_id == patient_id
        ).order_by(PhysicalAssessment.data_avaliacao.desc()).first()

        if not latest_assessment:
            raise ValueError("Nenhuma avaliação física encontrada para este paciente")

        # Buscar dados do paciente
        patient = self.db.query(Patient).filter(Patient.id == patient_id).first()
        if not patient or not patient.nascimento or not patient.sexo:
            raise ValueError("Dados insuficientes do paciente para cálculo nutricional")

        # Calcular idade
        idade = self.calc_service.calcular_idade(patient.nascimento.date())

        # Usar TMB e GET da avaliação se disponíveis, senão calcular
        if latest_assessment.tmb and latest_assessment.get:
            tmb = latest_assessment.tmb
            get = latest_assessment.get
        else:
            tmb = self.calc_service.calcular_tmb_mifflin_st_jeor(
                latest_assessment.peso,
                latest_assessment.altura,
                idade,
                patient.sexo.value
            )
            get = self.calc_service.calcular_get(tmb, "moderado")  # Padrão

        # Calcular calorias para objetivo
        calorias_alvo = self.calc_service.calcular_calorias_objetivo(get, objetivo)

        # Calcular macronutrientes (distribuição padrão)
        macros = self.calc_service.calcular_macronutrientes(calorias_alvo)

        # Criar plano nutricional
        plan_data = {
            "data_criacao": datetime.utcnow(),
            "tmb_calculado": tmb,
            "get_calculado": get,
            "calorias_alvo": calorias_alvo,
            "proteinas_g": macros["proteinas_g"],
            "carboidratos_g": macros["carboidratos_g"],
            "gorduras_g": macros["gorduras_g"],
            "observacoes": f"Plano gerado automaticamente baseado na avaliação de {latest_assessment.data_avaliacao.strftime('%d/%m/%Y')}",
            "paciente_id": patient_id,
            "nutricionista_id": nutritionist_id,
            "ativo": True
        }

        # Desativar planos anteriores
        self.db.query(NutritionalPlan).filter(
            NutritionalPlan.paciente_id == patient_id,
            NutritionalPlan.ativo == True
        ).update({"ativo": False})

        db_plan = NutritionalPlan(**plan_data)
        self.db.add(db_plan)
        self.db.commit()
        self.db.refresh(db_plan)
        return db_plan

    def get_plan_statistics(self, plan_id: int) -> Dict[str, Any]:
        """Obter estatísticas do plano nutricional"""
        plan = self.get_plan(plan_id)
        if not plan:
            return {}

        # Calcular distribuição percentual de macronutrientes
        total_calorias = plan.calorias_alvo or 0
        
        if total_calorias > 0:
            proteinas_pct = (plan.proteinas_g * 4 / total_calorias) * 100 if plan.proteinas_g else 0
            carboidratos_pct = (plan.carboidratos_g * 4 / total_calorias) * 100 if plan.carboidratos_g else 0
            gorduras_pct = (plan.gorduras_g * 9 / total_calorias) * 100 if plan.gorduras_g else 0
        else:
            proteinas_pct = carboidratos_pct = gorduras_pct = 0

        # Calcular déficit/superávit
        deficit_superavit = 0
        if plan.get_calculado and plan.calorias_alvo:
            deficit_superavit = plan.calorias_alvo - plan.get_calculado

        return {
            "calorias_alvo": plan.calorias_alvo,
            "tmb": plan.tmb_calculado,
            "get": plan.get_calculado,
            "deficit_superavit": round(deficit_superavit, 0),
            "macronutrientes": {
                "proteinas": {
                    "gramas": plan.proteinas_g,
                    "percentual": round(proteinas_pct, 1),
                    "calorias": round(plan.proteinas_g * 4, 0) if plan.proteinas_g else 0
                },
                "carboidratos": {
                    "gramas": plan.carboidratos_g,
                    "percentual": round(carboidratos_pct, 1),
                    "calorias": round(plan.carboidratos_g * 4, 0) if plan.carboidratos_g else 0
                },
                "gorduras": {
                    "gramas": plan.gorduras_g,
                    "percentual": round(gorduras_pct, 1),
                    "calorias": round(plan.gorduras_g * 9, 0) if plan.gorduras_g else 0
                }
            },
            "created_date": plan.created_at,
            "last_updated": plan.updated_at,
            "active": plan.ativo
        }

    def generate_meal_suggestions(self, calorias_alvo: float, num_refeicoes: int = 5) -> Dict[str, Any]:
        """Gerar sugestões de distribuição de refeições"""
        
        # Distribuição padrão de calorias por refeição
        distribuicao_padrao = {
            3: [0.30, 0.40, 0.30],  # café, almoço, jantar
            4: [0.25, 0.15, 0.35, 0.25],  # café, lanche, almoço, jantar
            5: [0.20, 0.10, 0.30, 0.15, 0.25],  # café, lanche1, almoço, lanche2, jantar
            6: [0.20, 0.10, 0.25, 0.15, 0.20, 0.10]  # café, lanche1, almoço, lanche2, jantar, ceia
        }

        if num_refeicoes not in distribuicao_padrao:
            num_refeicoes = 5

        distribuicao = distribuicao_padrao[num_refeicoes]
        
        nomes_refeicoes = {
            3: ["Café da manhã", "Almoço", "Jantar"],
            4: ["Café da manhã", "Lanche da manhã", "Almoço", "Jantar"],
            5: ["Café da manhã", "Lanche da manhã", "Almoço", "Lanche da tarde", "Jantar"],
            6: ["Café da manhã", "Lanche da manhã", "Almoço", "Lanche da tarde", "Jantar", "Ceia"]
        }

        refeicoes = []
        for i, percentual in enumerate(distribuicao):
            calorias_refeicao = calorias_alvo * percentual
            refeicoes.append({
                "nome": nomes_refeicoes[num_refeicoes][i],
                "calorias": round(calorias_refeicao, 0),
                "percentual": round(percentual * 100, 1)
            })

        return {
            "total_calorias": calorias_alvo,
            "numero_refeicoes": num_refeicoes,
            "refeicoes": refeicoes
        }
