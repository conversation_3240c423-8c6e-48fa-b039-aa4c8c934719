import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  ArrowLeft,
  Plus,
  Utensils,
  Calendar,
  Target,
  Clock,
  Eye,
  Edit,
  Trash2,
  Download
} from 'lucide-react'

interface NutritionPlan {
  id: number
  nome: string
  descricao: string
  objetivo: string
  calorias_totais: number
  proteinas: number
  carboidratos: number
  gorduras: number
  refeicoes_count: number
  data_criacao: string
  ativo: boolean
  validade: string
}

interface Patient {
  id: number
  nome: string
  email: string
}

export default function PatientNutritionPlans() {
  const router = useRouter()
  const { id } = router.query
  const [patient, setPatient] = useState<Patient | null>(null)
  const [plans, setPlans] = useState<NutritionPlan[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (id) {
      fetchData()
    }
  }, [id])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Mock data - em produção viria da API
      setPatient({
        id: Number(id),
        nome: 'Maria Silva',
        email: '<EMAIL>'
      })

      setPlans([
        {
          id: 1,
          nome: 'Plano de Emagrecimento',
          descricao: 'Plano alimentar focado na perda de peso saudável',
          objetivo: 'Perda de peso',
          calorias_totais: 1800,
          proteinas: 135,
          carboidratos: 180,
          gorduras: 60,
          refeicoes_count: 6,
          data_criacao: '2024-01-20',
          ativo: true,
          validade: '2024-03-20'
        },
        {
          id: 2,
          nome: 'Plano de Manutenção',
          descricao: 'Plano para manutenção do peso atual',
          objetivo: 'Manutenção',
          calorias_totais: 2200,
          proteinas: 165,
          carboidratos: 275,
          gorduras: 73,
          refeicoes_count: 5,
          data_criacao: '2024-01-01',
          ativo: false,
          validade: '2024-02-01'
        }
      ])
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const isExpired = (validadeString: string) => {
    return new Date(validadeString) < new Date()
  }

  const getObjectiveColor = (objective: string) => {
    switch (objective.toLowerCase()) {
      case 'perda de peso':
        return 'bg-red-100 text-red-800'
      case 'ganho de peso':
        return 'bg-blue-100 text-blue-800'
      case 'manutenção':
        return 'bg-green-100 text-green-800'
      case 'ganho de massa':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Planos Nutricionais - {patient?.nome} | Hypatium</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link 
                  href={`/nutrition/patients/${id}`}
                  className="mr-4 p-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    <Utensils className="h-6 w-6 text-green-600 mr-2" />
                    Planos Nutricionais - {patient?.nome}
                  </h1>
                  <p className="text-gray-600">Gerencie os planos alimentares do paciente</p>
                </div>
              </div>
              <Link
                href={`/nutrition/plans/new?patient=${id}`}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Novo Plano
              </Link>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {plans.length === 0 ? (
              <div className="bg-white shadow rounded-lg p-8 text-center">
                <Utensils className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum plano nutricional criado</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Comece criando o primeiro plano alimentar para este paciente.
                </p>
                <div className="mt-6">
                  <Link
                    href={`/nutrition/plans/new?patient=${id}`}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Criar Primeiro Plano
                  </Link>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {plans.map((plan) => (
                  <div key={plan.id} className="bg-white shadow rounded-lg overflow-hidden">
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900">
                            {plan.nome}
                          </h3>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getObjectiveColor(plan.objetivo)}`}>
                            {plan.objetivo}
                          </span>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            plan.ativo ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {plan.ativo ? 'Ativo' : 'Inativo'}
                          </span>
                          {isExpired(plan.validade) && (
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                              Expirado
                            </span>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          <Link
                            href={`/nutrition/plans/${plan.id}`}
                            className="text-green-600 hover:text-green-900"
                            title="Visualizar"
                          >
                            <Eye className="h-4 w-4" />
                          </Link>
                          <Link
                            href={`/nutrition/plans/${plan.id}/edit`}
                            className="text-blue-600 hover:text-blue-900"
                            title="Editar"
                          >
                            <Edit className="h-4 w-4" />
                          </Link>
                          <button
                            className="text-purple-600 hover:text-purple-900"
                            title="Download PDF"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                          <button
                            className="text-red-600 hover:text-red-900"
                            title="Excluir"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      <p className="text-sm text-gray-600 mb-4">{plan.descricao}</p>

                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{plan.calorias_totais}</p>
                          <p className="text-sm text-gray-500">Calorias</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-blue-600">{plan.proteinas}g</p>
                          <p className="text-sm text-gray-500">Proteínas</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-yellow-600">{plan.carboidratos}g</p>
                          <p className="text-sm text-gray-500">Carboidratos</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-red-600">{plan.gorduras}g</p>
                          <p className="text-sm text-gray-500">Gorduras</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-green-600">{plan.refeicoes_count}</p>
                          <p className="text-sm text-gray-500">Refeições</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-500 border-t border-gray-200 pt-4">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Criado em {formatDate(plan.data_criacao)}
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            Válido até {formatDate(plan.validade)}
                          </div>
                        </div>
                        <div className="flex items-center">
                          <Target className="h-4 w-4 mr-1" />
                          {plan.refeicoes_count} refeições/dia
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}
