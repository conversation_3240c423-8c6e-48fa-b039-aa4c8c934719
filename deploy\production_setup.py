#!/usr/bin/env python3
"""
Script de configuração para ambiente de produção
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Dict, Any

class ProductionSetup:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.config = {}
        
    def check_requirements(self) -> bool:
        """Verificar requisitos do sistema"""
        print("🔍 Verificando requisitos do sistema...")
        
        requirements = {
            "python": "3.8+",
            "docker": "20.0+",
            "docker-compose": "1.29+",
            "nginx": "1.18+",
            "postgresql": "12+"
        }
        
        missing = []
        
        # Verificar Python
        if sys.version_info < (3, 8):
            missing.append("Python 3.8+")
        
        # Verificar Docker
        try:
            result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                missing.append("Docker")
        except FileNotFoundError:
            missing.append("Docker")
        
        # Verificar Docker Compose
        try:
            result = subprocess.run(["docker-compose", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                missing.append("Docker Compose")
        except FileNotFoundError:
            missing.append("Docker Compose")
        
        if missing:
            print(f"❌ Requisitos faltando: {', '.join(missing)}")
            return False
        
        print("✅ Todos os requisitos atendidos")
        return True
    
    def setup_environment(self) -> bool:
        """Configurar variáveis de ambiente"""
        print("⚙️ Configurando variáveis de ambiente...")
        
        env_template = {
            # Database
            "DATABASE_URL": "**************************************/hypatium_prod",
            "POSTGRES_USER": "hypatium",
            "POSTGRES_PASSWORD": "secure_password_here",
            "POSTGRES_DB": "hypatium_prod",
            
            # Security
            "SECRET_KEY": "your-super-secret-key-here-change-in-production",
            "ALGORITHM": "HS256",
            "ACCESS_TOKEN_EXPIRE_MINUTES": "30",
            
            # API
            "API_V1_STR": "/api/v1",
            "PROJECT_NAME": "Hypatium",
            "VERSION": "1.0.0",
            "DESCRIPTION": "Plataforma modular para profissionais de saúde",
            
            # CORS
            "BACKEND_CORS_ORIGINS": '["http://localhost:3000","https://yourdomain.com"]',
            
            # Email (SMTP)
            "SMTP_HOST": "smtp.gmail.com",
            "SMTP_PORT": "587",
            "SMTP_USER": "<EMAIL>",
            "SMTP_PASSWORD": "your-app-password",
            "EMAILS_FROM_EMAIL": "<EMAIL>",
            "EMAILS_FROM_NAME": "Hypatium",
            
            # OpenAI
            "OPENAI_API_KEY": "your-openai-api-key-here",
            
            # Firebase (for push notifications)
            "FIREBASE_CREDENTIALS": "path/to/firebase-credentials.json",
            
            # File Upload
            "UPLOAD_DIR": "/app/uploads",
            "MAX_FILE_SIZE": "50",  # MB
            
            # Redis (for caching)
            "REDIS_URL": "redis://redis:6379/0",
            
            # Monitoring
            "SENTRY_DSN": "your-sentry-dsn-here",
            
            # Environment
            "ENVIRONMENT": "production",
            "DEBUG": "false",
            "TESTING": "false"
        }
        
        env_file = self.project_root / ".env.production"
        
        if env_file.exists():
            print(f"⚠️ Arquivo {env_file} já existe. Sobrescrever? (y/N): ", end="")
            if input().lower() != 'y':
                print("Mantendo arquivo existente.")
                return True
        
        with open(env_file, 'w') as f:
            f.write("# Hypatium Production Environment\n")
            f.write("# IMPORTANTE: Altere as senhas e chaves antes de usar em produção!\n\n")
            
            for key, value in env_template.items():
                f.write(f"{key}={value}\n")
        
        print(f"✅ Arquivo de ambiente criado: {env_file}")
        print("⚠️ IMPORTANTE: Edite o arquivo .env.production com suas configurações reais!")
        return True
    
    def setup_docker_production(self) -> bool:
        """Configurar Docker para produção"""
        print("🐳 Configurando Docker para produção...")
        
        docker_compose_prod = """version: '3.8'

services:
  # Database
  db:
    image: postgres:14
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - hypatium-network
    restart: unless-stopped

  # Redis for caching
  redis:
    image: redis:7-alpine
    networks:
      - hypatium-network
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - REDIS_URL=${REDIS_URL}
      - ENVIRONMENT=production
    volumes:
      - ./uploads:/app/uploads
      - ./static:/app/static
    depends_on:
      - db
      - redis
    networks:
      - hypatium-network
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    environment:
      - NEXT_PUBLIC_API_URL=https://api.yourdomain.com
      - NODE_ENV=production
    networks:
      - hypatium-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./static:/var/www/static
    depends_on:
      - backend
      - frontend
    networks:
      - hypatium-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  hypatium-network:
    driver: bridge
"""
        
        compose_file = self.project_root / "docker-compose.prod.yml"
        with open(compose_file, 'w') as f:
            f.write(docker_compose_prod)
        
        print(f"✅ Docker Compose para produção criado: {compose_file}")
        return True
    
    def setup_nginx(self) -> bool:
        """Configurar Nginx"""
        print("🌐 Configurando Nginx...")
        
        nginx_dir = self.project_root / "nginx"
        nginx_dir.mkdir(exist_ok=True)
        
        nginx_config = """events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }
    
    upstream frontend {
        server frontend:3000;
    }
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    
    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;
        
        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
        
        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Auth routes with stricter rate limiting
        location /api/v1/auth/login {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Static files
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
"""
        
        nginx_config_file = nginx_dir / "nginx.conf"
        with open(nginx_config_file, 'w') as f:
            f.write(nginx_config)
        
        # Criar diretório SSL
        ssl_dir = nginx_dir / "ssl"
        ssl_dir.mkdir(exist_ok=True)
        
        print(f"✅ Configuração do Nginx criada: {nginx_config_file}")
        print("⚠️ Configure seus certificados SSL no diretório nginx/ssl/")
        return True
    
    def create_dockerfiles(self) -> bool:
        """Criar Dockerfiles para produção"""
        print("📦 Criando Dockerfiles para produção...")
        
        # Backend Dockerfile
        backend_dockerfile = """FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create uploads directory
RUN mkdir -p /app/uploads /app/static

# Expose port
EXPOSE 8000

# Run application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
"""
        
        backend_dir = self.project_root / "backend"
        backend_dockerfile_path = backend_dir / "Dockerfile.prod"
        with open(backend_dockerfile_path, 'w') as f:
            f.write(backend_dockerfile)
        
        # Frontend Dockerfile
        frontend_dockerfile = """FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production image
FROM node:18-alpine AS runner

WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
"""
        
        frontend_dir = self.project_root / "frontend"
        frontend_dockerfile_path = frontend_dir / "Dockerfile.prod"
        with open(frontend_dockerfile_path, 'w') as f:
            f.write(frontend_dockerfile)
        
        print(f"✅ Dockerfiles criados:")
        print(f"   - {backend_dockerfile_path}")
        print(f"   - {frontend_dockerfile_path}")
        return True
    
    def create_deployment_scripts(self) -> bool:
        """Criar scripts de deployment"""
        print("🚀 Criando scripts de deployment...")
        
        deploy_dir = self.project_root / "deploy"
        deploy_dir.mkdir(exist_ok=True)
        
        # Script de deploy
        deploy_script = """#!/bin/bash

set -e

echo "🚀 Iniciando deployment do Hypatium..."

# Verificar se está no diretório correto
if [ ! -f "docker-compose.prod.yml" ]; then
    echo "❌ Erro: docker-compose.prod.yml não encontrado"
    exit 1
fi

# Fazer backup do banco de dados
echo "💾 Fazendo backup do banco de dados..."
docker-compose -f docker-compose.prod.yml exec -T db pg_dump -U $POSTGRES_USER $POSTGRES_DB > backup_$(date +%Y%m%d_%H%M%S).sql

# Parar serviços
echo "⏹️ Parando serviços..."
docker-compose -f docker-compose.prod.yml down

# Fazer pull das imagens
echo "📥 Atualizando imagens..."
docker-compose -f docker-compose.prod.yml pull

# Rebuild e restart
echo "🔨 Rebuilding e reiniciando serviços..."
docker-compose -f docker-compose.prod.yml up --build -d

# Aguardar serviços ficarem prontos
echo "⏳ Aguardando serviços ficarem prontos..."
sleep 30

# Verificar saúde dos serviços
echo "🔍 Verificando saúde dos serviços..."
docker-compose -f docker-compose.prod.yml ps

# Executar migrações
echo "🗄️ Executando migrações do banco..."
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head

echo "✅ Deployment concluído com sucesso!"
echo "🌐 Aplicação disponível em: https://yourdomain.com"
"""
        
        deploy_script_path = deploy_dir / "deploy.sh"
        with open(deploy_script_path, 'w') as f:
            f.write(deploy_script)
        
        # Tornar executável
        os.chmod(deploy_script_path, 0o755)
        
        # Script de monitoramento
        monitor_script = """#!/bin/bash

echo "📊 Status dos serviços Hypatium:"
echo "================================"

docker-compose -f docker-compose.prod.yml ps

echo ""
echo "💾 Uso de disco:"
df -h

echo ""
echo "🧠 Uso de memória:"
free -h

echo ""
echo "📈 Logs recentes (últimas 50 linhas):"
docker-compose -f docker-compose.prod.yml logs --tail=50
"""
        
        monitor_script_path = deploy_dir / "monitor.sh"
        with open(monitor_script_path, 'w') as f:
            f.write(monitor_script)
        
        os.chmod(monitor_script_path, 0o755)
        
        print(f"✅ Scripts de deployment criados:")
        print(f"   - {deploy_script_path}")
        print(f"   - {monitor_script_path}")
        return True
    
    def run_setup(self) -> bool:
        """Executar configuração completa"""
        print("🎯 Configurando Hypatium para produção...\n")
        
        steps = [
            ("Verificar requisitos", self.check_requirements),
            ("Configurar ambiente", self.setup_environment),
            ("Configurar Docker", self.setup_docker_production),
            ("Configurar Nginx", self.setup_nginx),
            ("Criar Dockerfiles", self.create_dockerfiles),
            ("Criar scripts de deployment", self.create_deployment_scripts),
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            if not step_func():
                print(f"❌ Falha em: {step_name}")
                return False
        
        print("\n🎉 Configuração de produção concluída com sucesso!")
        print("\n📝 Próximos passos:")
        print("1. Edite o arquivo .env.production com suas configurações")
        print("2. Configure certificados SSL no diretório nginx/ssl/")
        print("3. Execute: ./deploy/deploy.sh")
        print("4. Configure monitoramento com: ./deploy/monitor.sh")
        
        return True

if __name__ == "__main__":
    setup = ProductionSetup()
    success = setup.run_setup()
    sys.exit(0 if success else 1)
