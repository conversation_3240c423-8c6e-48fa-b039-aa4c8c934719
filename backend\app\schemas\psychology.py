"""
Schemas Pydantic para psicologia
"""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime

class PsychologySessionBase(BaseModel):
    """Schema base para sessão psicológica"""
    data_sessao: datetime
    duracao_minutos: Optional[int] = Field(None, ge=1, le=300)
    anotacoes: Optional[str] = None
    objetivos_sessao: Optional[str] = None
    tecnicas_utilizadas: Optional[str] = None
    observacoes_comportamentais: Optional[str] = None
    audio_url: Optional[str] = None
    transcricao_texto: Optional[str] = None
    transcricao_processada: bool = False
    humor_escala: Optional[int] = Field(None, ge=1, le=10)
    ansiedade_escala: Optional[int] = Field(None, ge=1, le=10)
    motivacao_escala: Optional[int] = Field(None, ge=1, le=10)
    proximos_passos: Optional[str] = None
    data_proxima_sessao: Optional[datetime] = None

class PsychologySessionCreate(PsychologySessionBase):
    """Schema para criação de sessão psicológica"""
    paciente_id: int
    psicologo_id: int

class PsychologySessionUpdate(BaseModel):
    """Schema para atualização de sessão psicológica"""
    data_sessao: Optional[datetime] = None
    duracao_minutos: Optional[int] = Field(None, ge=1, le=300)
    anotacoes: Optional[str] = None
    objetivos_sessao: Optional[str] = None
    tecnicas_utilizadas: Optional[str] = None
    observacoes_comportamentais: Optional[str] = None
    audio_url: Optional[str] = None
    transcricao_texto: Optional[str] = None
    transcricao_processada: Optional[bool] = None
    humor_escala: Optional[int] = Field(None, ge=1, le=10)
    ansiedade_escala: Optional[int] = Field(None, ge=1, le=10)
    motivacao_escala: Optional[int] = Field(None, ge=1, le=10)
    proximos_passos: Optional[str] = None
    data_proxima_sessao: Optional[datetime] = None

class PsychologySession(PsychologySessionBase):
    """Schema para resposta de sessão psicológica"""
    id: int
    paciente_id: int
    psicologo_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class AudioTranscription(BaseModel):
    """Schema para transcrição de áudio"""
    session_id: int
    audio_file_path: str
    transcription_text: Optional[str] = None
    confidence_score: Optional[float] = Field(None, ge=0, le=1)
    processing_status: str = Field(default="pending")  # pending, processing, completed, failed
    error_message: Optional[str] = None

class PsychologicalAssessment(BaseModel):
    """Schema para avaliação psicológica"""
    paciente_id: int
    data_avaliacao: datetime
    tipo_avaliacao: str  # inicial, seguimento, alta
    
    # Escalas e questionários
    beck_depression: Optional[int] = Field(None, ge=0, le=63)
    beck_anxiety: Optional[int] = Field(None, ge=0, le=63)
    whoqol_bref: Optional[dict] = None
    
    # Observações clínicas
    estado_mental: Optional[str] = None
    humor: Optional[str] = None
    afeto: Optional[str] = None
    pensamento: Optional[str] = None
    percepcao: Optional[str] = None
    cognicao: Optional[str] = None
    
    # Diagnóstico e plano
    hipotese_diagnostica: Optional[str] = None
    plano_terapeutico: Optional[str] = None
    objetivos_terapeuticos: Optional[str] = None
    
    observacoes: Optional[str] = None

class TherapeuticGoal(BaseModel):
    """Schema para objetivo terapêutico"""
    paciente_id: int
    objetivo: str
    descricao: Optional[str] = None
    prazo_estimado: Optional[datetime] = None
    status: str = Field(default="ativo")  # ativo, pausado, concluido, cancelado
    progresso_percentual: Optional[int] = Field(None, ge=0, le=100)
    observacoes: Optional[str] = None

class PsychologyReport(BaseModel):
    """Schema para relatório psicológico"""
    paciente_id: int
    periodo_inicio: datetime
    periodo_fim: datetime
    numero_sessoes: int
    evolucao_humor: Optional[str] = None
    evolucao_ansiedade: Optional[str] = None
    evolucao_motivacao: Optional[str] = None
    principais_tecnicas: Optional[str] = None
    objetivos_alcancados: Optional[str] = None
    recomendacoes: Optional[str] = None
    observacoes: Optional[str] = None
