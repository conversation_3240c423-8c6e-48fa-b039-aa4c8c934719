"""
Configurações da aplicação Hypatium
"""

from typing import List, Union
from pydantic import AnyHttpUrl, validator
from pydantic_settings import BaseSettings
from decouple import config


class Settings(BaseSettings):
    # Configurações da API
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Hypatium"

    # Configurações do servidor
    SERVER_NAME: str = config("SERVER_NAME", default="localhost")
    SERVER_HOST: str = config("SERVER_HOST", default="http://localhost")

    # CORS
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:3000",  # Frontend Next.js
        "http://localhost:8000",  # Backend FastAPI
        "https://hypatium.vercel.app",  # Frontend em produção
    ]

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Configurações do banco de dados
    DATABASE_URL: str = config("DATABASE_URL", default="sqlite:///./hypatium.db")

    # Configurações PostgreSQL (para produção)
    POSTGRES_SERVER: str = config("POSTGRES_SERVER", default="localhost")
    POSTGRES_USER: str = config("POSTGRES_USER", default="hypatium")
    POSTGRES_PASSWORD: str = config("POSTGRES_PASSWORD", default="hypatium123")
    POSTGRES_DB: str = config("POSTGRES_DB", default="hypatium")
    POSTGRES_PORT: str = config("POSTGRES_PORT", default="5432")

    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        # Usar DATABASE_URL se definido, senão usar PostgreSQL
        if self.DATABASE_URL and self.DATABASE_URL != "sqlite:///./hypatium.db":
            return self.DATABASE_URL
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

    # Configurações de segurança
    SECRET_KEY: str = config("SECRET_KEY", default="hypatium-secret-key-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 dias

    # Configurações de APIs externas
    OPENAI_API_KEY: str = config("OPENAI_API_KEY", default="")
    GOOGLE_CLOUD_CREDENTIALS: str = config("GOOGLE_CLOUD_CREDENTIALS", default="")
    FIREBASE_CREDENTIALS: str = config("FIREBASE_CREDENTIALS", default="")

    # Configurações de upload
    UPLOAD_FOLDER: str = "static/uploads"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_VIDEO_EXTENSIONS: List[str] = [".mp4", ".avi", ".mov", ".wmv"]
    ALLOWED_AUDIO_EXTENSIONS: List[str] = [".mp3", ".wav", ".m4a"]
    ALLOWED_IMAGE_EXTENSIONS: List[str] = [".jpg", ".jpeg", ".png", ".gif"]

    # Configurações de email (para futuras implementações)
    SMTP_TLS: bool = True
    SMTP_PORT: int = 587
    SMTP_HOST: str = config("SMTP_HOST", default="")
    SMTP_USER: str = config("SMTP_USER", default="")
    SMTP_PASSWORD: str = config("SMTP_PASSWORD", default="")
    EMAILS_FROM_EMAIL: str = config("EMAILS_FROM_EMAIL", default="")
    EMAILS_FROM_NAME: str = config("EMAILS_FROM_NAME", default="Hypatium")

    class Config:
        case_sensitive = True


settings = Settings()
