import { PrismaService } from '../prisma/prisma.service';
import { CreateVolumeLoadDto } from './dto/create-volume-load.dto';
import { UpdateVolumeLoadDto } from './dto/update-volume-load.dto';
export declare class VolumeLoadService {
    private prisma;
    constructor(prisma: PrismaService);
    private calcularVLSerie;
    private calcularVLExercicio;
    create(createVolumeLoadDto: CreateVolumeLoadDto, personalId: string): Promise<any>;
    update(id: string, updateVolumeLoadDto: UpdateVolumeLoadDto, personalId: string): Promise<any>;
    private calcularVLMesociclo;
    findByAvaliacao(avaliacaoComposicaoId: string, personalId?: string): Promise<any>;
    getVolumeLoadStats(avaliacaoComposicaoId: string, personalId?: string): Promise<{
        porExercicio: unknown[];
        porSemana: unknown[];
        vlTotalMesociclo: any;
    }>;
    remove(id: string, personalId: string): Promise<{
        message: string;
    }>;
}
