{"version": 3, "file": "relatorio.service.js", "sourceRoot": "", "sources": ["../../src/relatorio/relatorio.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmF;AACnF,6DAAyD;AACzD,2CAA+C;AAIxC,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAIjB;IACA;IAJF,MAAM,CAAM;IAEpB,YACU,MAAqB,EACrB,aAA4B;QAD5B,WAAM,GAAN,MAAM,CAAe;QACrB,kBAAa,GAAb,aAAa,CAAe;QAEpC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,gBAAgB,CAAC,CAAC;QAChE,IAAI,MAAM,EAAE,CAAC;YAIX,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,qBAA6B,EAAE,UAAkB;QAEpE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC;YACjE,KAAK,EAAE,EAAE,EAAE,EAAE,qBAAqB,EAAE;YACpC,OAAO,EAAE;gBACP,KAAK,EAAE;oBACL,MAAM,EAAE;wBACN,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,SAAS,EAAE,IAAI;wBACf,QAAQ,EAAE,IAAI;wBACd,SAAS,EAAE,IAAI;wBACf,OAAO,EAAE,IAAI;qBACd;iBACF;gBACD,mBAAmB,EAAE,IAAI;gBACzB,WAAW,EAAE;oBACX,OAAO,EAAE;wBACP,EAAE,SAAS,EAAE,KAAK,EAAE;wBACpB,EAAE,MAAM,EAAE,KAAK,EAAE;qBAClB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,SAAS,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;YACxC,MAAM,IAAI,2BAAkB,CAAC,gCAAgC,CAAC,CAAC;QACjE,CAAC;QAGD,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC;YAC1E,KAAK,EAAE;gBACL,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,aAAa,EAAE;oBACb,EAAE,EAAE,SAAS,CAAC,aAAa;iBAC5B;aACF;YACD,OAAO,EAAE;gBACP,aAAa,EAAE,MAAM;aACtB;YACD,IAAI,EAAE,CAAC;YACP,OAAO,EAAE;gBACP,mBAAmB,EAAE,IAAI;aAC1B;SACF,CAAC,CAAC;QAGH,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC;gBACH,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;YAC5E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;gBACtD,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;aAAM,CAAC;YACN,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;QAC1E,CAAC;QAGD,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;QAGlF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACnD,IAAI,EAAE;gBACJ,qBAAqB;gBACrB,YAAY;gBACZ,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;aACnD;SACF,CAAC,CAAC;QAEH,OAAO;YACL,GAAG,SAAS;YACZ,gBAAgB;YAChB,SAAS;SACV,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,SAAc,EAAE,oBAA2B;QACtE,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;QAE5E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAC3D,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE;;;;;;;;;;6FAU0E;iBACpF;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM;iBAChB;aACF;YACD,UAAU,EAAE,IAAI;YAChB,WAAW,EAAE,GAAG;SACjB,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,uBAAuB,CAAC;IAC5E,CAAC;IAEO,sBAAsB,CAAC,SAAc,EAAE,oBAA2B;QACxE,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAC9B,MAAM,UAAU,GAAG,SAAS,CAAC,mBAAmB,CAAC;QAEjD,IAAI,MAAM,GAAG;QACT,KAAK,CAAC,IAAI;SACT,KAAK,CAAC,KAAK;QACZ,KAAK,CAAC,SAAS;YACX,KAAK,CAAC,QAAQ;aACb,KAAK,CAAC,SAAS,IAAI,eAAe;WACpC,KAAK,CAAC,OAAO,IAAI,eAAe;;mBAExB,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;;;SAGvE,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,YAAY;oBAClC,SAAS,CAAC,UAAU;;;mBAGrB,SAAS,CAAC,gBAAgB,iBAAiB,SAAS,CAAC,eAAe;sBACjE,SAAS,CAAC,kBAAkB,iBAAiB,SAAS,CAAC,iBAAiB;sBACxE,SAAS,CAAC,kBAAkB,iBAAiB,SAAS,CAAC,iBAAiB;;;0BAGpE,SAAS,CAAC,kBAAkB;0BAC5B,SAAS,CAAC,gBAAgB,KAAK,CAAC;QAEtD,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,IAAI;;;iBAGC,UAAU,CAAC,UAAU;yBACb,UAAU,CAAC,iBAAiB;iBACpC,UAAU,CAAC,UAAU;oBAClB,UAAU,CAAC,aAAa;SACnC,UAAU,CAAC,QAAQ,iBAAiB,UAAU,CAAC,cAAc;yBAC7C,UAAU,CAAC,YAAY,UAAU,UAAU,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,0BAA0B,IAAI,CAAC;QACvJ,CAAC;QAED,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,sDAAsD,CAAC;YAEjE,oBAAoB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBAC1C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;gBAEhJ,MAAM,IAAI,iBAAiB,KAAK,GAAG,CAAC,KAAK,aAAa;SACrD,GAAG,CAAC,QAAQ,MAAM,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;sBAC7H,GAAG,CAAC,gBAAgB,OAAO,SAAS,CAAC,gBAAgB,MAAM,SAAS,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;iBACjM,GAAG,CAAC,gBAAgB,QAAQ,SAAS,CAAC,gBAAgB,OAAO,SAAS,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;YAC/M,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,kBAAkB,CAAC,SAAc,EAAE,oBAA2B;QACpE,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAC9B,IAAI,OAAO,GAAG,mCAAmC,KAAK,CAAC,IAAI,MAAM,CAAC;QAElE,OAAO,IAAI,qBAAqB,CAAC;QACjC,OAAO,IAAI,YAAY,KAAK,CAAC,KAAK,UAAU,KAAK,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ,OAAO,CAAC;QACxF,OAAO,IAAI,cAAc,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,YAAY,KAAK,CAAC;QAC5E,OAAO,IAAI,mCAAmC,SAAS,CAAC,gBAAgB,OAAO,CAAC;QAEhF,OAAO,IAAI,mCAAmC,CAAC;QAC/C,OAAO,IAAI,+BAA+B,SAAS,CAAC,UAAU,MAAM,CAAC;QACrE,OAAO,IAAI,kBAAkB,SAAS,CAAC,kBAAkB,MAAM,CAAC;QAChE,OAAO,IAAI,kBAAkB,SAAS,CAAC,gBAAgB,QAAQ,CAAC;QAEhE,IAAI,SAAS,CAAC,mBAAmB,EAAE,CAAC;YAClC,MAAM,UAAU,GAAG,SAAS,CAAC,mBAAmB,CAAC;YACjD,OAAO,IAAI,uBAAuB,CAAC;YACnC,OAAO,IAAI,sCAAsC,UAAU,CAAC,UAAU,aAAa,CAAC;YACpF,OAAO,IAAI,6BAA6B,UAAU,CAAC,QAAQ,aAAa,CAAC;YACzE,OAAO,IAAI,mCAAmC,UAAU,CAAC,YAAY,eAAe,CAAC;QACvF,CAAC;QAED,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAErJ,OAAO,IAAI,aAAa,aAAa,WAAW,CAAC;YACjD,OAAO,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;YACpF,OAAO,IAAI,gBAAgB,CAAC,SAAS,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;YACpG,OAAO,IAAI,kBAAkB,CAAC,SAAS,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC3G,CAAC;QAED,OAAO,IAAI,kBAAkB,CAAC;QAC9B,OAAO,IAAI,+CAA+C,CAAC;QAC3D,OAAO,IAAI,sCAAsC,CAAC;QAClD,OAAO,IAAI,iCAAiC,CAAC;QAE7C,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,kBAAkB,CAAC,SAAc,EAAE,oBAA2B;QACpE,MAAM,eAAe,GAAG,CAAC,GAAG,oBAAoB,CAAC,OAAO,EAAE,EAAE,SAAS,CAAC,CAAC;QAEvE,OAAO;YACL,WAAW,EAAE,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACtC,IAAI,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC5D,KAAK,EAAE,EAAE,CAAC,QAAQ;aACnB,CAAC,CAAC;YACH,eAAe,EAAE,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC1C,IAAI,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC5D,MAAM,EAAE,EAAE,CAAC,gBAAgB;gBAC3B,QAAQ,EAAE,EAAE,CAAC,kBAAkB;gBAC/B,QAAQ,EAAE,EAAE,CAAC,kBAAkB;aAChC,CAAC,CAAC;YACH,aAAa,EAAE,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACxC,IAAI,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC5D,UAAU,EAAE,EAAE,CAAC,kBAAkB;gBACjC,UAAU,EAAE,EAAE,CAAC,gBAAgB;aAChC,CAAC,CAAC;YACH,kBAAkB,EAAE,eAAe;iBAChC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,mBAAmB,CAAC;iBACpC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACV,IAAI,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC5D,GAAG,EAAE,EAAE,CAAC,mBAAmB,CAAC,UAAU;gBACtC,GAAG,EAAE,EAAE,CAAC,mBAAmB,CAAC,QAAQ;gBACpC,OAAO,EAAE,EAAE,CAAC,mBAAmB,CAAC,YAAY;aAC7C,CAAC,CAAC;SACN,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,OAAe,EAAE,UAAmB;QAE9D,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;gBAC/C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;aACvB,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;gBAC9C,MAAM,IAAI,2BAAkB,CAAC,4BAA4B,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpC,KAAK,EAAE;gBACL,mBAAmB,EAAE;oBACnB,OAAO;iBACR;aACF;YACD,OAAO,EAAE;gBACP,mBAAmB,EAAE;oBACnB,MAAM,EAAE;wBACN,aAAa,EAAE,IAAI;wBACnB,QAAQ,EAAE,IAAI;wBACd,YAAY,EAAE,IAAI;wBAClB,gBAAgB,EAAE,IAAI;wBACtB,KAAK,EAAE;4BACL,MAAM,EAAE;gCACN,IAAI,EAAE,IAAI;6BACX;yBACF;qBACF;iBACF;aACF;YACD,OAAO,EAAE;gBACP,WAAW,EAAE,MAAM;aACpB;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,EAAU,EAAE,UAAmB;QACrD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YACvD,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,OAAO,EAAE;gBACP,mBAAmB,EAAE;oBACnB,OAAO,EAAE;wBACP,KAAK,EAAE;4BACL,MAAM,EAAE;gCACN,IAAI,EAAE,IAAI;gCACV,KAAK,EAAE,IAAI;gCACX,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;6BACf;yBACF;wBACD,mBAAmB,EAAE,IAAI;qBAC1B;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,UAAU,IAAI,SAAS,CAAC,mBAAmB,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;YAC1E,MAAM,IAAI,2BAAkB,CAAC,gCAAgC,CAAC,CAAC;QACjE,CAAC;QAED,OAAO;YACL,GAAG,SAAS;YACZ,gBAAgB,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI;SAC7F,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,qBAA6B,EAAE,UAAkB;QAE9E,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC;YACjE,KAAK,EAAE,EAAE,EAAE,EAAE,qBAAqB,EAAE;YACpC,OAAO,EAAE;gBACP,KAAK,EAAE;oBACL,MAAM,EAAE;wBACN,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;qBACZ;iBACF;gBACD,WAAW,EAAE;oBACX,OAAO,EAAE;wBACP,EAAE,SAAS,EAAE,KAAK,EAAE;wBACpB,EAAE,MAAM,EAAE,KAAK,EAAE;qBAClB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,SAAS,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;YACxC,MAAM,IAAI,2BAAkB,CAAC,gCAAgC,CAAC,CAAC;QACjE,CAAC;QAGD,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAG7E,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC;gBACH,SAAS,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;YAC9E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACzD,SAAS,GAAG,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;aAAM,CAAC;YACN,SAAS,GAAG,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QAC5E,CAAC;QAED,OAAO;YACL,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,OAAO,EAAE,SAAS;YAClB,eAAe;YACf,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC;SACxD,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAAC,WAAkB;QACjD,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE;YAClD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;gBACvB,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG;oBAClB,SAAS,EAAE,EAAE,CAAC,SAAS;oBACvB,OAAO,EAAE,EAAE;oBACX,OAAO,EAAE,CAAC;oBACV,UAAU,EAAE,EAAE;iBACf,CAAC;YACJ,CAAC;YAED,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC7B,MAAM,EAAE,EAAE,CAAC,MAAM;gBACjB,WAAW,EAAE,EAAE,CAAC,WAAW,IAAI,CAAC;aACjC,CAAC,CAAC;YAEH,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,OAAO,IAAI,EAAE,CAAC,WAAW,IAAI,CAAC,CAAC;YAEjD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAS,CAAC,CAAC;QAGd,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,EAAO,EAAE,EAAE;YAC9C,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YAEzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3C,MAAM,QAAQ,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC;gBAC/C,MAAM,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;gBACxC,MAAM,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE5E,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;oBACjB,MAAM,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM;oBAC5B,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBAC9C,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,SAAc,EAAE,OAAc;QACnE,MAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAElE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAC3D,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE;;;;;;;;;4DASyC;iBACnD;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM;iBAChB;aACF;YACD,UAAU,EAAE,IAAI;YAChB,WAAW,EAAE,GAAG;SACjB,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,uBAAuB,CAAC;IAC5E,CAAC;IAEO,yBAAyB,CAAC,SAAc,EAAE,OAAc;QAC9D,IAAI,MAAM,GAAG,4BAA4B,SAAS,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC;QAEpE,MAAM,IAAI,iBAAiB,CAAC;QAC5B,MAAM,IAAI,0BAA0B,OAAO,CAAC,MAAM,IAAI,CAAC;QACvD,MAAM,IAAI,qCAAqC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC;QAE1G,MAAM,IAAI,+BAA+B,CAAC;QAC1C,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACnB,MAAM,IAAI,eAAe,EAAE,CAAC,SAAS,KAAK,CAAC;YAC3C,MAAM,IAAI,wBAAwB,EAAE,CAAC,OAAO,IAAI,CAAC;YACjD,MAAM,IAAI,0BAA0B,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC;YAC1D,MAAM,IAAI,4BAA4B,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAE3G,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,eAAe,GAAG,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC;gBACvG,MAAM,IAAI,uBAAuB,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;YACnE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,4BAA4B,CAAC,SAAc,EAAE,OAAc;QACjE,IAAI,OAAO,GAAG,8BAA8B,SAAS,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC;QAEvE,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACrE,OAAO,IAAI,iBAAiB,CAAC;QAC7B,OAAO,IAAI,qCAAqC,OAAO,CAAC,MAAM,IAAI,CAAC;QACnE,OAAO,IAAI,qCAAqC,OAAO,IAAI,CAAC;QAC5D,OAAO,IAAI,sCAAsC,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;QAE7F,OAAO,IAAI,0BAA0B,CAAC;QACtC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACnB,OAAO,IAAI,eAAe,EAAE,CAAC,SAAS,KAAK,CAAC;YAC5C,OAAO,IAAI,wBAAwB,EAAE,CAAC,OAAO,IAAI,CAAC;YAClD,OAAO,IAAI,uBAAuB,CAAC,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;YAElF,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,eAAe,GAAG,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC;gBACvG,OAAO,IAAI,uBAAuB,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,oBAAoB,CAAC;QAChC,OAAO,IAAI,gDAAgD,CAAC;QAC5D,OAAO,IAAI,sDAAsD,CAAC;QAClE,OAAO,IAAI,uCAAuC,CAAC;QAEnD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,uBAAuB,CAAC,OAAc;QAC5C,OAAO;YACL,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACjC,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE;gBAC/B,OAAO,EAAE,EAAE,CAAC,OAAO;aACpB,CAAC,CAAC;YACH,mBAAmB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE;gBAC9C,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;oBACjC,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;oBACxE,IAAI,QAAQ,EAAE,CAAC;wBACb,QAAQ,CAAC,OAAO,IAAI,MAAM,CAAC,WAAW,CAAC;oBACzC,CAAC;yBAAM,CAAC;wBACN,GAAG,CAAC,IAAI,CAAC;4BACP,MAAM,EAAE,IAAI,MAAM,CAAC,MAAM,EAAE;4BAC3B,OAAO,EAAE,MAAM,CAAC,WAAW;yBAC5B,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;SAC3E,CAAC;IACJ,CAAC;CACF,CAAA;AA7gBY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAKO,8BAAa;QACN,sBAAa;GAL3B,gBAAgB,CA6gB5B"}