"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlunoController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const aluno_service_1 = require("./aluno.service");
const create_aluno_dto_1 = require("./dto/create-aluno.dto");
const update_aluno_dto_1 = require("./dto/update-aluno.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
let AlunoController = class AlunoController {
    alunoService;
    constructor(alunoService) {
        this.alunoService = alunoService;
    }
    create(createAlunoDto, req) {
        return this.alunoService.create(createAlunoDto, req.user.id);
    }
    findAllByPersonal(req) {
        return this.alunoService.findAllByPersonal(req.user.id);
    }
    findOne(id, req) {
        const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
        return this.alunoService.findOne(id, personalId);
    }
    update(id, updateAlunoDto, req) {
        const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
        return this.alunoService.update(id, updateAlunoDto, personalId);
    }
    remove(id, req) {
        return this.alunoService.remove(id, req.user.id);
    }
    getStats(id, req) {
        const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
        return this.alunoService.getAlunoStats(id, personalId);
    }
};
exports.AlunoController = AlunoController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo aluno (apenas Personal Trainer)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Aluno criado com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_aluno_dto_1.CreateAlunoDto, Object]),
    __metadata("design:returntype", void 0)
], AlunoController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Listar alunos do Personal Trainer' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Lista de alunos encontrada' }),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AlunoController.prototype, "findAllByPersonal", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar aluno por ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Aluno encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Aluno não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AlunoController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar dados do aluno' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Aluno atualizado com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Aluno não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_aluno_dto_1.UpdateAlunoDto, Object]),
    __metadata("design:returntype", void 0)
], AlunoController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Remover aluno (apenas Personal Trainer)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Aluno removido com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Aluno não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Acesso negado' }),
    (0, roles_decorator_1.Roles)('personal'),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AlunoController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Buscar estatísticas do aluno' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Estatísticas encontradas' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Aluno não encontrado' }),
    (0, common_1.Get)(':id/stats'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AlunoController.prototype, "getStats", null);
exports.AlunoController = AlunoController = __decorate([
    (0, swagger_1.ApiTags)('alunos'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('alunos'),
    __metadata("design:paramtypes", [aluno_service_1.AlunoService])
], AlunoController);
//# sourceMappingURL=aluno.controller.js.map