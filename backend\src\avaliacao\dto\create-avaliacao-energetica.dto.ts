import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CreateAvaliacaoEnergeticaDto {
  @ApiProperty({
    description: 'TMB FAO/OMS em kcal',
    example: 1800,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(800)
  @Max(5000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  tmbFAO_OMS?: number;

  @ApiProperty({
    description: 'TMB Harris-Benedict em kcal',
    example: 1850,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(800)
  @Max(5000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  tmbHarrisBenedict?: number;

  @ApiProperty({
    description: 'TMB Mifflin em kcal',
    example: 1820,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(800)
  @Max(5000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  tmbMifflin?: number;

  @ApiProperty({
    description: 'TMB Cunningham em kcal',
    example: 1900,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(800)
  @Max(5000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  tmbCunningham?: number;

  @ApiProperty({
    description: 'TMB Tinsley MLG em kcal',
    example: 1880,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(800)
  @Max(5000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  tmbTinsleyMLG?: number;

  @ApiProperty({
    description: 'TMB Tinsley Peso em kcal',
    example: 1820,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(800)
  @Max(5000)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  tmbTinsleyPeso?: number;

  @ApiProperty({
    description: 'Fator de atividade (1.2 = sedentário, 1.4 = leve, 1.6 = moderado, 1.8 = intenso, 2.0 = muito intenso)',
    example: 1.4,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1.0)
  @Max(2.5)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  fatorAtividade?: number;

  @ApiProperty({
    description: 'Percentual de déficit (-) ou superávit (+) calórico (-30% a +30%)',
    example: -10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(-50)
  @Max(50)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  percentualDeficitSuperavit?: number;
}
