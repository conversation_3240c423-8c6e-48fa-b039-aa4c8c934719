import { useState } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  Calculator, 
  User, 
  Activity,
  Target,
  TrendingUp,
  Utensils,
  Save,
  RefreshCw
} from 'lucide-react'

const calculatorSchema = z.object({
  peso: z.number().min(20, 'Peso deve ser maior que 20kg').max(300, 'Peso deve ser menor que 300kg'),
  altura: z.number().min(1.0, 'Altura deve ser maior que 1.0m').max(2.5, 'Altura deve ser menor que 2.5m'),
  idade: z.number().min(10, 'Idade deve ser maior que 10 anos').max(120, 'Idade deve ser menor que 120 anos'),
  sexo: z.enum(['masculino', 'feminino']),
  nivel_atividade: z.enum(['sedentario', 'leve', 'moderado', 'intenso', 'muito_intenso']),
  objetivo: z.enum(['deficit', 'manutencao', 'superavit']),
  percentual_ajuste: z.number().optional(),
  
  // Distribuição de macronutrientes
  proteinas_percentual: z.number().min(10).max(40).default(25),
  carboidratos_percentual: z.number().min(20).max(65).default(45),
  gorduras_percentual: z.number().min(15).max(40).default(30),
})

type CalculatorForm = z.infer<typeof calculatorSchema>

interface CalculationResult {
  tmb: number
  get: number
  calorias_alvo: number
  nivel_atividade: string
  objetivo: string
  macronutrientes?: {
    proteinas_g: number
    carboidratos_g: number
    gorduras_g: number
    calorias_total: number
  }
}

export default function NutritionCalculatorPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<CalculationResult | null>(null)
  const [macroResult, setMacroResult] = useState<any>(null)
  const [error, setError] = useState('')

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<CalculatorForm>({
    resolver: zodResolver(calculatorSchema),
    defaultValues: {
      sexo: 'feminino',
      nivel_atividade: 'moderado',
      objetivo: 'manutencao',
      proteinas_percentual: 25,
      carboidratos_percentual: 45,
      gorduras_percentual: 30,
    }
  })

  const watchedValues = watch()

  // Verificar se percentuais somam 100%
  const totalPercentual = (watchedValues.proteinas_percentual || 0) + 
                         (watchedValues.carboidratos_percentual || 0) + 
                         (watchedValues.gorduras_percentual || 0)

  const onSubmit = async (data: CalculatorForm) => {
    setIsLoading(true)
    setError('')

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Calcular necessidades nutricionais
      const nutritionData = {
        peso: data.peso,
        altura: data.altura,
        idade: data.idade,
        sexo: data.sexo,
        nivel_atividade: data.nivel_atividade,
        objetivo: data.objetivo,
        percentual_ajuste: data.percentual_ajuste
      }

      const nutritionResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/nutrition/calculate/needs`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(nutritionData),
        }
      )

      if (!nutritionResponse.ok) {
        throw new Error('Erro ao calcular necessidades nutricionais')
      }

      const nutritionResult = await nutritionResponse.json()
      setResult(nutritionResult)

      // Calcular macronutrientes
      const macroData = {
        calorias_totais: nutritionResult.calorias_alvo,
        proteinas_percentual: data.proteinas_percentual,
        carboidratos_percentual: data.carboidratos_percentual,
        gorduras_percentual: data.gorduras_percentual
      }

      const macroResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/nutrition/calculate/macros`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(macroData),
        }
      )

      if (macroResponse.ok) {
        const macroResult = await macroResponse.json()
        setMacroResult(macroResult)
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao calcular')
    } finally {
      setIsLoading(false)
    }
  }

  const getNivelAtividadeLabel = (nivel: string) => {
    const labels = {
      sedentario: 'Sedentário (pouco ou nenhum exercício)',
      leve: 'Leve (exercício leve 1-3 dias/semana)',
      moderado: 'Moderado (exercício moderado 3-5 dias/semana)',
      intenso: 'Intenso (exercício intenso 6-7 dias/semana)',
      muito_intenso: 'Muito Intenso (exercício muito intenso, trabalho físico)'
    }
    return labels[nivel as keyof typeof labels] || nivel
  }

  const getObjetivoLabel = (objetivo: string) => {
    const labels = {
      deficit: 'Emagrecimento (déficit calórico)',
      manutencao: 'Manutenção de peso',
      superavit: 'Ganho de peso (superávit calórico)'
    }
    return labels[objetivo as keyof typeof labels] || objetivo
  }

  return (
    <>
      <Head>
        <title>Calculadora Nutricional - Hypatium</title>
        <meta name="description" content="Calcule necessidades nutricionais e distribuição de macronutrientes" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href="/dashboard" className="text-blue-600 hover:text-blue-800">
                  ← Voltar ao Dashboard
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Calculator className="h-6 w-6 mr-2" />
                Calculadora Nutricional
              </h1>
              <div></div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Form */}
              <div className="space-y-6">
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Dados Pessoais */}
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="mb-6">
                      <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center">
                        <User className="h-5 w-5 mr-2" />
                        Dados Pessoais
                      </h3>
                    </div>

                    {error && (
                      <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                        {error}
                      </div>
                    )}

                    <div className="grid grid-cols-6 gap-6">
                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Peso (kg) *
                        </label>
                        <input
                          {...register('peso', { valueAsNumber: true })}
                          type="number"
                          step="0.1"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="70.5"
                        />
                        {errors.peso && (
                          <p className="mt-1 text-sm text-red-600">{errors.peso.message}</p>
                        )}
                      </div>

                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Altura (m) *
                        </label>
                        <input
                          {...register('altura', { valueAsNumber: true })}
                          type="number"
                          step="0.01"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="1.65"
                        />
                        {errors.altura && (
                          <p className="mt-1 text-sm text-red-600">{errors.altura.message}</p>
                        )}
                      </div>

                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Idade *
                        </label>
                        <input
                          {...register('idade', { valueAsNumber: true })}
                          type="number"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="30"
                        />
                        {errors.idade && (
                          <p className="mt-1 text-sm text-red-600">{errors.idade.message}</p>
                        )}
                      </div>

                      <div className="col-span-6 sm:col-span-3">
                        <label className="block text-sm font-medium text-gray-700">
                          Sexo *
                        </label>
                        <select
                          {...register('sexo')}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="feminino">Feminino</option>
                          <option value="masculino">Masculino</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Atividade e Objetivo */}
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="mb-6">
                      <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center">
                        <Activity className="h-5 w-5 mr-2" />
                        Atividade e Objetivo
                      </h3>
                    </div>

                    <div className="grid grid-cols-6 gap-6">
                      <div className="col-span-6">
                        <label className="block text-sm font-medium text-gray-700">
                          Nível de Atividade *
                        </label>
                        <select
                          {...register('nivel_atividade')}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="sedentario">Sedentário</option>
                          <option value="leve">Leve</option>
                          <option value="moderado">Moderado</option>
                          <option value="intenso">Intenso</option>
                          <option value="muito_intenso">Muito Intenso</option>
                        </select>
                        <p className="mt-1 text-sm text-gray-500">
                          {getNivelAtividadeLabel(watchedValues.nivel_atividade || 'moderado')}
                        </p>
                      </div>

                      <div className="col-span-6 sm:col-span-3">
                        <label className="block text-sm font-medium text-gray-700">
                          Objetivo *
                        </label>
                        <select
                          {...register('objetivo')}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="deficit">Emagrecimento</option>
                          <option value="manutencao">Manutenção</option>
                          <option value="superavit">Ganho de Peso</option>
                        </select>
                      </div>

                      <div className="col-span-6 sm:col-span-3">
                        <label className="block text-sm font-medium text-gray-700">
                          Ajuste Personalizado (%)
                        </label>
                        <input
                          {...register('percentual_ajuste', { valueAsNumber: true })}
                          type="number"
                          step="1"
                          min="-50"
                          max="50"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Ex: -20 para 20% de déficit"
                        />
                        <p className="mt-1 text-sm text-gray-500">
                          Deixe vazio para usar padrão do objetivo
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Macronutrientes */}
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="mb-6">
                      <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center">
                        <Utensils className="h-5 w-5 mr-2" />
                        Distribuição de Macronutrientes
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Total: {totalPercentual}% 
                        {totalPercentual !== 100 && (
                          <span className="text-red-600 ml-2">
                            (deve somar 100%)
                          </span>
                        )}
                      </p>
                    </div>

                    <div className="grid grid-cols-3 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Proteínas (%)
                        </label>
                        <input
                          {...register('proteinas_percentual', { valueAsNumber: true })}
                          type="number"
                          min="10"
                          max="40"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                        <p className="mt-1 text-xs text-gray-500">10-40%</p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Carboidratos (%)
                        </label>
                        <input
                          {...register('carboidratos_percentual', { valueAsNumber: true })}
                          type="number"
                          min="20"
                          max="65"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                        <p className="mt-1 text-xs text-gray-500">20-65%</p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Gorduras (%)
                        </label>
                        <input
                          {...register('gorduras_percentual', { valueAsNumber: true })}
                          type="number"
                          min="15"
                          max="40"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                        <p className="mt-1 text-xs text-gray-500">15-40%</p>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={isLoading || totalPercentual !== 100}
                      className="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      {isLoading ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Calculator className="h-4 w-4 mr-2" />
                      )}
                      Calcular
                    </button>
                  </div>
                </form>
              </div>

              {/* Results */}
              <div className="space-y-6">
                {result && (
                  <>
                    {/* Necessidades Calóricas */}
                    <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <TrendingUp className="h-5 w-5 mr-2" />
                        Necessidades Calóricas
                      </h3>
                      
                      <div className="grid grid-cols-1 gap-4">
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                          <p className="text-sm text-gray-600">Taxa Metabólica Basal (TMB)</p>
                          <p className="text-2xl font-bold text-blue-600">{result.tmb} kcal</p>
                        </div>
                        
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                          <p className="text-sm text-gray-600">Gasto Energético Total (GET)</p>
                          <p className="text-2xl font-bold text-green-600">{result.get} kcal</p>
                        </div>
                        
                        <div className="text-center p-4 bg-purple-50 rounded-lg">
                          <p className="text-sm text-gray-600">Calorias Alvo</p>
                          <p className="text-3xl font-bold text-purple-600">{result.calorias_alvo} kcal</p>
                          <p className="text-sm text-gray-500 mt-1">
                            {getObjetivoLabel(result.objetivo)}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Macronutrientes */}
                    {macroResult && (
                      <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                          <Utensils className="h-5 w-5 mr-2" />
                          Distribuição de Macronutrientes
                        </h3>
                        
                        <div className="space-y-4">
                          <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                            <span className="font-medium text-red-700">Proteínas</span>
                            <div className="text-right">
                              <p className="font-bold text-red-600">{macroResult.proteinas_g}g</p>
                              <p className="text-sm text-red-500">{watchedValues.proteinas_percentual}%</p>
                            </div>
                          </div>
                          
                          <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                            <span className="font-medium text-yellow-700">Carboidratos</span>
                            <div className="text-right">
                              <p className="font-bold text-yellow-600">{macroResult.carboidratos_g}g</p>
                              <p className="text-sm text-yellow-500">{watchedValues.carboidratos_percentual}%</p>
                            </div>
                          </div>
                          
                          <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                            <span className="font-medium text-orange-700">Gorduras</span>
                            <div className="text-right">
                              <p className="font-bold text-orange-600">{macroResult.gorduras_g}g</p>
                              <p className="text-sm text-orange-500">{watchedValues.gorduras_percentual}%</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}

                {!result && (
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="text-center text-gray-500">
                      <Calculator className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">
                        Preencha os dados
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Complete o formulário para ver os cálculos nutricionais
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
