"""
Endpoints de pacientes
"""

from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.models.models import User, Patient
from app.schemas.patient import Patient as PatientSchema, PatientCreate, PatientUpdate

router = APIRouter()

@router.post("/", response_model=PatientSchema)
def create_patient(
    *,
    db: Session = Depends(get_db),
    patient_in: PatientCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new patient
    """
    # Verificar se o usuário pode criar pacientes
    if current_user.tipo not in ["personal", "nutricionista", "psicologo", "admin"]:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    patient = Patient(**patient_in.dict())
    db.add(patient)
    db.commit()
    db.refresh(patient)
    return patient

@router.get("/", response_model=List[PatientSchema])
def read_patients(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve patients
    """
    # Filtrar pacientes baseado no tipo de usuário
    if current_user.tipo == "admin":
        patients = db.query(Patient).offset(skip).limit(limit).all()
    else:
        patients = db.query(Patient).filter(
            Patient.responsavel_id == current_user.id
        ).offset(skip).limit(limit).all()
    
    return patients

@router.get("/{patient_id}", response_model=PatientSchema)
def read_patient(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get patient by ID
    """
    patient = db.query(Patient).filter(Patient.id == patient_id).first()
    if not patient:
        raise HTTPException(status_code=404, detail="Patient not found")
    
    # Verificar permissões
    if current_user.tipo != "admin" and patient.responsavel_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    return patient
