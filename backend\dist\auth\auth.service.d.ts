import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
export declare class AuthService {
    private prisma;
    private jwtService;
    constructor(prisma: PrismaService, jwtService: JwtService);
    validateUser(email: string, password: string): Promise<any>;
    login(loginDto: LoginDto): Promise<{
        access_token: any;
        user: {
            id: any;
            email: any;
            nome: any;
            userType: any;
        };
    }>;
    registerPersonal(registerDto: RegisterDto): Promise<any>;
    registerAluno(registerDto: RegisterDto & {
        personalId: string;
        pesoAtual: number;
        estatura: number;
        dataNascimento: Date;
        idade: number;
    }): Promise<any>;
}
