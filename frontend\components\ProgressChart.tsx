import { useMemo } from 'react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts'

interface ProgressData {
  data: string
  peso: number
  imc?: number
  gordura_percentual?: number
  massa_gorda?: number
  massa_magra?: number
}

interface ProgressChartProps {
  data: ProgressData[]
  metrics: ('peso' | 'imc' | 'gordura_percentual' | 'massa_gorda' | 'massa_magra')[]
  title?: string
  height?: number
}

const metricConfig = {
  peso: {
    label: 'Peso (kg)',
    color: '#3b82f6',
    unit: 'kg'
  },
  imc: {
    label: 'IMC',
    color: '#10b981',
    unit: ''
  },
  gordura_percentual: {
    label: 'Gordura (%)',
    color: '#f59e0b',
    unit: '%'
  },
  massa_gorda: {
    label: 'Massa Gorda (kg)',
    color: '#ef4444',
    unit: 'kg'
  },
  massa_magra: {
    label: '<PERSON><PERSON> (kg)',
    color: '#8b5cf6',
    unit: 'kg'
  }
}

export default function ProgressChart({ 
  data, 
  metrics, 
  title = "Progresso", 
  height = 300 
}: ProgressChartProps) {
  const chartData = useMemo(() => {
    return data.map(item => ({
      ...item,
      data_formatada: new Date(item.data).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit'
      })
    })).reverse() // Ordem cronológica
  }, [data])

  const formatTooltipValue = (value: number, name: string) => {
    const metric = metrics.find(m => metricConfig[m].label === name)
    if (metric) {
      const config = metricConfig[metric]
      return [`${value?.toFixed(1)}${config.unit}`, config.label]
    }
    return [value, name]
  }

  const formatTooltipLabel = (label: string) => {
    return `Data: ${label}`
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
        <div className="flex items-center justify-center h-64 text-gray-500">
          <div className="text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">Sem dados</h3>
            <p className="mt-1 text-sm text-gray-500">
              Não há dados suficientes para exibir o gráfico.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
      
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="data_formatada" 
            stroke="#6b7280"
            fontSize={12}
          />
          <YAxis 
            stroke="#6b7280"
            fontSize={12}
          />
          <Tooltip 
            formatter={formatTooltipValue}
            labelFormatter={formatTooltipLabel}
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e5e7eb',
              borderRadius: '6px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend />
          
          {metrics.map(metric => {
            const config = metricConfig[metric]
            return (
              <Line
                key={metric}
                type="monotone"
                dataKey={metric}
                stroke={config.color}
                strokeWidth={2}
                dot={{ fill: config.color, strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: config.color, strokeWidth: 2 }}
                name={config.label}
                connectNulls={false}
              />
            )
          })}
        </LineChart>
      </ResponsiveContainer>

      {/* Estatísticas resumidas */}
      <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
        {metrics.map(metric => {
          const config = metricConfig[metric]
          const values = chartData.map(d => d[metric]).filter(v => v != null)
          
          if (values.length === 0) return null

          const latest = values[values.length - 1]
          const previous = values.length > 1 ? values[values.length - 2] : null
          const change = previous ? latest - previous : null
          
          return (
            <div key={metric} className="text-center">
              <p className="text-sm text-gray-500">{config.label}</p>
              <p className="text-lg font-semibold" style={{ color: config.color }}>
                {latest?.toFixed(1)}{config.unit}
              </p>
              {change !== null && (
                <p className={`text-xs ${change > 0 ? 'text-red-500' : change < 0 ? 'text-green-500' : 'text-gray-500'}`}>
                  {change > 0 ? '+' : ''}{change.toFixed(1)}{config.unit}
                </p>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}
