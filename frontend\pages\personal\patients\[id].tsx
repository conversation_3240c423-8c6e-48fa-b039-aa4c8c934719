import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import {
  User,
  Mail,
  Phone,
  Calendar,
  Target,
  Edit,
  Plus,
  TrendingUp,
  Activity,
  FileText,
  Eye,
  ArrowLeft
} from 'lucide-react'
import Layout from '../../../components/Layout'

interface Patient {
  id: number
  nome: string
  email?: string
  telefone?: string
  nascimento?: string
  sexo?: string
  profissao?: string
  objetivo?: string
  observacoes?: string
  ativo: boolean
  created_at: string
}

interface Assessment {
  id: number
  peso: number
  altura: number
  imc: number
  gordura_percentual?: number
  massa_gorda?: number
  massa_magra?: number
  data_avaliacao: string
}

export default function PatientDetailPage() {
  const router = useRouter()
  const { id } = router.query
  const [patient, setPatient] = useState<Patient | null>(null)
  const [assessments, setAssessments] = useState<Assessment[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (id) {
      fetchPatientData()
    }
  }, [id])

  const fetchPatientData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Mock data - em produção viria da API
      const patientData = {
        id: Number(id),
        nome: 'João Santos',
        email: '<EMAIL>',
        telefone: '(11) 97777-7777',
        nascimento: '1990-05-10',
        sexo: 'masculino',
        profissao: 'Engenheiro de Software',
        objetivo: 'Ganho de massa muscular e melhora do condicionamento físico',
        observacoes: 'Paciente muito dedicado, boa execução dos exercícios',
        ativo: true,
        created_at: '2024-01-20'
      }
      setPatient(patientData)

      // Mock assessments
      const assessmentsData = [
        {
          id: 1,
          peso: 75.5,
          altura: 1.78,
          imc: 23.8,
          gordura_percentual: 12.5,
          massa_gorda: 9.4,
          massa_magra: 66.1,
          data_avaliacao: '2024-01-20'
        },
        {
          id: 2,
          peso: 73.2,
          altura: 1.78,
          imc: 23.1,
          gordura_percentual: 14.2,
          massa_gorda: 10.4,
          massa_magra: 62.8,
          data_avaliacao: '2024-01-05'
        }
      ]
      setAssessments(assessmentsData)
    } catch (error) {
      console.error('Erro ao buscar dados do paciente:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const calculateAge = (birthDate: string) => {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }

    return age
  }

  const getLatestAssessment = () => {
    return assessments.length > 0 ? assessments[0] : null
  }

  if (isLoading) {
    return (
      <Layout title="Carregando...">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </Layout>
    )
  }

  if (!patient) {
    return (
      <Layout title="Paciente não encontrado">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">Paciente não encontrado</h2>
          <Link href="/personal/patients" className="text-blue-600 hover:text-blue-800 mt-4 inline-block">
            ← Voltar para lista de pacientes
          </Link>
        </div>
      </Layout>
    )
  }

  const latestAssessment = getLatestAssessment()

  return (
    <Layout title={`${patient.nome} - Personal Trainer`}>
      <Head>
        <title>{patient.nome} - Personal Trainer | Hypatium</title>
        <meta name="description" content={`Perfil do paciente ${patient.nome}`} />
      </Head>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Link
                  href="/personal/patients"
                  className="mr-4 p-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    <Activity className="h-6 w-6 text-blue-600 mr-2" />
                    {patient.nome}
                  </h1>
                  <p className="text-gray-600">Paciente de Personal Trainer</p>
                </div>
              </div>
              <div className="flex space-x-3">
                <Link
                  href={`/personal/workouts/new?patient=${patient.id}`}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Novo Treino
                </Link>
                <Link
                  href={`/personal/patients/${patient.id}/edit`}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </Link>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Patient Info */}
              <div className="lg:col-span-1">
                <div className="bg-white shadow rounded-lg p-6">
                  <div className="flex items-center mb-6">
                    <div className="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
                      <span className="text-xl font-medium text-blue-600">
                        {patient.nome.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="ml-4">
                      <h2 className="text-xl font-bold text-gray-900">{patient.nome}</h2>
                      <p className="text-sm text-gray-500">
                        Paciente desde {formatDate(patient.created_at)}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {patient.email && (
                      <div className="flex items-center">
                        <Mail className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">{patient.email}</span>
                      </div>
                    )}

                    {patient.telefone && (
                      <div className="flex items-center">
                        <Phone className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">{patient.telefone}</span>
                      </div>
                    )}

                    {patient.nascimento && (
                      <div className="flex items-center">
                        <Calendar className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">
                          {calculateAge(patient.nascimento)} anos ({formatDate(patient.nascimento)})
                        </span>
                      </div>
                    )}

                    {patient.profissao && (
                      <div className="flex items-center">
                        <User className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">{patient.profissao}</span>
                      </div>
                    )}
                  </div>

                  {patient.objetivo && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="flex items-start">
                        <Target className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                        <div>
                          <h3 className="text-sm font-medium text-gray-900">Objetivo</h3>
                          <p className="text-sm text-gray-600 mt-1">{patient.objetivo}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {patient.observacoes && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="flex items-start">
                        <FileText className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                        <div>
                          <h3 className="text-sm font-medium text-gray-900">Observações</h3>
                          <p className="text-sm text-gray-600 mt-1">{patient.observacoes}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* Latest Assessment */}
                {latestAssessment ? (
                  <div className="bg-white shadow rounded-lg p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium text-gray-900">Última Avaliação</h3>
                      <span className="text-sm text-gray-500">
                        {formatDate(latestAssessment.data_avaliacao)}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-gray-900">{latestAssessment.peso}kg</p>
                        <p className="text-sm text-gray-500">Peso</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-gray-900">{latestAssessment.imc?.toFixed(1)}</p>
                        <p className="text-sm text-gray-500">IMC</p>
                      </div>
                      {latestAssessment.gordura_percentual && (
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">
                            {latestAssessment.gordura_percentual.toFixed(1)}%
                          </p>
                          <p className="text-sm text-gray-500">Gordura</p>
                        </div>
                      )}
                      {latestAssessment.massa_magra && (
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">
                            {latestAssessment.massa_magra.toFixed(1)}kg
                          </p>
                          <p className="text-sm text-gray-500">Massa Magra</p>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="bg-white shadow rounded-lg p-6 text-center">
                    <TrendingUp className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma avaliação</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Comece criando uma avaliação física para este paciente.
                    </p>
                    <div className="mt-6">
                      <Link
                        href={`/personal/assessments/new?patient=${patient.id}`}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Nova Avaliação
                      </Link>
                    </div>
                  </div>
                )}

                {/* Quick Actions */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Ações Rápidas</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Link
                      href={`/personal/assessments/new?patient=${patient.id}`}
                      className="flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                    >
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Nova Avaliação
                    </Link>
                    <Link
                      href={`/personal/workouts/new?patient=${patient.id}`}
                      className="flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200"
                    >
                      <Activity className="h-4 w-4 mr-2" />
                      Novo Treino
                    </Link>
                    <Link
                      href={`/personal/patients/${patient.id}/assessments`}
                      className="flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Ver Avaliações
                    </Link>
                    <Link
                      href={`/personal/patients/${patient.id}/workouts`}
                      className="flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <Activity className="h-4 w-4 mr-2" />
                      Ver Treinos
                    </Link>
                  </div>
                </div>

                {/* Workouts Summary */}
                <div className="bg-white shadow rounded-lg p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Treinos Ativos</h3>
                    <Link
                      href={`/personal/patients/${patient.id}/workouts`}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      Ver todos
                    </Link>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900">Treino A - Peito e Tríceps</h4>
                      <p className="text-sm text-gray-600 mt-1">8 exercícios • 60 min</p>
                      <div className="flex justify-between items-center mt-3">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          Intermediário
                        </span>
                        <Link
                          href={`/personal/workouts/1`}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                      </div>
                    </div>
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900">Treino B - Costas e Bíceps</h4>
                      <p className="text-sm text-gray-600 mt-1">7 exercícios • 65 min</p>
                      <div className="flex justify-between items-center mt-3">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          Intermediário
                        </span>
                        <Link
                          href={`/personal/workouts/2`}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 text-center">
                    <Link
                      href={`/personal/workouts/new?patient=${patient.id}`}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Criar Novo Treino
                    </Link>
                  </div>
                </div>

                {/* Assessment History */}
                {assessments.length > 0 && (
                  <div className="bg-white shadow rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Histórico de Avaliações</h3>
                    <div className="space-y-3">
                      {assessments.slice(0, 5).map((assessment) => (
                        <div key={assessment.id} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {formatDate(assessment.data_avaliacao)}
                            </p>
                            <p className="text-sm text-gray-500">
                              Peso: {assessment.peso}kg | IMC: {assessment.imc?.toFixed(1)}
                            </p>
                          </div>
                          <Link
                            href={`/personal/assessments/${assessment.id}`}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            <Eye className="h-4 w-4" />
                          </Link>
                        </div>
                      ))}
                    </div>
                    {assessments.length > 5 && (
                      <div className="mt-4 text-center">
                        <Link
                          href={`/personal/assessments?patient=${patient.id}`}
                          className="text-blue-600 hover:text-blue-800 text-sm"
                        >
                          Ver todas as avaliações
                        </Link>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
