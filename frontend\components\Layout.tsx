import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import {
  Home,
  Users,
  Brain,
  Utensils,
  Activity,
  FileText,
  Settings,
  LogOut,
  Menu,
  X,
  Bell
} from 'lucide-react'
import Logo from './Logo'

interface LayoutProps {
  children: React.ReactNode
  title?: string
}

interface User {
  id: number
  nome: string
  email: string
  tipo: string
}

export default function Layout({ children, title }: LayoutProps) {
  const router = useRouter()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [user, setUser] = useState<User | null>(null)

  useEffect(() => {
    // Verificar autenticação
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    // Simular dados do usuário (em produção, viria da API)
    setUser({
      id: 1,
      nome: 'Administrador',
      email: '<EMAIL>',
      tipo: 'admin'
    })
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('token')
    router.push('/login')
  }

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: Home, current: router.pathname === '/dashboard' },
    { 
      name: 'Personal Trainer', 
      icon: Activity, 
      current: router.pathname.startsWith('/personal'),
      children: [
        { name: 'Pacientes', href: '/personal/patients' },
        { name: 'Novo Paciente', href: '/personal/patients/new' },
        { name: 'Exercícios', href: '/personal/exercises/library' },
        { name: 'Treinos', href: '/personal/workouts/new' }
      ]
    },
    { 
      name: 'Nutrição', 
      icon: Utensils, 
      current: router.pathname.startsWith('/nutrition'),
      children: [
        { name: 'Pacientes', href: '/nutrition/patients' },
        { name: 'Novo Paciente', href: '/nutrition/patients/new' },
        { name: 'Planos', href: '/nutrition/plans' },
        { name: 'Calculadora', href: '/nutrition/calculator' }
      ]
    },
    { 
      name: 'Psicologia', 
      icon: Brain, 
      current: router.pathname.startsWith('/psychology'),
      children: [
        { name: 'Pacientes', href: '/psychology/patients' },
        { name: 'Novo Paciente', href: '/psychology/patients/new' },
        { name: 'Sessões', href: '/psychology/sessions' },
        { name: 'Nova Sessão', href: '/psychology/sessions/new' }
      ]
    },
    { name: 'Relatórios', href: '/reports/generate', icon: FileText, current: router.pathname.startsWith('/reports') },
    { name: 'Configurações', href: '/settings/notifications', icon: Settings, current: router.pathname.startsWith('/settings') }
  ]

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-6 w-6 text-white" />
            </button>
          </div>
          <SidebarContent navigation={navigation} user={user} onLogout={handleLogout} />
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <SidebarContent navigation={navigation} user={user} onLogout={handleLogout} />
      </div>

      {/* Main content */}
      <div className="md:pl-64 flex flex-col flex-1">
        {/* Top header */}
        <div className="sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-white shadow">
          <button
            className="-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        {/* Page header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div>
                {title && <h1 className="text-2xl font-bold text-gray-900">{title}</h1>}
              </div>
              <div className="flex items-center space-x-4">
                <button className="p-2 text-gray-400 hover:text-gray-500">
                  <Bell className="h-6 w-6" />
                </button>
                <div className="flex items-center space-x-3">
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{user.nome}</p>
                    <p className="text-gray-500">{user.email}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1">
          {children}
        </main>
      </div>
    </div>
  )
}

function SidebarContent({ navigation, user, onLogout }: { 
  navigation: any[], 
  user: User, 
  onLogout: () => void 
}) {
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (name: string) => {
    setExpandedItems(prev => 
      prev.includes(name) 
        ? prev.filter(item => item !== name)
        : [...prev, name]
    )
  }

  return (
    <div className="flex-1 flex flex-col min-h-0 bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
        <div className="flex items-center flex-shrink-0 px-4">
          <Logo size={32} />
        </div>

        {/* Navigation */}
        <nav className="mt-8 flex-1 px-2 space-y-1">
          {navigation.map((item) => (
            <div key={item.name}>
              {item.children ? (
                <div>
                  <button
                    onClick={() => toggleExpanded(item.name)}
                    className={`w-full group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                      item.current
                        ? 'bg-blue-100 text-blue-900'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <item.icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </button>
                  {expandedItems.includes(item.name) && (
                    <div className="ml-8 space-y-1">
                      {item.children.map((child: any) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className="group flex items-center px-2 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <Link
                  href={item.href}
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    item.current
                      ? 'bg-blue-100 text-blue-900'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.name}
                </Link>
              )}
            </div>
          ))}
        </nav>
      </div>

      {/* User section */}
      <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
        <div className="flex-shrink-0 w-full group block">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-700">{user.nome}</p>
                <p className="text-xs text-gray-500 capitalize">{user.tipo}</p>
              </div>
            </div>
            <button
              onClick={onLogout}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="Sair"
            >
              <LogOut className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
