import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CreateAvaliacaoComposicaoDto {
  @ApiProperty({
    description: 'ID do aluno',
    example: 'clp123abc456',
  })
  @IsString()
  @IsNotEmpty()
  alunoId: string;

  // Dobras Cutâneas (mm)
  @ApiProperty({
    description: 'Dobra subescapular em mm',
    example: 15.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  subescapular?: number;

  @ApiProperty({
    description: 'Dobra tricipital em mm',
    example: 12.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  tricipital?: number;

  @ApiProperty({
    description: 'Dobra bicipital em mm',
    example: 8.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  bicipital?: number;

  @ApiProperty({
    description: 'Dobra torácica em mm',
    example: 10.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  toracica?: number;

  @ApiProperty({
    description: 'Dobra abdominal em mm',
    example: 18.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  abdominal?: number;

  @ApiProperty({
    description: 'Dobra axilar média em mm',
    example: 9.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  axilarMedia?: number;

  @ApiProperty({
    description: 'Dobra suprailíaca em mm',
    example: 14.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  suprailíaca?: number;

  @ApiProperty({
    description: 'Dobra da coxa em mm',
    example: 16.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  coxa?: number;

  @ApiProperty({
    description: 'Dobra da panturrilha em mm',
    example: 11.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  panturrilha?: number;

  // Circunferências (cm)
  @ApiProperty({
    description: 'Circunferência do ombro em cm',
    example: 110.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(200)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  ombro?: number;

  @ApiProperty({
    description: 'Circunferência do tórax em cm',
    example: 95.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(200)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  torax?: number;

  @ApiProperty({
    description: 'Circunferência da cintura em cm',
    example: 85.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(200)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  cintura?: number;

  @ApiProperty({
    description: 'Circunferência do abdomen em cm',
    example: 88.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(200)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  abdomen?: number;

  @ApiProperty({
    description: 'Circunferência do abdomen inferior em cm',
    example: 90.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(200)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  abdomenInferior?: number;

  @ApiProperty({
    description: 'Circunferência do braço direito em cm',
    example: 32.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(15)
  @Max(60)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  bracoD?: number;

  @ApiProperty({
    description: 'Circunferência do braço esquerdo em cm',
    example: 31.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(15)
  @Max(60)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  bracoE?: number;

  @ApiProperty({
    description: 'Circunferência do braço contraído direito em cm',
    example: 34.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(15)
  @Max(60)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  bracoContraidoD?: number;

  @ApiProperty({
    description: 'Circunferência do braço contraído esquerdo em cm',
    example: 33.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(15)
  @Max(60)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  bracoContraidoE?: number;

  @ApiProperty({
    description: 'Circunferência do antebraço direito em cm',
    example: 26.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(15)
  @Max(50)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  antebracoD?: number;

  @ApiProperty({
    description: 'Circunferência do antebraço esquerdo em cm',
    example: 25.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(15)
  @Max(50)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  antebracoE?: number;

  @ApiProperty({
    description: 'Circunferência do quadril em cm',
    example: 98.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(60)
  @Max(150)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  quadril?: number;

  @ApiProperty({
    description: 'Circunferência da coxa proximal direita em cm',
    example: 55.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(30)
  @Max(80)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  coxaProximalD?: number;

  @ApiProperty({
    description: 'Circunferência da coxa medial direita em cm',
    example: 52.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(30)
  @Max(80)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  coxaMedialD?: number;

  @ApiProperty({
    description: 'Circunferência da coxa distal direita em cm',
    example: 48.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(30)
  @Max(80)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  coxaDistalD?: number;

  @ApiProperty({
    description: 'Circunferência da coxa proximal esquerda em cm',
    example: 54.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(30)
  @Max(80)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  coxaProximalE?: number;

  @ApiProperty({
    description: 'Circunferência da coxa medial esquerda em cm',
    example: 51.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(30)
  @Max(80)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  coxaMedialE?: number;

  @ApiProperty({
    description: 'Circunferência da coxa distal esquerda em cm',
    example: 47.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(30)
  @Max(80)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  coxaDistalE?: number;

  @ApiProperty({
    description: 'Circunferência da panturrilha direita em cm',
    example: 36.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(20)
  @Max(60)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  panturrilhaD?: number;

  @ApiProperty({
    description: 'Circunferência da panturrilha esquerda em cm',
    example: 35.5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(20)
  @Max(60)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  panturrilhaE?: number;
}
