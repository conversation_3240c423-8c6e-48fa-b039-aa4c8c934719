"""
Testes principais da aplicação
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.core.database import get_db, Base
from app.core.config import settings

# Configurar banco de dados de teste
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture
def client():
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def auth_headers(client):
    """Criar usuário de teste e retornar headers de autenticação"""
    # Criar usuário admin de teste
    user_data = {
        "email": "<EMAIL>",
        "password": "testpass123",
        "nome": "Test User",
        "tipo": "admin"
    }
    
    # Registrar usuário
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 200
    
    # Fazer login
    login_data = {
        "username": user_data["email"],
        "password": user_data["password"]
    }
    
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}

class TestAuth:
    """Testes de autenticação"""
    
    def test_register_user(self, client):
        """Testar registro de usuário"""
        user_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "nome": "New User",
            "tipo": "personal"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["nome"] == user_data["nome"]
        assert data["tipo"] == user_data["tipo"]
    
    def test_login_user(self, client):
        """Testar login de usuário"""
        # Primeiro registrar
        user_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "nome": "Login User",
            "tipo": "personal"
        }
        client.post("/api/v1/auth/register", json=user_data)
        
        # Depois fazer login
        login_data = {
            "username": user_data["email"],
            "password": user_data["password"]
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_invalid_login(self, client):
        """Testar login com credenciais inválidas"""
        login_data = {
            "username": "<EMAIL>",
            "password": "wrongpassword"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 400

class TestPatients:
    """Testes de pacientes"""
    
    def test_create_patient(self, client, auth_headers):
        """Testar criação de paciente"""
        patient_data = {
            "nome": "João Silva",
            "email": "<EMAIL>",
            "telefone": "(11) 99999-9999",
            "nascimento": "1990-01-01",
            "sexo": "masculino",
            "objetivo": "Perda de peso"
        }
        
        response = client.post("/api/v1/patients/", json=patient_data, headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["nome"] == patient_data["nome"]
        assert data["email"] == patient_data["email"]
    
    def test_get_patients(self, client, auth_headers):
        """Testar listagem de pacientes"""
        # Criar um paciente primeiro
        patient_data = {
            "nome": "Maria Santos",
            "email": "<EMAIL>",
            "telefone": "(11) 88888-8888",
            "nascimento": "1985-05-15",
            "sexo": "feminino"
        }
        client.post("/api/v1/patients/", json=patient_data, headers=auth_headers)
        
        # Listar pacientes
        response = client.get("/api/v1/patients/", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) >= 1
        assert any(p["nome"] == patient_data["nome"] for p in data)
    
    def test_get_patient_by_id(self, client, auth_headers):
        """Testar busca de paciente por ID"""
        # Criar paciente
        patient_data = {
            "nome": "Carlos Oliveira",
            "email": "<EMAIL>"
        }
        response = client.post("/api/v1/patients/", json=patient_data, headers=auth_headers)
        patient_id = response.json()["id"]
        
        # Buscar por ID
        response = client.get(f"/api/v1/patients/{patient_id}", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == patient_id
        assert data["nome"] == patient_data["nome"]

class TestAssessments:
    """Testes de avaliações físicas"""
    
    def test_create_assessment(self, client, auth_headers):
        """Testar criação de avaliação física"""
        # Criar paciente primeiro
        patient_data = {"nome": "Test Patient", "email": "<EMAIL>"}
        patient_response = client.post("/api/v1/patients/", json=patient_data, headers=auth_headers)
        patient_id = patient_response.json()["id"]
        
        # Criar avaliação
        assessment_data = {
            "paciente_id": patient_id,
            "peso": 70.5,
            "altura": 1.75,
            "data_avaliacao": "2024-01-15T10:00:00"
        }
        
        response = client.post("/api/v1/assessments/", json=assessment_data, headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["peso"] == assessment_data["peso"]
        assert data["altura"] == assessment_data["altura"]
        assert data["imc"] is not None  # IMC deve ser calculado automaticamente
    
    def test_assessment_calculations(self, client, auth_headers):
        """Testar cálculos automáticos da avaliação"""
        # Criar paciente
        patient_data = {"nome": "Test Patient", "email": "<EMAIL>"}
        patient_response = client.post("/api/v1/patients/", json=patient_data, headers=auth_headers)
        patient_id = patient_response.json()["id"]
        
        # Criar avaliação com dados para cálculo
        assessment_data = {
            "paciente_id": patient_id,
            "peso": 80.0,
            "altura": 1.80,
            "data_avaliacao": "2024-01-15T10:00:00"
        }
        
        response = client.post("/api/v1/assessments/", json=assessment_data, headers=auth_headers)
        data = response.json()
        
        # Verificar se IMC foi calculado corretamente
        expected_imc = 80.0 / (1.80 ** 2)
        assert abs(data["imc"] - expected_imc) < 0.01

class TestNutrition:
    """Testes de nutrição"""
    
    def test_calculate_nutrition(self, client, auth_headers):
        """Testar cálculo nutricional"""
        calc_data = {
            "peso": 70.0,
            "altura": 1.75,
            "idade": 30,
            "sexo": "masculino",
            "nivel_atividade": "moderado",
            "objetivo": "manutencao"
        }
        
        response = client.post("/api/v1/nutrition/calculate", json=calc_data, headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "tmb" in data
        assert "get" in data
        assert "calorias_alvo" in data
        assert "macronutrientes" in data
        assert data["tmb"] > 0
        assert data["get"] > data["tmb"]

class TestReports:
    """Testes de relatórios"""
    
    def test_get_report_templates(self, client, auth_headers):
        """Testar obtenção de templates de relatórios"""
        response = client.get("/api/v1/reports/templates", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) > 0
        assert all("id" in template for template in data)
        assert all("name" in template for template in data)

class TestPDF:
    """Testes de geração de PDF"""
    
    def test_pdf_stats(self, client, auth_headers):
        """Testar estatísticas de PDFs"""
        response = client.get("/api/v1/pdf/stats", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "total_files" in data
        assert "total_size" in data
        assert "total_size_mb" in data

class TestNotifications:
    """Testes de notificações"""
    
    def test_notification_status(self, client, auth_headers):
        """Testar status do sistema de notificações"""
        response = client.get("/api/v1/notifications/status", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "email_service" in data
        assert "push_service" in data
        assert "services_available" in data

class TestUpload:
    """Testes de upload"""
    
    def test_upload_status(self, client, auth_headers):
        """Testar status do sistema de upload"""
        response = client.get("/api/v1/upload/status", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "upload_enabled" in data
        assert "max_file_size" in data
        assert "allowed_types" in data

class TestAPI:
    """Testes gerais da API"""
    
    def test_health_check(self, client):
        """Testar endpoint de health check"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_api_docs(self, client):
        """Testar acesso à documentação da API"""
        response = client.get("/docs")
        assert response.status_code == 200
    
    def test_openapi_schema(self, client):
        """Testar schema OpenAPI"""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert "paths" in data

# Testes de integração
class TestIntegration:
    """Testes de integração entre módulos"""
    
    def test_complete_patient_flow(self, client, auth_headers):
        """Testar fluxo completo de paciente"""
        # 1. Criar paciente
        patient_data = {
            "nome": "Paciente Completo",
            "email": "<EMAIL>",
            "nascimento": "1990-01-01",
            "sexo": "masculino"
        }
        patient_response = client.post("/api/v1/patients/", json=patient_data, headers=auth_headers)
        assert patient_response.status_code == 200
        patient_id = patient_response.json()["id"]
        
        # 2. Criar avaliação física
        assessment_data = {
            "paciente_id": patient_id,
            "peso": 75.0,
            "altura": 1.78,
            "data_avaliacao": "2024-01-15T10:00:00"
        }
        assessment_response = client.post("/api/v1/assessments/", json=assessment_data, headers=auth_headers)
        assert assessment_response.status_code == 200
        
        # 3. Criar plano nutricional
        nutrition_data = {
            "paciente_id": patient_id,
            "calorias_alvo": 2200,
            "proteinas_g": 150,
            "carboidratos_g": 250,
            "gorduras_g": 80
        }
        nutrition_response = client.post("/api/v1/nutrition/", json=nutrition_data, headers=auth_headers)
        assert nutrition_response.status_code == 200
        
        # 4. Verificar se tudo foi criado corretamente
        patient_check = client.get(f"/api/v1/patients/{patient_id}", headers=auth_headers)
        assert patient_check.status_code == 200
        
        assessments_check = client.get(f"/api/v1/assessments/patient/{patient_id}", headers=auth_headers)
        assert assessments_check.status_code == 200
        assert len(assessments_check.json()) >= 1
