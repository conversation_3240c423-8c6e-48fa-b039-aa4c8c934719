#!/usr/bin/env python3
"""
Servidor mínimo para testar o login
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session
from app.core.database import <PERSON><PERSON><PERSON><PERSON>
from app.models.models import User
from app.core.security import verify_password, create_access_token
import uvicorn

app = FastAPI(title="Hypatium Login Test")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.get("/")
def root():
    return {"message": "Hypatium Login Test Server"}

@app.get("/health")
def health():
    return {"status": "healthy"}

@app.post("/api/v1/auth/login")
def login(
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    """Endpoint de login"""
    try:
        # Buscar usuário
        user = db.query(User).filter(User.email == username).first()
        
        if not user:
            raise HTTPException(status_code=401, detail="Usuário não encontrado")
        
        if not user.ativo:
            raise HTTPException(status_code=401, detail="Usuário inativo")
        
        # Verificar senha
        if not verify_password(password, user.senha_hash):
            raise HTTPException(status_code=401, detail="Senha incorreta")
        
        # Criar token
        access_token = create_access_token(subject=str(user.id))
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "nome": user.nome,
                "email": user.email,
                "tipo": user.tipo.value
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Erro no login: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/api/v1/users/me")
def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """Obter usuário atual"""
    return {"message": "Token válido", "token": token[:20] + "..."}

if __name__ == "__main__":
    print("🚀 Iniciando servidor de teste...")
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
