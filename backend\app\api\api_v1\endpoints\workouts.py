"""
Endpoints de treinos
"""

from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.models.models import User
from app.schemas.workout import (
    Workout,
    WorkoutCreate,
    WorkoutUpdate,
    WorkoutWithExercises,
    WorkoutExercise,
    WorkoutExerciseCreate,
    WorkoutExerciseUpdate,
    WorkoutTemplate,
    WorkoutProgress
)
from app.services.workout_service import WorkoutService

router = APIRouter()

@router.post("/", response_model=Workout)
def create_workout(
    *,
    db: Session = Depends(get_db),
    workout_in: WorkoutCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Criar novo treino
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem criar treinos")

    workout_service = WorkoutService(db)

    # Definir personal como usuário atual se não especificado
    if not workout_in.personal_id:
        workout_in.personal_id = current_user.id

    try:
        workout = workout_service.create_workout(workout_in)
        return workout
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/patient/{patient_id}", response_model=List[Workout])
def read_workouts_by_patient(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active_only: bool = Query(False),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter treinos de um paciente
    """
    workout_service = WorkoutService(db)

    if active_only:
        workouts = workout_service.get_active_workouts_by_patient(patient_id)
    else:
        workouts = workout_service.get_workouts_by_patient(patient_id, skip=skip, limit=limit)

    return workouts

@router.get("/personal/{personal_id}", response_model=List[Workout])
def read_workouts_by_personal(
    *,
    db: Session = Depends(get_db),
    personal_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter treinos criados por um personal trainer
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"] and current_user.id != personal_id:
        raise HTTPException(status_code=403, detail="Sem permissão para acessar estes treinos")

    workout_service = WorkoutService(db)
    workouts = workout_service.get_workouts_by_personal(personal_id, skip=skip, limit=limit)
    return workouts

@router.get("/{workout_id}", response_model=WorkoutWithExercises)
def read_workout(
    *,
    db: Session = Depends(get_db),
    workout_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter treino por ID com exercícios
    """
    workout_service = WorkoutService(db)
    workout = workout_service.get_workout(workout_id)

    if not workout:
        raise HTTPException(status_code=404, detail="Treino não encontrado")

    # Obter exercícios
    exercises = workout_service.get_exercises_by_workout(workout_id)

    # Converter para schema com exercícios
    workout_dict = {
        **workout.__dict__,
        "exercicios": exercises
    }

    return workout_dict

@router.put("/{workout_id}", response_model=Workout)
def update_workout(
    *,
    db: Session = Depends(get_db),
    workout_id: int,
    workout_in: WorkoutUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Atualizar treino
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem atualizar treinos")

    workout_service = WorkoutService(db)
    workout = workout_service.update_workout(workout_id, workout_in)

    if not workout:
        raise HTTPException(status_code=404, detail="Treino não encontrado")

    return workout

@router.delete("/{workout_id}")
def delete_workout(
    *,
    db: Session = Depends(get_db),
    workout_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Deletar treino
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem deletar treinos")

    workout_service = WorkoutService(db)
    success = workout_service.delete_workout(workout_id)

    if not success:
        raise HTTPException(status_code=404, detail="Treino não encontrado")

    return {"message": "Treino deletado com sucesso"}

@router.post("/{workout_id}/deactivate", response_model=Workout)
def deactivate_workout(
    *,
    db: Session = Depends(get_db),
    workout_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Desativar treino (soft delete)
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem desativar treinos")

    workout_service = WorkoutService(db)
    workout = workout_service.deactivate_workout(workout_id)

    if not workout:
        raise HTTPException(status_code=404, detail="Treino não encontrado")

    return workout

# Endpoints para exercícios
@router.post("/{workout_id}/exercises", response_model=WorkoutExercise)
def create_exercise(
    *,
    db: Session = Depends(get_db),
    workout_id: int,
    exercise_in: WorkoutExerciseCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Criar novo exercício em um treino
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem criar exercícios")

    workout_service = WorkoutService(db)

    # Definir treino_id
    exercise_in.treino_id = workout_id

    try:
        exercise = workout_service.create_exercise(exercise_in)
        return exercise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{workout_id}/exercises", response_model=List[WorkoutExercise])
def read_exercises_by_workout(
    *,
    db: Session = Depends(get_db),
    workout_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter exercícios de um treino
    """
    workout_service = WorkoutService(db)
    exercises = workout_service.get_exercises_by_workout(workout_id)
    return exercises

@router.put("/exercises/{exercise_id}", response_model=WorkoutExercise)
def update_exercise(
    *,
    db: Session = Depends(get_db),
    exercise_id: int,
    exercise_in: WorkoutExerciseUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Atualizar exercício
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem atualizar exercícios")

    workout_service = WorkoutService(db)
    exercise = workout_service.update_exercise(exercise_id, exercise_in)

    if not exercise:
        raise HTTPException(status_code=404, detail="Exercício não encontrado")

    return exercise

@router.delete("/exercises/{exercise_id}")
def delete_exercise(
    *,
    db: Session = Depends(get_db),
    exercise_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Deletar exercício
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem deletar exercícios")

    workout_service = WorkoutService(db)
    success = workout_service.delete_exercise(exercise_id)

    if not success:
        raise HTTPException(status_code=404, detail="Exercício não encontrado")

    return {"message": "Exercício deletado com sucesso"}

@router.post("/{workout_id}/exercises/reorder")
def reorder_exercises(
    *,
    db: Session = Depends(get_db),
    workout_id: int,
    exercise_orders: List[Dict[str, int]],
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Reordenar exercícios de um treino
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem reordenar exercícios")

    workout_service = WorkoutService(db)
    success = workout_service.reorder_exercises(workout_id, exercise_orders)

    if not success:
        raise HTTPException(status_code=400, detail="Erro ao reordenar exercícios")

    return {"message": "Exercícios reordenados com sucesso"}

@router.post("/{workout_id}/duplicate", response_model=Workout)
def duplicate_workout(
    *,
    db: Session = Depends(get_db),
    workout_id: int,
    new_patient_id: Optional[int] = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Duplicar treino
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem duplicar treinos")

    workout_service = WorkoutService(db)
    new_workout = workout_service.duplicate_workout(workout_id, new_patient_id)

    if not new_workout:
        raise HTTPException(status_code=404, detail="Treino não encontrado")

    return new_workout

@router.get("/{workout_id}/statistics", response_model=Dict[str, Any])
def get_workout_statistics(
    *,
    db: Session = Depends(get_db),
    workout_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter estatísticas do treino
    """
    workout_service = WorkoutService(db)
    stats = workout_service.get_workout_statistics(workout_id)

    if not stats:
        raise HTTPException(status_code=404, detail="Treino não encontrado")

    return stats

@router.get("/exercises/search", response_model=List[Dict[str, Any]])
def search_exercises(
    *,
    db: Session = Depends(get_db),
    query: Optional[str] = Query(None),
    muscle_group: Optional[str] = Query(None),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Buscar exercícios por nome ou grupo muscular
    """
    workout_service = WorkoutService(db)
    exercises = workout_service.search_exercises(query, muscle_group)
    return exercises