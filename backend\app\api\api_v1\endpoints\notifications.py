"""
Endpoints para sistema de notificações
"""

from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.models.models import User
from app.services.notification_service import NotificationService

router = APIRouter()

@router.post("/appointment-reminder/{session_id}")
def send_appointment_reminder(
    *,
    db: Session = Depends(get_db),
    session_id: int,
    hours_before: int = 24,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Enviar lembrete de consulta
    """
    notification_service = NotificationService(db)
    result = notification_service.send_appointment_reminder(session_id, hours_before)
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result['error'])
    
    return result

@router.post("/workout-reminder/{patient_id}/{workout_id}")
def send_workout_reminder(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    workout_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Enviar lembrete de treino
    """
    notification_service = NotificationService(db)
    result = notification_service.send_workout_reminder(patient_id, workout_id)
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result['error'])
    
    return result

@router.post("/nutrition-reminder/{patient_id}/{plan_id}")
def send_nutrition_reminder(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    plan_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Enviar lembrete nutricional
    """
    notification_service = NotificationService(db)
    result = notification_service.send_nutrition_reminder(patient_id, plan_id)
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result['error'])
    
    return result

@router.post("/progress-update/{patient_id}")
def send_progress_update(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    progress_data: Dict[str, Any],
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Enviar atualização de progresso
    """
    notification_service = NotificationService(db)
    result = notification_service.send_progress_update(
        patient_id, 
        current_user.id, 
        progress_data
    )
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result['error'])
    
    return result

@router.post("/report-ready/{patient_id}/{report_id}")
def send_report_ready(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    report_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Notificar que relatório está pronto
    """
    notification_service = NotificationService(db)
    result = notification_service.send_report_ready(
        patient_id, 
        current_user.id, 
        report_id
    )
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result['error'])
    
    return result

@router.post("/schedule-reminders")
def schedule_appointment_reminders(
    *,
    db: Session = Depends(get_db),
    background_tasks: BackgroundTasks,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Agendar lembretes de consultas (executar em background)
    """
    if current_user.tipo not in ["admin", "psicologo"]:
        raise HTTPException(status_code=403, detail="Sem permissão para agendar lembretes")
    
    def run_scheduler():
        notification_service = NotificationService(db)
        return notification_service.schedule_appointment_reminders()
    
    background_tasks.add_task(run_scheduler)
    
    return {"message": "Agendamento de lembretes iniciado em background"}

@router.get("/preferences/{patient_id}")
def get_notification_preferences(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter preferências de notificação do paciente
    """
    # Verificar permissões
    if current_user.tipo not in ["admin"] and current_user.id != patient_id:
        raise HTTPException(status_code=403, detail="Sem permissão para acessar preferências")
    
    notification_service = NotificationService(db)
    preferences = notification_service.get_notification_preferences(patient_id)
    
    return preferences

@router.put("/preferences/{patient_id}")
def update_notification_preferences(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    preferences: Dict[str, Any],
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Atualizar preferências de notificação
    """
    # Verificar permissões
    if current_user.tipo not in ["admin"] and current_user.id != patient_id:
        raise HTTPException(status_code=403, detail="Sem permissão para alterar preferências")
    
    notification_service = NotificationService(db)
    result = notification_service.update_notification_preferences(patient_id, preferences)
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result['error'])
    
    return result

@router.get("/status")
def get_notification_status(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter status do sistema de notificações
    """
    notification_service = NotificationService(db)
    status = notification_service.get_notification_status()
    
    return status

@router.post("/test/{patient_id}")
def test_notification(
    *,
    db: Session = Depends(get_db),
    patient_id: int,
    notification_type: str = "test",
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Testar envio de notificação
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Apenas administradores podem testar notificações")
    
    notification_service = NotificationService(db)
    
    # Dados de teste
    test_data = {
        'type': notification_type,
        'title': 'Teste de Notificação',
        'message': 'Esta é uma notificação de teste do sistema Hypatium.',
        'patient_id': patient_id,
        'professional_id': current_user.id,
        'test': True
    }
    
    # Buscar paciente
    from app.models.models import Patient
    patient = db.query(Patient).filter(Patient.id == patient_id).first()
    if not patient:
        raise HTTPException(status_code=404, detail="Paciente não encontrado")
    
    result = notification_service._send_notification(patient, test_data)
    
    return result
