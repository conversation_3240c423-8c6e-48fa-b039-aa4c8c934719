"""
Serviço para gerenciamento de treinos
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime

from app.models.models import Workout, WorkoutExercise, Patient
from app.schemas.workout import (
    WorkoutCreate, 
    WorkoutUpdate,
    WorkoutExerciseCreate,
    WorkoutExerciseUpdate,
    WorkoutTemplate
)

class WorkoutService:
    def __init__(self, db: Session):
        self.db = db

    def get_workout(self, workout_id: int) -> Optional[Workout]:
        """Obter treino por ID"""
        return self.db.query(Workout).filter(Workout.id == workout_id).first()

    def get_workouts_by_patient(self, patient_id: int, skip: int = 0, limit: int = 100) -> List[Workout]:
        """Obter treinos de um paciente"""
        return self.db.query(Workout).filter(
            Workout.paciente_id == patient_id
        ).order_by(Workout.data_inicio.desc()).offset(skip).limit(limit).all()

    def get_active_workouts_by_patient(self, patient_id: int) -> List[Workout]:
        """Obter treinos ativos de um paciente"""
        return self.db.query(Workout).filter(
            Workout.paciente_id == patient_id,
            Workout.ativo == True
        ).order_by(Workout.data_inicio.desc()).all()

    def get_workouts_by_personal(self, personal_id: int, skip: int = 0, limit: int = 100) -> List[Workout]:
        """Obter treinos criados por um personal trainer"""
        return self.db.query(Workout).filter(
            Workout.personal_id == personal_id
        ).order_by(Workout.data_inicio.desc()).offset(skip).limit(limit).all()

    def create_workout(self, workout_in: WorkoutCreate) -> Workout:
        """Criar novo treino"""
        # Verificar se paciente existe
        patient = self.db.query(Patient).filter(Patient.id == workout_in.paciente_id).first()
        if not patient:
            raise ValueError("Paciente não encontrado")

        db_workout = Workout(**workout_in.dict())
        self.db.add(db_workout)
        self.db.commit()
        self.db.refresh(db_workout)
        return db_workout

    def update_workout(self, workout_id: int, workout_in: WorkoutUpdate) -> Optional[Workout]:
        """Atualizar treino"""
        db_workout = self.get_workout(workout_id)
        if not db_workout:
            return None

        update_data = workout_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_workout, field, value)

        self.db.commit()
        self.db.refresh(db_workout)
        return db_workout

    def delete_workout(self, workout_id: int) -> bool:
        """Deletar treino"""
        db_workout = self.get_workout(workout_id)
        if not db_workout:
            return False

        # Deletar exercícios associados
        self.db.query(WorkoutExercise).filter(
            WorkoutExercise.treino_id == workout_id
        ).delete()

        self.db.delete(db_workout)
        self.db.commit()
        return True

    def deactivate_workout(self, workout_id: int) -> Optional[Workout]:
        """Desativar treino (soft delete)"""
        db_workout = self.get_workout(workout_id)
        if not db_workout:
            return None

        db_workout.ativo = False
        db_workout.data_fim = datetime.utcnow()
        self.db.commit()
        self.db.refresh(db_workout)
        return db_workout

    # Métodos para exercícios
    def get_exercise(self, exercise_id: int) -> Optional[WorkoutExercise]:
        """Obter exercício por ID"""
        return self.db.query(WorkoutExercise).filter(
            WorkoutExercise.id == exercise_id
        ).first()

    def get_exercises_by_workout(self, workout_id: int) -> List[WorkoutExercise]:
        """Obter exercícios de um treino"""
        return self.db.query(WorkoutExercise).filter(
            WorkoutExercise.treino_id == workout_id
        ).order_by(WorkoutExercise.ordem).all()

    def create_exercise(self, exercise_in: WorkoutExerciseCreate) -> WorkoutExercise:
        """Criar novo exercício"""
        # Verificar se treino existe
        workout = self.get_workout(exercise_in.treino_id)
        if not workout:
            raise ValueError("Treino não encontrado")

        db_exercise = WorkoutExercise(**exercise_in.dict())
        self.db.add(db_exercise)
        self.db.commit()
        self.db.refresh(db_exercise)
        return db_exercise

    def update_exercise(self, exercise_id: int, exercise_in: WorkoutExerciseUpdate) -> Optional[WorkoutExercise]:
        """Atualizar exercício"""
        db_exercise = self.get_exercise(exercise_id)
        if not db_exercise:
            return None

        update_data = exercise_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_exercise, field, value)

        self.db.commit()
        self.db.refresh(db_exercise)
        return db_exercise

    def delete_exercise(self, exercise_id: int) -> bool:
        """Deletar exercício"""
        db_exercise = self.get_exercise(exercise_id)
        if not db_exercise:
            return False

        self.db.delete(db_exercise)
        self.db.commit()
        return True

    def reorder_exercises(self, workout_id: int, exercise_orders: List[Dict[str, int]]) -> bool:
        """Reordenar exercícios de um treino"""
        try:
            for item in exercise_orders:
                exercise_id = item.get('exercise_id')
                new_order = item.get('ordem')
                
                exercise = self.get_exercise(exercise_id)
                if exercise and exercise.treino_id == workout_id:
                    exercise.ordem = new_order

            self.db.commit()
            return True
        except Exception:
            self.db.rollback()
            return False

    def duplicate_workout(self, workout_id: int, new_patient_id: Optional[int] = None) -> Optional[Workout]:
        """Duplicar treino para o mesmo paciente ou outro"""
        original_workout = self.get_workout(workout_id)
        if not original_workout:
            return None

        # Criar novo treino
        new_workout_data = {
            "nome": f"{original_workout.nome} (Cópia)",
            "descricao": original_workout.descricao,
            "data_inicio": datetime.utcnow(),
            "observacoes": original_workout.observacoes,
            "paciente_id": new_patient_id or original_workout.paciente_id,
            "personal_id": original_workout.personal_id
        }

        new_workout = Workout(**new_workout_data)
        self.db.add(new_workout)
        self.db.flush()  # Para obter o ID

        # Duplicar exercícios
        original_exercises = self.get_exercises_by_workout(workout_id)
        for exercise in original_exercises:
            new_exercise_data = {
                "nome_exercicio": exercise.nome_exercicio,
                "grupo_muscular": exercise.grupo_muscular,
                "video_url": exercise.video_url,
                "imagem_url": exercise.imagem_url,
                "semana": exercise.semana,
                "series": exercise.series,
                "repeticoes": exercise.repeticoes,
                "carga": exercise.carga,
                "descanso": exercise.descanso,
                "observacoes": exercise.observacoes,
                "ordem": exercise.ordem,
                "treino_id": new_workout.id
            }
            new_exercise = WorkoutExercise(**new_exercise_data)
            self.db.add(new_exercise)

        self.db.commit()
        self.db.refresh(new_workout)
        return new_workout

    def create_from_template(self, template: WorkoutTemplate, patient_id: int, personal_id: int) -> Workout:
        """Criar treino a partir de template"""
        # Criar treino
        workout_data = {
            "nome": template.nome,
            "descricao": template.descricao,
            "data_inicio": datetime.utcnow(),
            "paciente_id": patient_id,
            "personal_id": personal_id
        }

        new_workout = Workout(**workout_data)
        self.db.add(new_workout)
        self.db.flush()

        # Adicionar exercícios do template
        for i, exercise_template in enumerate(template.exercicios, 1):
            exercise_data = {
                **exercise_template.dict(),
                "treino_id": new_workout.id,
                "ordem": i
            }
            new_exercise = WorkoutExercise(**exercise_data)
            self.db.add(new_exercise)

        self.db.commit()
        self.db.refresh(new_workout)
        return new_workout

    def get_workout_statistics(self, workout_id: int) -> Dict[str, Any]:
        """Obter estatísticas do treino"""
        workout = self.get_workout(workout_id)
        if not workout:
            return {}

        exercises = self.get_exercises_by_workout(workout_id)
        
        # Agrupar por grupo muscular
        muscle_groups = {}
        total_exercises = len(exercises)
        total_sets = sum(ex.series for ex in exercises)

        for exercise in exercises:
            group = exercise.grupo_muscular or "Não especificado"
            if group not in muscle_groups:
                muscle_groups[group] = {"count": 0, "sets": 0}
            muscle_groups[group]["count"] += 1
            muscle_groups[group]["sets"] += exercise.series

        return {
            "total_exercises": total_exercises,
            "total_sets": total_sets,
            "muscle_groups": muscle_groups,
            "duration_weeks": max([ex.semana for ex in exercises]) if exercises else 0,
            "workout_active": workout.ativo,
            "created_date": workout.created_at,
            "last_updated": workout.updated_at
        }

    def search_exercises(self, query: str, muscle_group: Optional[str] = None) -> List[Dict[str, Any]]:
        """Buscar exercícios por nome ou grupo muscular"""
        filters = []
        
        if query:
            filters.append(WorkoutExercise.nome_exercicio.ilike(f"%{query}%"))
        
        if muscle_group:
            filters.append(WorkoutExercise.grupo_muscular == muscle_group)

        exercises = self.db.query(WorkoutExercise).filter(*filters).distinct(
            WorkoutExercise.nome_exercicio
        ).limit(50).all()

        # Retornar exercícios únicos com informações básicas
        unique_exercises = {}
        for exercise in exercises:
            name = exercise.nome_exercicio
            if name not in unique_exercises:
                unique_exercises[name] = {
                    "nome": name,
                    "grupo_muscular": exercise.grupo_muscular,
                    "video_url": exercise.video_url,
                    "imagem_url": exercise.imagem_url
                }

        return list(unique_exercises.values())
