import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  Brain, 
  User, 
  Calendar,
  Clock,
  Target,
  FileText,
  Mic,
  Save,
  X,
  Upload
} from 'lucide-react'

const sessionSchema = z.object({
  paciente_id: z.number().min(1, 'Selecione um paciente'),
  data_sessao: z.string().min(1, 'Data da sessão é obrigatória'),
  hora_sessao: z.string().min(1, 'Hora da sessão é obrigatória'),
  duracao_minutos: z.number().min(15).max(300).default(60),
  
  // Escalas de avaliação
  humor_escala: z.number().min(1).max(10).optional(),
  ansiedade_escala: z.number().min(1).max(10).optional(),
  motivacao_escala: z.number().min(1).max(10).optional(),
  
  // Conteúdo da sessão
  objetivos_sessao: z.string().optional(),
  anotacoes: z.string().optional(),
  tecnicas_utilizadas: z.string().optional(),
  observacoes_comportamentais: z.string().optional(),
  
  // Próxima sessão
  data_proxima_sessao: z.string().optional(),
  observacoes_proxima_sessao: z.string().optional(),
})

type SessionForm = z.infer<typeof sessionSchema>

interface Patient {
  id: number
  nome: string
  email?: string
}

export default function NewPsychologySessionPage() {
  const router = useRouter()
  const { patient: patientId } = router.query
  const [patients, setPatients] = useState<Patient[]>([])
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [audioFile, setAudioFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<SessionForm>({
    resolver: zodResolver(sessionSchema),
    defaultValues: {
      data_sessao: new Date().toISOString().split('T')[0],
      hora_sessao: new Date().toTimeString().slice(0, 5),
      duracao_minutos: 60,
      paciente_id: patientId ? parseInt(patientId as string) : 0,
    }
  })

  const watchedValues = watch()

  useEffect(() => {
    fetchPatients()
  }, [])

  useEffect(() => {
    if (patientId) {
      setValue('paciente_id', parseInt(patientId as string))
      const patient = patients.find(p => p.id === parseInt(patientId as string))
      setSelectedPatient(patient || null)
    }
  }, [patientId, patients, setValue])

  useEffect(() => {
    if (watchedValues.paciente_id) {
      const patient = patients.find(p => p.id === watchedValues.paciente_id)
      setSelectedPatient(patient || null)
    }
  }, [watchedValues.paciente_id, patients])

  const fetchPatients = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/patients/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        setPatients(data)
      }
    } catch (error) {
      console.error('Erro ao buscar pacientes:', error)
    }
  }

  const handleAudioUpload = async (file: File) => {
    setIsUploading(true)
    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/upload/audio`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          body: formData,
        }
      )

      if (response.ok) {
        const result = await response.json()
        return result.file_url
      }
    } catch (error) {
      console.error('Erro ao fazer upload do áudio:', error)
    } finally {
      setIsUploading(false)
    }
    return null
  }

  const onSubmit = async (data: SessionForm) => {
    setIsLoading(true)
    setError('')

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Upload do áudio se houver
      let audioUrl = null
      if (audioFile) {
        audioUrl = await handleAudioUpload(audioFile)
      }

      // Combinar data e hora
      const dataHora = new Date(`${data.data_sessao}T${data.hora_sessao}:00`)

      // Preparar dados da sessão
      const sessionData = {
        paciente_id: data.paciente_id,
        data_sessao: dataHora.toISOString(),
        duracao_minutos: data.duracao_minutos,
        humor_escala: data.humor_escala,
        ansiedade_escala: data.ansiedade_escala,
        motivacao_escala: data.motivacao_escala,
        objetivos_sessao: data.objetivos_sessao,
        anotacoes: data.anotacoes,
        tecnicas_utilizadas: data.tecnicas_utilizadas,
        observacoes_comportamentais: data.observacoes_comportamentais,
        audio_url: audioUrl,
        data_proxima_sessao: data.data_proxima_sessao ? new Date(data.data_proxima_sessao).toISOString() : null,
        observacoes_proxima_sessao: data.observacoes_proxima_sessao,
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/psychology/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(sessionData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Erro ao criar sessão')
      }

      const session = await response.json()
      router.push('/psychology/sessions')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao criar sessão')
    } finally {
      setIsLoading(false)
    }
  }

  const getScaleDescription = (value: number) => {
    if (value <= 3) return 'Baixo'
    if (value <= 6) return 'Moderado'
    return 'Alto'
  }

  return (
    <>
      <Head>
        <title>Nova Sessão Psicológica - Hypatium</title>
        <meta name="description" content="Criar nova sessão psicológica" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href="/psychology/sessions" className="text-blue-600 hover:text-blue-800">
                  ← Voltar para Sessões
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Brain className="h-6 w-6 mr-2" />
                Nova Sessão Psicológica
              </h1>
              <div></div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Informações Básicas */}
              <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Informações da Sessão
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Dados básicos da sessão psicológica.
                  </p>
                </div>

                {error && (
                  <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                <div className="grid grid-cols-6 gap-6">
                  <div className="col-span-6">
                    <label className="block text-sm font-medium text-gray-700">
                      Paciente *
                    </label>
                    <select
                      {...register('paciente_id', { valueAsNumber: true })}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value={0}>Selecione um paciente...</option>
                      {patients.map(patient => (
                        <option key={patient.id} value={patient.id}>
                          {patient.nome}
                        </option>
                      ))}
                    </select>
                    {errors.paciente_id && (
                      <p className="mt-1 text-sm text-red-600">{errors.paciente_id.message}</p>
                    )}
                  </div>

                  <div className="col-span-6 sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Data da Sessão *
                    </label>
                    <div className="mt-1 relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Calendar className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        {...register('data_sessao')}
                        type="date"
                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    {errors.data_sessao && (
                      <p className="mt-1 text-sm text-red-600">{errors.data_sessao.message}</p>
                    )}
                  </div>

                  <div className="col-span-6 sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Hora da Sessão *
                    </label>
                    <div className="mt-1 relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Clock className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        {...register('hora_sessao')}
                        type="time"
                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    {errors.hora_sessao && (
                      <p className="mt-1 text-sm text-red-600">{errors.hora_sessao.message}</p>
                    )}
                  </div>

                  <div className="col-span-6 sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Duração (minutos) *
                    </label>
                    <input
                      {...register('duracao_minutos', { valueAsNumber: true })}
                      type="number"
                      min="15"
                      max="300"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    {errors.duracao_minutos && (
                      <p className="mt-1 text-sm text-red-600">{errors.duracao_minutos.message}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Escalas de Avaliação */}
              <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">
                    Escalas de Avaliação
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Avalie o estado emocional do paciente (escala de 1 a 10).
                  </p>
                </div>

                <div className="grid grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Humor (1-10)
                    </label>
                    <input
                      {...register('humor_escala', { valueAsNumber: true })}
                      type="number"
                      min="1"
                      max="10"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    {watchedValues.humor_escala && (
                      <p className="mt-1 text-xs text-gray-500">
                        {getScaleDescription(watchedValues.humor_escala)}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Ansiedade (1-10)
                    </label>
                    <input
                      {...register('ansiedade_escala', { valueAsNumber: true })}
                      type="number"
                      min="1"
                      max="10"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    {watchedValues.ansiedade_escala && (
                      <p className="mt-1 text-xs text-gray-500">
                        {getScaleDescription(watchedValues.ansiedade_escala)}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Motivação (1-10)
                    </label>
                    <input
                      {...register('motivacao_escala', { valueAsNumber: true })}
                      type="number"
                      min="1"
                      max="10"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    {watchedValues.motivacao_escala && (
                      <p className="mt-1 text-xs text-gray-500">
                        {getScaleDescription(watchedValues.motivacao_escala)}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Conteúdo da Sessão */}
              <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Conteúdo da Sessão
                  </h3>
                </div>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Objetivos da Sessão
                    </label>
                    <textarea
                      {...register('objetivos_sessao')}
                      rows={3}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Descreva os objetivos principais desta sessão..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Anotações da Sessão
                    </label>
                    <textarea
                      {...register('anotacoes')}
                      rows={5}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Registre os pontos principais discutidos, insights, progressos observados..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Técnicas Utilizadas
                    </label>
                    <input
                      {...register('tecnicas_utilizadas')}
                      type="text"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Ex: TCC, Mindfulness, Relaxamento..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Observações Comportamentais
                    </label>
                    <textarea
                      {...register('observacoes_comportamentais')}
                      rows={3}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Observações sobre linguagem corporal, expressões, mudanças de comportamento..."
                    />
                  </div>
                </div>
              </div>

              {/* Upload de Áudio */}
              <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center">
                    <Mic className="h-5 w-5 mr-2" />
                    Gravação da Sessão
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Faça upload da gravação de áudio da sessão (opcional).
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Arquivo de Áudio
                  </label>
                  <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                    <div className="space-y-1 text-center">
                      <Mic className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                          <span>Selecionar arquivo</span>
                          <input
                            type="file"
                            accept="audio/*"
                            className="sr-only"
                            onChange={(e) => setAudioFile(e.target.files?.[0] || null)}
                          />
                        </label>
                        <p className="pl-1">ou arraste aqui</p>
                      </div>
                      <p className="text-xs text-gray-500">
                        MP3, WAV, M4A até 50MB
                      </p>
                    </div>
                  </div>
                  {audioFile && (
                    <p className="mt-2 text-sm text-green-600">
                      Arquivo selecionado: {audioFile.name}
                    </p>
                  )}
                </div>
              </div>

              {/* Próxima Sessão */}
              <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center">
                    <Target className="h-5 w-5 mr-2" />
                    Próxima Sessão
                  </h3>
                </div>

                <div className="grid grid-cols-6 gap-6">
                  <div className="col-span-6 sm:col-span-3">
                    <label className="block text-sm font-medium text-gray-700">
                      Data da Próxima Sessão
                    </label>
                    <input
                      {...register('data_proxima_sessao')}
                      type="date"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="col-span-6">
                    <label className="block text-sm font-medium text-gray-700">
                      Observações para Próxima Sessão
                    </label>
                    <textarea
                      {...register('observacoes_proxima_sessao')}
                      rows={3}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Pontos a abordar, exercícios para casa, objetivos específicos..."
                    />
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3">
                <Link
                  href="/psychology/sessions"
                  className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancelar
                </Link>
                <button
                  type="submit"
                  disabled={isLoading || isUploading}
                  className="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isLoading || isUploading ? (
                    <div className="loading-spinner mr-2"></div>
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {isUploading ? 'Fazendo upload...' : 'Salvar Sessão'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  )
}
