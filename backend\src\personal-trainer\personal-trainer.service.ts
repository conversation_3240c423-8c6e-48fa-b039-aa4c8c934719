import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { UpdatePersonalTrainerDto } from './dto/update-personal-trainer.dto';

@Injectable()
export class PersonalTrainerService {
  constructor(private prisma: PrismaService) {}

  async findOne(id: string) {
    const personal = await this.prisma.personalTrainer.findUnique({
      where: { id },
      include: {
        alunos: {
          select: {
            id: true,
            nome: true,
            email: true,
            telefone: true,
            pesoAtual: true,
            estatura: true,
            idade: true,
            dataCadastro: true,
          },
        },
        _count: {
          select: {
            alunos: true,
            avaliacoes: true,
          },
        },
      },
    });

    if (!personal) {
      throw new NotFoundException('Personal Trainer não encontrado');
    }

    const { senha, ...result } = personal;
    return result;
  }

  async update(id: string, updatePersonalTrainerDto: UpdatePersonalTrainerDto) {
    const personal = await this.prisma.personalTrainer.update({
      where: { id },
      data: updatePersonalTrainerDto,
    });

    const { senha, ...result } = personal;
    return result;
  }

  async getDashboardStats(personalId: string) {
    const [totalAlunos, totalAvaliacoes, avaliacoesRecentes] = await Promise.all([
      this.prisma.aluno.count({
        where: { personalId },
      }),
      this.prisma.avaliacaoComposicao.count({
        where: { personalId },
      }),
      this.prisma.avaliacaoComposicao.findMany({
        where: { personalId },
        include: {
          aluno: {
            select: {
              nome: true,
            },
          },
        },
        orderBy: {
          dataAvaliacao: 'desc',
        },
        take: 5,
      }),
    ]);

    return {
      totalAlunos,
      totalAvaliacoes,
      avaliacoesRecentes,
    };
  }
}
