"""
Schemas Pydantic para treinos
"""

from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime

class WorkoutExerciseBase(BaseModel):
    """Schema base para exercício do treino"""
    nome_exercicio: str = Field(..., min_length=1, max_length=100)
    grupo_muscular: Optional[str] = None
    video_url: Optional[str] = None
    imagem_url: Optional[str] = None
    semana: int = Field(default=1, ge=1, le=52)
    series: int = Field(..., ge=1, le=10)
    repeticoes: str = Field(..., min_length=1, max_length=20)
    carga: Optional[float] = Field(None, ge=0)
    descanso: Optional[str] = None
    observacoes: Optional[str] = None
    ordem: int = Field(default=1, ge=1)

class WorkoutExerciseCreate(WorkoutExerciseBase):
    """Schema para criação de exercício do treino"""
    treino_id: int

class WorkoutExerciseUpdate(BaseModel):
    """Schema para atualização de exercício do treino"""
    nome_exercicio: Optional[str] = Field(None, min_length=1, max_length=100)
    grupo_muscular: Optional[str] = None
    video_url: Optional[str] = None
    imagem_url: Optional[str] = None
    semana: Optional[int] = Field(None, ge=1, le=52)
    series: Optional[int] = Field(None, ge=1, le=10)
    repeticoes: Optional[str] = Field(None, min_length=1, max_length=20)
    carga: Optional[float] = Field(None, ge=0)
    descanso: Optional[str] = None
    observacoes: Optional[str] = None
    ordem: Optional[int] = Field(None, ge=1)

class WorkoutExercise(WorkoutExerciseBase):
    """Schema para resposta de exercício do treino"""
    id: int
    treino_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class WorkoutBase(BaseModel):
    """Schema base para treino"""
    nome: str = Field(..., min_length=1, max_length=100)
    descricao: Optional[str] = None
    data_inicio: datetime
    data_fim: Optional[datetime] = None
    ativo: bool = True
    observacoes: Optional[str] = None

class WorkoutCreate(WorkoutBase):
    """Schema para criação de treino"""
    paciente_id: int
    personal_id: int

class WorkoutUpdate(BaseModel):
    """Schema para atualização de treino"""
    nome: Optional[str] = Field(None, min_length=1, max_length=100)
    descricao: Optional[str] = None
    data_inicio: Optional[datetime] = None
    data_fim: Optional[datetime] = None
    ativo: Optional[bool] = None
    observacoes: Optional[str] = None

class Workout(WorkoutBase):
    """Schema para resposta de treino"""
    id: int
    paciente_id: int
    personal_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class WorkoutWithExercises(Workout):
    """Schema para treino com exercícios"""
    exercicios: List[WorkoutExercise] = []

class WorkoutTemplate(BaseModel):
    """Schema para template de treino"""
    nome: str
    descricao: Optional[str] = None
    categoria: str  # hipertrofia, emagrecimento, condicionamento, etc.
    nivel: str  # iniciante, intermediario, avancado
    exercicios: List[WorkoutExerciseBase]

class WorkoutProgress(BaseModel):
    """Schema para progresso do treino"""
    treino_id: int
    semana: int
    exercicio_id: int
    carga_utilizada: Optional[float] = None
    repeticoes_realizadas: Optional[str] = None
    observacoes: Optional[str] = None
    data_execucao: datetime
