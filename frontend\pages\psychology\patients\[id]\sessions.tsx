import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  ArrowLeft,
  Plus,
  Brain,
  Calendar,
  Clock,
  FileText,
  Eye,
  Edit,
  Trash2,
  MessageCircle
} from 'lucide-react'

interface Session {
  id: number
  data_sessao: string
  duracao: number
  tipo_sessao: string
  status: string
  observacoes: string
  objetivos: string
  progresso: string
  proxima_sessao?: string
  created_at: string
}

interface Patient {
  id: number
  nome: string
  email: string
}

export default function PatientSessions() {
  const router = useRouter()
  const { id } = router.query
  const [patient, setPatient] = useState<Patient | null>(null)
  const [sessions, setSessions] = useState<Session[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (id) {
      fetchData()
    }
  }, [id])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Mock data - em produção viria da API
      setPatient({
        id: Number(id),
        nome: 'Ana Costa',
        email: '<EMAIL>'
      })

      setSessions([
        {
          id: 1,
          data_sessao: '2024-01-20',
          duracao: 50,
          tipo_sessao: 'Terapia Individual',
          status: 'Concluída',
          observacoes: 'Paciente demonstrou melhora significativa no controle da ansiedade. Relatou menos episódios de pânico durante a semana.',
          objetivos: 'Trabalhar técnicas de respiração e mindfulness',
          progresso: 'Excelente progresso nas técnicas de relaxamento',
          proxima_sessao: '2024-01-27',
          created_at: '2024-01-20'
        },
        {
          id: 2,
          data_sessao: '2024-01-13',
          duracao: 50,
          tipo_sessao: 'Terapia Individual',
          status: 'Concluída',
          observacoes: 'Sessão focada em identificar gatilhos de ansiedade. Paciente conseguiu identificar padrões de pensamento negativos.',
          objetivos: 'Identificar gatilhos e padrões de pensamento',
          progresso: 'Boa evolução na autoconscientização',
          proxima_sessao: '2024-01-20',
          created_at: '2024-01-13'
        },
        {
          id: 3,
          data_sessao: '2024-01-06',
          duracao: 50,
          tipo_sessao: 'Avaliação Inicial',
          status: 'Concluída',
          observacoes: 'Primeira sessão. Anamnese completa realizada. Paciente apresenta sintomas de ansiedade generalizada.',
          objetivos: 'Estabelecer rapport e compreender demandas',
          progresso: 'Sessão inicial - estabelecimento de vínculo terapêutico',
          created_at: '2024-01-06'
        }
      ])
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR')
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'concluída':
        return 'bg-green-100 text-green-800'
      case 'agendada':
        return 'bg-blue-100 text-blue-800'
      case 'cancelada':
        return 'bg-red-100 text-red-800'
      case 'reagendada':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getSessionTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'avaliação inicial':
        return 'bg-purple-100 text-purple-800'
      case 'terapia individual':
        return 'bg-blue-100 text-blue-800'
      case 'terapia de grupo':
        return 'bg-green-100 text-green-800'
      case 'follow-up':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Sessões - {patient?.nome} | Hypatium</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link 
                  href={`/psychology/patients/${id}`}
                  className="mr-4 p-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    <Brain className="h-6 w-6 text-purple-600 mr-2" />
                    Sessões - {patient?.nome}
                  </h1>
                  <p className="text-gray-600">Histórico de sessões psicológicas</p>
                </div>
              </div>
              <Link
                href={`/psychology/sessions/new?patient=${id}`}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Nova Sessão
              </Link>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {sessions.length === 0 ? (
              <div className="bg-white shadow rounded-lg p-8 text-center">
                <Brain className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma sessão registrada</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Comece registrando a primeira sessão para este paciente.
                </p>
                <div className="mt-6">
                  <Link
                    href={`/psychology/sessions/new?patient=${id}`}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Primeira Sessão
                  </Link>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Sessions Summary */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Resumo do Tratamento</h3>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-purple-600">{sessions.length}</p>
                      <p className="text-sm text-gray-500">Total de Sessões</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">
                        {sessions.filter(s => s.status === 'Concluída').length}
                      </p>
                      <p className="text-sm text-gray-500">Concluídas</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-blue-600">
                        {sessions.reduce((total, session) => total + session.duracao, 0)}
                      </p>
                      <p className="text-sm text-gray-500">Minutos Totais</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-orange-600">
                        {sessions.filter(s => s.proxima_sessao).length}
                      </p>
                      <p className="text-sm text-gray-500">Próximas Agendadas</p>
                    </div>
                  </div>
                </div>

                {/* Sessions List */}
                <div className="space-y-4">
                  {sessions.map((session, index) => (
                    <div key={session.id} className="bg-white shadow rounded-lg p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex items-center space-x-3">
                          <h4 className="text-lg font-medium text-gray-900">
                            Sessão #{sessions.length - index}
                          </h4>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSessionTypeColor(session.tipo_sessao)}`}>
                            {session.tipo_sessao}
                          </span>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(session.status)}`}>
                            {session.status}
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          <Link
                            href={`/psychology/sessions/${session.id}`}
                            className="text-purple-600 hover:text-purple-900"
                            title="Visualizar"
                          >
                            <Eye className="h-4 w-4" />
                          </Link>
                          <Link
                            href={`/psychology/sessions/${session.id}/edit`}
                            className="text-blue-600 hover:text-blue-900"
                            title="Editar"
                          >
                            <Edit className="h-4 w-4" />
                          </Link>
                          <button
                            className="text-red-600 hover:text-red-900"
                            title="Excluir"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <Calendar className="h-4 w-4 mr-2" />
                          {formatDate(session.data_sessao)}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <Clock className="h-4 w-4 mr-2" />
                          {session.duracao} minutos
                        </div>
                        {session.proxima_sessao && (
                          <div className="flex items-center text-sm text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            Próxima: {formatDate(session.proxima_sessao)}
                          </div>
                        )}
                      </div>

                      <div className="space-y-3">
                        <div>
                          <h5 className="text-sm font-medium text-gray-900 mb-1">Objetivos da Sessão</h5>
                          <p className="text-sm text-gray-600">{session.objetivos}</p>
                        </div>
                        
                        <div>
                          <h5 className="text-sm font-medium text-gray-900 mb-1">Observações</h5>
                          <p className="text-sm text-gray-600">{session.observacoes}</p>
                        </div>
                        
                        <div>
                          <h5 className="text-sm font-medium text-gray-900 mb-1">Progresso</h5>
                          <p className="text-sm text-gray-600">{session.progresso}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}
