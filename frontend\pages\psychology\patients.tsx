import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { Plus, Search, Filter, Users, Brain } from 'lucide-react'
import Layout from '../../components/Layout'

interface Patient {
  id: number
  nome: string
  email: string
  telefone: string
  data_nascimento: string
  motivo_consulta: string
  status: string
  ultima_sessao: string
  created_at: string
}

export default function PsychologyPatients() {
  const router = useRouter()
  const [patients, setPatients] = useState<Patient[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    // Verificar autenticação
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    // Carregar pacientes (mock data por enquanto)
    setPatients([
      {
        id: 1,
        nome: '<PERSON> Costa',
        email: '<EMAIL>',
        telefone: '(11) 99999-9999',
        data_nascimento: '1988-03-20',
        motivo_consulta: 'Ansiedade',
        status: 'Em tratamento',
        ultima_sessao: '2024-01-20',
        created_at: '2024-01-10'
      },
      {
        id: 2,
        nome: 'Carlos Oliveira',
        email: '<EMAIL>',
        telefone: '(11) 88888-8888',
        data_nascimento: '1975-11-15',
        motivo_consulta: 'Depressão',
        status: 'Em tratamento',
        ultima_sessao: '2024-01-18',
        created_at: '2023-12-15'
      }
    ])
    setLoading(false)
  }, [router])

  const filteredPatients = patients.filter(patient =>
    patient.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.motivo_consulta.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <Layout title="Pacientes - Psicologia">
      <Head>
        <title>Pacientes - Psicologia | Hypatium</title>
      </Head>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Page Header */}
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Brain className="h-6 w-6 text-purple-600 mr-2" />
                  Pacientes - Psicologia
                </h1>
                <p className="text-gray-600">Gerencie seus pacientes e acompanhamento psicológico</p>
              </div>
              <Link
                href="/psychology/patients/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Novo Paciente
              </Link>
            </div>

            {/* Search and Filters */}
            <div className="bg-white shadow rounded-lg mb-6">
              <div className="p-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Buscar pacientes..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      />
                    </div>
                  </div>
                  <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <Filter className="h-4 w-4 mr-2" />
                    Filtros
                  </button>
                </div>
              </div>
            </div>

            {/* Patients List */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                {loading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                    <p className="mt-2 text-gray-600">Carregando pacientes...</p>
                  </div>
                ) : filteredPatients.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum paciente encontrado</h3>
                    <p className="text-gray-600 mb-4">
                      {searchTerm ? 'Tente ajustar sua busca' : 'Comece adicionando seu primeiro paciente'}
                    </p>
                    <Link
                      href="/psychology/patients/new"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Adicionar Paciente
                    </Link>
                  </div>
                ) : (
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Paciente
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Contato
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Motivo da Consulta
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Última Sessão
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Ações
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredPatients.map((patient) => (
                          <tr key={patient.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{patient.nome}</div>
                                <div className="text-sm text-gray-500">{patient.email}</div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {patient.telefone}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {patient.motivo_consulta}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                {patient.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {new Date(patient.ultima_sessao).toLocaleDateString('pt-BR')}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <Link
                                href={`/psychology/patients/${patient.id}`}
                                className="text-purple-600 hover:text-purple-900 mr-4"
                              >
                                Ver
                              </Link>
                              <Link
                                href={`/psychology/patients/${patient.id}/edit`}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                Editar
                              </Link>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </div>
      </div>
    </Layout>
  )
}
