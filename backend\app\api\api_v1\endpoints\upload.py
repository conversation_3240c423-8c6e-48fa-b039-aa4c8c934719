"""
Endpoints para upload de arquivos
"""

from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.models.models import User
from app.services.upload_service import UploadService

router = APIRouter()

@router.post("/", response_model=Dict[str, Any])
async def upload_file(
    *,
    file: UploadFile = File(...),
    subfolder: Optional[str] = Query(None),
    custom_filename: Optional[str] = Query(None),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Upload de arquivo
    """
    upload_service = UploadService()
    
    result = await upload_service.upload_file(
        file=file,
        subfolder=subfolder,
        custom_filename=custom_filename
    )
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result.get('errors', ['Erro no upload']))
    
    return result

@router.post("/audio", response_model=Dict[str, Any])
async def upload_audio(
    *,
    file: UploadFile = File(...),
    session_id: Optional[int] = Query(None),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Upload de arquivo de áudio para sessões psicológicas
    """
    # Verificar permissões
    if current_user.tipo not in ["psicologo", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas psicólogos podem fazer upload de áudio")
    
    upload_service = UploadService()
    
    # Validar se é arquivo de áudio
    if not file.filename or not any(file.filename.lower().endswith(ext) for ext in ['.mp3', '.wav', '.m4a', '.aac', '.ogg']):
        raise HTTPException(status_code=400, detail="Apenas arquivos de áudio são permitidos")
    
    # Definir subfolder baseado no usuário e sessão
    subfolder = f"user_{current_user.id}"
    if session_id:
        subfolder += f"/session_{session_id}"
    
    result = await upload_service.upload_file(
        file=file,
        subfolder=subfolder
    )
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result.get('errors', ['Erro no upload de áudio']))
    
    # TODO: Aqui poderia integrar com serviço de transcrição automática
    # como Google Speech-to-Text ou OpenAI Whisper
    
    return result

@router.post("/video", response_model=Dict[str, Any])
async def upload_video(
    *,
    file: UploadFile = File(...),
    exercise_name: Optional[str] = Query(None),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Upload de vídeo para exercícios
    """
    # Verificar permissões
    if current_user.tipo not in ["personal", "admin"]:
        raise HTTPException(status_code=403, detail="Apenas personal trainers podem fazer upload de vídeos")
    
    upload_service = UploadService()
    
    # Validar se é arquivo de vídeo
    if not file.filename or not any(file.filename.lower().endswith(ext) for ext in ['.mp4', '.avi', '.mov', '.wmv']):
        raise HTTPException(status_code=400, detail="Apenas arquivos de vídeo são permitidos")
    
    # Definir subfolder para exercícios
    subfolder = f"exercises/user_{current_user.id}"
    
    # Nome customizado baseado no exercício
    custom_filename = None
    if exercise_name:
        # Limpar nome do exercício para usar como filename
        clean_name = "".join(c for c in exercise_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        clean_name = clean_name.replace(' ', '_').lower()
        extension = file.filename.split('.')[-1] if '.' in file.filename else 'mp4'
        custom_filename = f"{clean_name}.{extension}"
    
    result = await upload_service.upload_file(
        file=file,
        subfolder=subfolder,
        custom_filename=custom_filename
    )
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result.get('errors', ['Erro no upload de vídeo']))
    
    return result

@router.post("/image", response_model=Dict[str, Any])
async def upload_image(
    *,
    file: UploadFile = File(...),
    category: Optional[str] = Query(None),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Upload de imagem
    """
    upload_service = UploadService()
    
    # Validar se é arquivo de imagem
    if not file.filename or not any(file.filename.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
        raise HTTPException(status_code=400, detail="Apenas arquivos de imagem são permitidos")
    
    # Definir subfolder baseado na categoria
    subfolder = f"user_{current_user.id}"
    if category:
        subfolder += f"/{category}"
    
    result = await upload_service.upload_file(
        file=file,
        subfolder=subfolder
    )
    
    if not result['success']:
        raise HTTPException(status_code=400, detail=result.get('errors', ['Erro no upload de imagem']))
    
    return result

@router.get("/list", response_model=List[Dict[str, Any]])
def list_files(
    *,
    file_type: Optional[str] = Query(None),
    subfolder: Optional[str] = Query(None),
    limit: int = Query(100, ge=1, le=500),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Listar arquivos do usuário
    """
    upload_service = UploadService()
    
    # Filtrar por pasta do usuário se não for admin
    if current_user.tipo != "admin":
        user_subfolder = f"user_{current_user.id}"
        if subfolder:
            subfolder = f"{user_subfolder}/{subfolder}"
        else:
            subfolder = user_subfolder
    
    files = upload_service.list_files(
        file_type=file_type,
        subfolder=subfolder,
        limit=limit
    )
    
    return files

@router.delete("/{file_path:path}")
def delete_file(
    *,
    file_path: str,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Deletar arquivo
    """
    upload_service = UploadService()
    
    # Verificar se o usuário tem permissão para deletar o arquivo
    if current_user.tipo != "admin":
        if f"user_{current_user.id}" not in file_path:
            raise HTTPException(status_code=403, detail="Sem permissão para deletar este arquivo")
    
    success = upload_service.delete_file(file_path)
    
    if not success:
        raise HTTPException(status_code=404, detail="Arquivo não encontrado")
    
    return {"message": "Arquivo deletado com sucesso"}

@router.get("/stats", response_model=Dict[str, Any])
def get_upload_stats(
    *,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Obter estatísticas de upload
    """
    upload_service = UploadService()
    
    if current_user.tipo == "admin":
        # Admin vê estatísticas globais
        stats = upload_service.get_upload_stats()
    else:
        # Usuários veem apenas suas estatísticas
        user_files = upload_service.list_files(subfolder=f"user_{current_user.id}", limit=1000)
        
        stats = {
            'total_files': len(user_files),
            'total_size': sum(f['file_info'].get('size', 0) for f in user_files),
            'by_type': {}
        }
        
        # Agrupar por tipo
        for file_data in user_files:
            file_type = file_data['file_type']
            if file_type not in stats['by_type']:
                stats['by_type'][file_type] = {'count': 0, 'size': 0}
            
            stats['by_type'][file_type]['count'] += 1
            stats['by_type'][file_type]['size'] += file_data['file_info'].get('size', 0)
        
        # Converter para MB
        stats['total_size_mb'] = round(stats['total_size'] / (1024 * 1024), 2)
        for type_stats in stats['by_type'].values():
            type_stats['size_mb'] = round(type_stats['size'] / (1024 * 1024), 2)
    
    return stats

@router.post("/cleanup")
def cleanup_old_files(
    *,
    days_old: int = Query(30, ge=1, le=365),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Limpar arquivos antigos (apenas admin)
    """
    if current_user.tipo != "admin":
        raise HTTPException(status_code=403, detail="Apenas administradores podem executar limpeza")
    
    upload_service = UploadService()
    result = upload_service.cleanup_old_files(days_old=days_old)
    
    return result
