import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  TrendingUp, 
  Calendar,
  Download,
  Filter,
  BarChart3,
  Activity
} from 'lucide-react'
import ProgressChart from '../../../../components/ProgressChart'

interface Patient {
  id: number
  nome: string
}

interface ProgressData {
  data: string
  peso: number
  imc?: number
  gordura_percentual?: number
  massa_gorda?: number
  massa_magra?: number
}

export default function PatientProgressPage() {
  const router = useRouter()
  const { id } = router.query
  const [patient, setPatient] = useState<Patient | null>(null)
  const [progressData, setProgressData] = useState<ProgressData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['peso', 'imc'])
  const [timeRange, setTimeRange] = useState('6months')

  useEffect(() => {
    if (id) {
      fetchData()
    }
  }, [id, timeRange])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Buscar dados do paciente
      const patientResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/patients/${id}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (patientResponse.ok) {
        const patientData = await patientResponse.json()
        setPatient(patientData)
      }

      // Buscar dados de progresso
      const progressResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/assessments/patient/${id}/progress?limit=20`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (progressResponse.ok) {
        const progressData = await progressResponse.json()
        setProgressData(progressData)
      }
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleMetricToggle = (metric: string) => {
    setSelectedMetrics(prev => 
      prev.includes(metric) 
        ? prev.filter(m => m !== metric)
        : [...prev, metric]
    )
  }

  const getFilteredData = () => {
    if (timeRange === 'all') return progressData

    const now = new Date()
    const cutoffDate = new Date()
    
    switch (timeRange) {
      case '1month':
        cutoffDate.setMonth(now.getMonth() - 1)
        break
      case '3months':
        cutoffDate.setMonth(now.getMonth() - 3)
        break
      case '6months':
        cutoffDate.setMonth(now.getMonth() - 6)
        break
      case '1year':
        cutoffDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        return progressData
    }

    return progressData.filter(item => new Date(item.data) >= cutoffDate)
  }

  const getProgressSummary = () => {
    const data = getFilteredData()
    if (data.length < 2) return null

    const first = data[data.length - 1]
    const last = data[0]

    return {
      peso: {
        change: last.peso - first.peso,
        percentage: ((last.peso - first.peso) / first.peso) * 100
      },
      imc: last.imc && first.imc ? {
        change: last.imc - first.imc,
        percentage: ((last.imc - first.imc) / first.imc) * 100
      } : null,
      gordura_percentual: last.gordura_percentual && first.gordura_percentual ? {
        change: last.gordura_percentual - first.gordura_percentual,
        percentage: ((last.gordura_percentual - first.gordura_percentual) / first.gordura_percentual) * 100
      } : null
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  if (!patient) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Paciente não encontrado</h2>
          <Link href="/personal/patients" className="text-blue-600 hover:text-blue-800 mt-4 inline-block">
            ← Voltar para lista de pacientes
          </Link>
        </div>
      </div>
    )
  }

  const filteredData = getFilteredData()
  const summary = getProgressSummary()

  return (
    <>
      <Head>
        <title>Progresso - {patient.nome} - Hypatium</title>
        <meta name="description" content={`Progresso do paciente ${patient.nome}`} />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href={`/personal/patients/${id}`} className="text-blue-600 hover:text-blue-800">
                  ← Voltar para {patient.nome}
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <TrendingUp className="h-6 w-6 mr-2" />
                Progresso - {patient.nome}
              </h1>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Controls */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                {/* Time Range */}
                <div className="flex items-center space-x-4">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    Período:
                  </label>
                  <select
                    value={timeRange}
                    onChange={(e) => setTimeRange(e.target.value)}
                    className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="1month">Último mês</option>
                    <option value="3months">Últimos 3 meses</option>
                    <option value="6months">Últimos 6 meses</option>
                    <option value="1year">Último ano</option>
                    <option value="all">Todos os dados</option>
                  </select>
                </div>

                {/* Metric Selection */}
                <div className="flex items-center space-x-4">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Métricas:
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {[
                      { key: 'peso', label: 'Peso' },
                      { key: 'imc', label: 'IMC' },
                      { key: 'gordura_percentual', label: 'Gordura %' },
                      { key: 'massa_gorda', label: 'Massa Gorda' },
                      { key: 'massa_magra', label: 'Massa Magra' }
                    ].map(metric => (
                      <button
                        key={metric.key}
                        onClick={() => handleMetricToggle(metric.key)}
                        className={`px-3 py-1 text-xs rounded-full border ${
                          selectedMetrics.includes(metric.key)
                            ? 'bg-blue-100 border-blue-300 text-blue-700'
                            : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        {metric.label}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Summary Cards */}
            {summary && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="bg-white p-6 rounded-lg shadow">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Activity className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Variação de Peso</p>
                      <p className={`text-2xl font-bold ${summary.peso.change >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {summary.peso.change >= 0 ? '+' : ''}{summary.peso.change.toFixed(1)}kg
                      </p>
                      <p className="text-sm text-gray-500">
                        {summary.peso.percentage >= 0 ? '+' : ''}{summary.peso.percentage.toFixed(1)}%
                      </p>
                    </div>
                  </div>
                </div>

                {summary.imc && (
                  <div className="bg-white p-6 rounded-lg shadow">
                    <div className="flex items-center">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <TrendingUp className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Variação de IMC</p>
                        <p className={`text-2xl font-bold ${summary.imc.change >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                          {summary.imc.change >= 0 ? '+' : ''}{summary.imc.change.toFixed(1)}
                        </p>
                        <p className="text-sm text-gray-500">
                          {summary.imc.percentage >= 0 ? '+' : ''}{summary.imc.percentage.toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {summary.gordura_percentual && (
                  <div className="bg-white p-6 rounded-lg shadow">
                    <div className="flex items-center">
                      <div className="p-2 bg-yellow-100 rounded-lg">
                        <BarChart3 className="h-6 w-6 text-yellow-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Variação de Gordura</p>
                        <p className={`text-2xl font-bold ${summary.gordura_percentual.change >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                          {summary.gordura_percentual.change >= 0 ? '+' : ''}{summary.gordura_percentual.change.toFixed(1)}%
                        </p>
                        <p className="text-sm text-gray-500">
                          {summary.gordura_percentual.percentage >= 0 ? '+' : ''}{summary.gordura_percentual.percentage.toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Progress Chart */}
            <ProgressChart
              data={filteredData}
              metrics={selectedMetrics as any}
              title="Evolução das Métricas"
              height={400}
            />

            {/* Data Table */}
            <div className="mt-6 bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Histórico de Avaliações</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Data
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Peso (kg)
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        IMC
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Gordura (%)
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Massa Magra (kg)
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredData.map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(item.data).toLocaleDateString('pt-BR')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.peso.toFixed(1)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.imc?.toFixed(1) || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.gordura_percentual?.toFixed(1) || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.massa_magra?.toFixed(1) || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
