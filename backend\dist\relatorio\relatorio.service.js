"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RelatorioService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const config_1 = require("@nestjs/config");
let RelatorioService = class RelatorioService {
    prisma;
    configService;
    openai;
    constructor(prisma, configService) {
        this.prisma = prisma;
        this.configService = configService;
        const apiKey = this.configService.get('OPENAI_API_KEY');
        if (apiKey) {
            this.openai = null;
        }
    }
    async gerarRelatorio(avaliacaoComposicaoId, personalId) {
        const avaliacao = await this.prisma.avaliacaoComposicao.findUnique({
            where: { id: avaliacaoComposicaoId },
            include: {
                aluno: {
                    select: {
                        nome: true,
                        idade: true,
                        pesoAtual: true,
                        estatura: true,
                        objetivos: true,
                        habitos: true,
                    },
                },
                avaliacaoEnergetica: true,
                volumeLoads: {
                    orderBy: [
                        { exercicio: 'asc' },
                        { semana: 'asc' },
                    ],
                },
            },
        });
        if (!avaliacao) {
            throw new common_1.NotFoundException('Avaliação não encontrada');
        }
        if (avaliacao.personalId !== personalId) {
            throw new common_1.ForbiddenException('Acesso negado a esta avaliação');
        }
        const avaliacoesAnteriores = await this.prisma.avaliacaoComposicao.findMany({
            where: {
                alunoId: avaliacao.alunoId,
                dataAvaliacao: {
                    lt: avaliacao.dataAvaliacao,
                },
            },
            orderBy: {
                dataAvaliacao: 'desc',
            },
            take: 3,
            include: {
                avaliacaoEnergetica: true,
            },
        });
        let textoAnalise = '';
        if (this.openai) {
            try {
                textoAnalise = await this.gerarAnaliseIA(avaliacao, avaliacoesAnteriores);
            }
            catch (error) {
                console.error('Erro ao gerar análise com IA:', error);
                textoAnalise = this.gerarAnaliseBasica(avaliacao, avaliacoesAnteriores);
            }
        }
        else {
            textoAnalise = this.gerarAnaliseBasica(avaliacao, avaliacoesAnteriores);
        }
        const graficosEvolucao = this.gerarDadosGraficos(avaliacao, avaliacoesAnteriores);
        const relatorio = await this.prisma.relatorio.create({
            data: {
                avaliacaoComposicaoId,
                textoAnalise,
                graficosEvolucao: JSON.stringify(graficosEvolucao),
            },
        });
        return {
            ...relatorio,
            graficosEvolucao,
            avaliacao,
        };
    }
    async gerarAnaliseIA(avaliacao, avaliacoesAnteriores) {
        const prompt = this.construirPromptAnalise(avaliacao, avaliacoesAnteriores);
        const completion = await this.openai.chat.completions.create({
            model: 'gpt-4',
            messages: [
                {
                    role: 'system',
                    content: `Você é um especialista em educação física e nutrição. Analise os dados de avaliação física fornecidos e gere um relatório profissional, detalhado e personalizado.

          Estruture sua resposta em:
          1. RESUMO EXECUTIVO
          2. ANÁLISE DA COMPOSIÇÃO CORPORAL
          3. ANÁLISE ENERGÉTICA
          4. EVOLUÇÃO (se houver dados anteriores)
          5. RECOMENDAÇÕES ESPECÍFICAS
          6. METAS SUGERIDAS

          Use linguagem técnica mas acessível. Seja específico com os números e percentuais.`,
                },
                {
                    role: 'user',
                    content: prompt,
                },
            ],
            max_tokens: 2000,
            temperature: 0.7,
        });
        return completion.choices[0]?.message?.content || 'Erro ao gerar análise';
    }
    construirPromptAnalise(avaliacao, avaliacoesAnteriores) {
        const aluno = avaliacao.aluno;
        const energetica = avaliacao.avaliacaoEnergetica;
        let prompt = `DADOS DO ALUNO:
Nome: ${aluno.nome}
Idade: ${aluno.idade} anos
Peso: ${aluno.pesoAtual} kg
Estatura: ${aluno.estatura} cm
Objetivos: ${aluno.objetivos || 'Não informado'}
Hábitos: ${aluno.habitos || 'Não informado'}

AVALIAÇÃO ATUAL (${new Date(avaliacao.dataAvaliacao).toLocaleDateString('pt-BR')}):

COMPOSIÇÃO CORPORAL:
- IMC: ${avaliacao.valorIMC} (${avaliacao.categoriaIMC})
- Soma de dobras: ${avaliacao.somaDobras} mm

PERCENTUAL DE GORDURA:
- Método Guedes: ${avaliacao.guedesPercentual}% (Densidade: ${avaliacao.guedesDensidade})
- Método Pollock 3: ${avaliacao.pollock3Percentual}% (Densidade: ${avaliacao.pollock3Densidade})
- Método Pollock 7: ${avaliacao.pollock7Percentual}% (Densidade: ${avaliacao.pollock7Densidade})

MASSA CORPORAL:
- Massa Gorda (Guedes): ${avaliacao.guedesMassaGordura} kg
- Massa Magra (Guedes): ${avaliacao.guedesMassaMagro} kg`;
        if (energetica) {
            prompt += `

AVALIAÇÃO ENERGÉTICA:
- TMB FAO/OMS: ${energetica.tmbFAO_OMS} kcal
- TMB Harris-Benedict: ${energetica.tmbHarrisBenedict} kcal
- TMB Mifflin: ${energetica.tmbMifflin} kcal
- TMB Cunningham: ${energetica.tmbCunningham} kcal
- GET: ${energetica.valorGET} kcal (Fator: ${energetica.fatorAtividade})
- Consumo Recomendado: ${energetica.valorConsumo} kcal (${energetica.percentualDeficitSuperavit > 0 ? '+' : ''}${energetica.percentualDeficitSuperavit}%)`;
        }
        if (avaliacoesAnteriores.length > 0) {
            prompt += `\n\nEVOLUÇÃO (comparação com avaliações anteriores):`;
            avaliacoesAnteriores.forEach((ant, index) => {
                const diasDiferenca = Math.floor((new Date(avaliacao.dataAvaliacao).getTime() - new Date(ant.dataAvaliacao).getTime()) / (1000 * 60 * 60 * 24));
                prompt += `\n\nAvaliação ${index + 1} (${diasDiferenca} dias atrás):
- IMC: ${ant.valorIMC} → ${avaliacao.valorIMC} (${avaliacao.valorIMC - ant.valorIMC > 0 ? '+' : ''}${(avaliacao.valorIMC - ant.valorIMC).toFixed(2)})
- % Gordura Guedes: ${ant.guedesPercentual}% → ${avaliacao.guedesPercentual}% (${avaliacao.guedesPercentual - ant.guedesPercentual > 0 ? '+' : ''}${(avaliacao.guedesPercentual - ant.guedesPercentual).toFixed(2)}%)
- Massa Magra: ${ant.guedesMassaMagro}kg → ${avaliacao.guedesMassaMagro}kg (${avaliacao.guedesMassaMagro - ant.guedesMassaMagro > 0 ? '+' : ''}${(avaliacao.guedesMassaMagro - ant.guedesMassaMagro).toFixed(2)}kg)`;
            });
        }
        return prompt;
    }
    gerarAnaliseBasica(avaliacao, avaliacoesAnteriores) {
        const aluno = avaliacao.aluno;
        let analise = `RELATÓRIO DE AVALIAÇÃO FÍSICA - ${aluno.nome}\n\n`;
        analise += `RESUMO EXECUTIVO:\n`;
        analise += `Aluno de ${aluno.idade} anos, ${aluno.pesoAtual}kg, ${aluno.estatura}cm.\n`;
        analise += `IMC atual: ${avaliacao.valorIMC} (${avaliacao.categoriaIMC})\n`;
        analise += `Percentual de gordura (Guedes): ${avaliacao.guedesPercentual}%\n\n`;
        analise += `ANÁLISE DA COMPOSIÇÃO CORPORAL:\n`;
        analise += `- Soma das dobras cutâneas: ${avaliacao.somaDobras}mm\n`;
        analise += `- Massa gorda: ${avaliacao.guedesMassaGordura}kg\n`;
        analise += `- Massa magra: ${avaliacao.guedesMassaMagro}kg\n\n`;
        if (avaliacao.avaliacaoEnergetica) {
            const energetica = avaliacao.avaliacaoEnergetica;
            analise += `ANÁLISE ENERGÉTICA:\n`;
            analise += `- Taxa Metabólica Basal (Mifflin): ${energetica.tmbMifflin} kcal/dia\n`;
            analise += `- Gasto Energético Total: ${energetica.valorGET} kcal/dia\n`;
            analise += `- Consumo calórico recomendado: ${energetica.valorConsumo} kcal/dia\n\n`;
        }
        if (avaliacoesAnteriores.length > 0) {
            const anterior = avaliacoesAnteriores[0];
            const diasDiferenca = Math.floor((new Date(avaliacao.dataAvaliacao).getTime() - new Date(anterior.dataAvaliacao).getTime()) / (1000 * 60 * 60 * 24));
            analise += `EVOLUÇÃO (${diasDiferenca} dias):\n`;
            analise += `- IMC: ${(avaliacao.valorIMC - anterior.valorIMC).toFixed(2)} pontos\n`;
            analise += `- % Gordura: ${(avaliacao.guedesPercentual - anterior.guedesPercentual).toFixed(2)}%\n`;
            analise += `- Massa Magra: ${(avaliacao.guedesMassaMagro - anterior.guedesMassaMagro).toFixed(2)}kg\n\n`;
        }
        analise += `RECOMENDAÇÕES:\n`;
        analise += `- Manter acompanhamento regular das medidas\n`;
        analise += `- Ajustar treino conforme evolução\n`;
        analise += `- Monitorar ingestão calórica\n`;
        return analise;
    }
    gerarDadosGraficos(avaliacao, avaliacoesAnteriores) {
        const todasAvaliacoes = [...avaliacoesAnteriores.reverse(), avaliacao];
        return {
            evolucaoIMC: todasAvaliacoes.map(av => ({
                data: new Date(av.dataAvaliacao).toLocaleDateString('pt-BR'),
                valor: av.valorIMC,
            })),
            evolucaoGordura: todasAvaliacoes.map(av => ({
                data: new Date(av.dataAvaliacao).toLocaleDateString('pt-BR'),
                guedes: av.guedesPercentual,
                pollock3: av.pollock3Percentual,
                pollock7: av.pollock7Percentual,
            })),
            evolucaoMassa: todasAvaliacoes.map(av => ({
                data: new Date(av.dataAvaliacao).toLocaleDateString('pt-BR'),
                massaGorda: av.guedesMassaGordura,
                massaMagra: av.guedesMassaMagro,
            })),
            evolucaoEnergetica: todasAvaliacoes
                .filter(av => av.avaliacaoEnergetica)
                .map(av => ({
                data: new Date(av.dataAvaliacao).toLocaleDateString('pt-BR'),
                tmb: av.avaliacaoEnergetica.tmbMifflin,
                get: av.avaliacaoEnergetica.valorGET,
                consumo: av.avaliacaoEnergetica.valorConsumo,
            })),
        };
    }
    async findRelatoriosByAluno(alunoId, personalId) {
        if (personalId) {
            const aluno = await this.prisma.aluno.findUnique({
                where: { id: alunoId },
            });
            if (!aluno || aluno.personalId !== personalId) {
                throw new common_1.ForbiddenException('Acesso negado a este aluno');
            }
        }
        return this.prisma.relatorio.findMany({
            where: {
                avaliacaoComposicao: {
                    alunoId,
                },
            },
            include: {
                avaliacaoComposicao: {
                    select: {
                        dataAvaliacao: true,
                        valorIMC: true,
                        categoriaIMC: true,
                        guedesPercentual: true,
                        aluno: {
                            select: {
                                nome: true,
                            },
                        },
                    },
                },
            },
            orderBy: {
                dataGeracao: 'desc',
            },
        });
    }
    async findRelatorioById(id, personalId) {
        const relatorio = await this.prisma.relatorio.findUnique({
            where: { id },
            include: {
                avaliacaoComposicao: {
                    include: {
                        aluno: {
                            select: {
                                nome: true,
                                idade: true,
                                pesoAtual: true,
                                estatura: true,
                            },
                        },
                        avaliacaoEnergetica: true,
                    },
                },
            },
        });
        if (!relatorio) {
            throw new common_1.NotFoundException('Relatório não encontrado');
        }
        if (personalId && relatorio.avaliacaoComposicao.personalId !== personalId) {
            throw new common_1.ForbiddenException('Acesso negado a este relatório');
        }
        return {
            ...relatorio,
            graficosEvolucao: relatorio.graficosEvolucao ? JSON.parse(relatorio.graficosEvolucao) : null,
        };
    }
    async gerarRelatorioVolumeLoad(avaliacaoComposicaoId, personalId) {
        const avaliacao = await this.prisma.avaliacaoComposicao.findUnique({
            where: { id: avaliacaoComposicaoId },
            include: {
                aluno: {
                    select: {
                        nome: true,
                        idade: true,
                    },
                },
                volumeLoads: {
                    orderBy: [
                        { exercicio: 'asc' },
                        { semana: 'asc' },
                    ],
                },
            },
        });
        if (!avaliacao) {
            throw new common_1.NotFoundException('Avaliação não encontrada');
        }
        if (avaliacao.personalId !== personalId) {
            throw new common_1.ForbiddenException('Acesso negado a esta avaliação');
        }
        const dadosVolumeLoad = this.processarDadosVolumeLoad(avaliacao.volumeLoads);
        let analiseVL = '';
        if (this.openai) {
            try {
                analiseVL = await this.gerarAnaliseVolumeLoadIA(avaliacao, dadosVolumeLoad);
            }
            catch (error) {
                console.error('Erro ao gerar análise VL com IA:', error);
                analiseVL = this.gerarAnaliseVolumeLoadBasica(avaliacao, dadosVolumeLoad);
            }
        }
        else {
            analiseVL = this.gerarAnaliseVolumeLoadBasica(avaliacao, dadosVolumeLoad);
        }
        return {
            aluno: avaliacao.aluno,
            analise: analiseVL,
            dadosVolumeLoad,
            graficos: this.gerarGraficosVolumeLoad(dadosVolumeLoad),
        };
    }
    processarDadosVolumeLoad(volumeLoads) {
        const porExercicio = volumeLoads.reduce((acc, vl) => {
            if (!acc[vl.exercicio]) {
                acc[vl.exercicio] = {
                    exercicio: vl.exercicio,
                    semanas: [],
                    vlTotal: 0,
                    progressao: [],
                };
            }
            acc[vl.exercicio].semanas.push({
                semana: vl.semana,
                vlExercicio: vl.vlExercicio || 0,
            });
            acc[vl.exercicio].vlTotal += vl.vlExercicio || 0;
            return acc;
        }, {});
        Object.values(porExercicio).forEach((ex) => {
            ex.semanas.sort((a, b) => a.semana - b.semana);
            for (let i = 1; i < ex.semanas.length; i++) {
                const anterior = ex.semanas[i - 1].vlExercicio;
                const atual = ex.semanas[i].vlExercicio;
                const percentual = anterior > 0 ? ((atual - anterior) / anterior) * 100 : 0;
                ex.progressao.push({
                    semana: ex.semanas[i].semana,
                    percentual: parseFloat(percentual.toFixed(2)),
                });
            }
        });
        return Object.values(porExercicio);
    }
    async gerarAnaliseVolumeLoadIA(avaliacao, dadosVL) {
        const prompt = this.construirPromptVolumeLoad(avaliacao, dadosVL);
        const completion = await this.openai.chat.completions.create({
            model: 'gpt-4',
            messages: [
                {
                    role: 'system',
                    content: `Você é um especialista em treinamento de força e periodização. Analise os dados de Volume Load fornecidos e gere um relatório técnico sobre a progressão do treinamento.

          Estruture sua resposta em:
          1. RESUMO DA PERIODIZAÇÃO
          2. ANÁLISE POR EXERCÍCIO
          3. PROGRESSÃO GERAL
          4. PONTOS DE ATENÇÃO
          5. RECOMENDAÇÕES PARA PRÓXIMO CICLO

          Use terminologia técnica de treinamento de força.`,
                },
                {
                    role: 'user',
                    content: prompt,
                },
            ],
            max_tokens: 1500,
            temperature: 0.7,
        });
        return completion.choices[0]?.message?.content || 'Erro ao gerar análise';
    }
    construirPromptVolumeLoad(avaliacao, dadosVL) {
        let prompt = `ANÁLISE DE VOLUME LOAD - ${avaliacao.aluno.nome}\n\n`;
        prompt += `DADOS GERAIS:\n`;
        prompt += `- Total de exercícios: ${dadosVL.length}\n`;
        prompt += `- Volume Load total do mesociclo: ${dadosVL.reduce((total, ex) => total + ex.vlTotal, 0)}\n\n`;
        prompt += `DETALHAMENTO POR EXERCÍCIO:\n`;
        dadosVL.forEach(ex => {
            prompt += `\nExercício ${ex.exercicio}:\n`;
            prompt += `- Volume Load total: ${ex.vlTotal}\n`;
            prompt += `- Semanas registradas: ${ex.semanas.length}\n`;
            prompt += `- Progressão por semana: ${ex.semanas.map(s => `S${s.semana}: ${s.vlExercicio}`).join(', ')}\n`;
            if (ex.progressao.length > 0) {
                const progressaoMedia = ex.progressao.reduce((sum, p) => sum + p.percentual, 0) / ex.progressao.length;
                prompt += `- Progressão média: ${progressaoMedia.toFixed(2)}%\n`;
            }
        });
        return prompt;
    }
    gerarAnaliseVolumeLoadBasica(avaliacao, dadosVL) {
        let analise = `RELATÓRIO DE VOLUME LOAD - ${avaliacao.aluno.nome}\n\n`;
        const vlTotal = dadosVL.reduce((total, ex) => total + ex.vlTotal, 0);
        analise += `RESUMO GERAL:\n`;
        analise += `- Total de exercícios analisados: ${dadosVL.length}\n`;
        analise += `- Volume Load total do mesociclo: ${vlTotal}\n`;
        analise += `- Volume Load médio por exercício: ${(vlTotal / dadosVL.length).toFixed(0)}\n\n`;
        analise += `ANÁLISE POR EXERCÍCIO:\n`;
        dadosVL.forEach(ex => {
            analise += `\nExercício ${ex.exercicio}:\n`;
            analise += `- Volume Load total: ${ex.vlTotal}\n`;
            analise += `- Média por semana: ${(ex.vlTotal / ex.semanas.length).toFixed(0)}\n`;
            if (ex.progressao.length > 0) {
                const progressaoMedia = ex.progressao.reduce((sum, p) => sum + p.percentual, 0) / ex.progressao.length;
                analise += `- Progressão média: ${progressaoMedia.toFixed(2)}%\n`;
            }
        });
        analise += `\nRECOMENDAÇÕES:\n`;
        analise += `- Manter registro consistente do Volume Load\n`;
        analise += `- Buscar progressão gradual entre 2-10% por semana\n`;
        analise += `- Ajustar cargas conforme adaptação\n`;
        return analise;
    }
    gerarGraficosVolumeLoad(dadosVL) {
        return {
            vlPorExercicio: dadosVL.map(ex => ({
                exercicio: `Ex ${ex.exercicio}`,
                vlTotal: ex.vlTotal,
            })),
            progressaoPorSemana: dadosVL.reduce((acc, ex) => {
                ex.semanas.forEach((semana) => {
                    const existing = acc.find((item) => item.semana === semana.semana);
                    if (existing) {
                        existing.vlTotal += semana.vlExercicio;
                    }
                    else {
                        acc.push({
                            semana: `S${semana.semana}`,
                            vlTotal: semana.vlExercicio,
                        });
                    }
                });
                return acc;
            }, []).sort((a, b) => a.semana.localeCompare(b.semana)),
        };
    }
};
exports.RelatorioService = RelatorioService;
exports.RelatorioService = RelatorioService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_1.ConfigService])
], RelatorioService);
//# sourceMappingURL=relatorio.service.js.map