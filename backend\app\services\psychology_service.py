"""
Serviço para gerenciamento de sessões psicológicas
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.models.models import PsychologySession, Patient
from app.schemas.psychology import (
    PsychologySessionCreate, 
    PsychologySessionUpdate,
    AudioTranscription,
    PsychologicalAssessment,
    TherapeuticGoal,
    PsychologyReport
)

class PsychologyService:
    def __init__(self, db: Session):
        self.db = db

    def get_session(self, session_id: int) -> Optional[PsychologySession]:
        """Obter sessão psicológica por ID"""
        return self.db.query(PsychologySession).filter(
            PsychologySession.id == session_id
        ).first()

    def get_sessions_by_patient(self, patient_id: int, skip: int = 0, limit: int = 100) -> List[PsychologySession]:
        """Obter sessões de um paciente"""
        return self.db.query(PsychologySession).filter(
            PsychologySession.paciente_id == patient_id
        ).order_by(PsychologySession.data_sessao.desc()).offset(skip).limit(limit).all()

    def get_sessions_by_psychologist(self, psychologist_id: int, skip: int = 0, limit: int = 100) -> List[PsychologySession]:
        """Obter sessões criadas por um psicólogo"""
        return self.db.query(PsychologySession).filter(
            PsychologySession.psicologo_id == psychologist_id
        ).order_by(PsychologySession.data_sessao.desc()).offset(skip).limit(limit).all()

    def get_recent_sessions(self, psychologist_id: int, days: int = 7) -> List[PsychologySession]:
        """Obter sessões recentes de um psicólogo"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        return self.db.query(PsychologySession).filter(
            PsychologySession.psicologo_id == psychologist_id,
            PsychologySession.data_sessao >= cutoff_date
        ).order_by(PsychologySession.data_sessao.desc()).all()

    def create_session(self, session_in: PsychologySessionCreate) -> PsychologySession:
        """Criar nova sessão psicológica"""
        # Verificar se paciente existe
        patient = self.db.query(Patient).filter(Patient.id == session_in.paciente_id).first()
        if not patient:
            raise ValueError("Paciente não encontrado")

        db_session = PsychologySession(**session_in.dict())
        self.db.add(db_session)
        self.db.commit()
        self.db.refresh(db_session)
        return db_session

    def update_session(self, session_id: int, session_in: PsychologySessionUpdate) -> Optional[PsychologySession]:
        """Atualizar sessão psicológica"""
        db_session = self.get_session(session_id)
        if not db_session:
            return None

        update_data = session_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_session, field, value)

        self.db.commit()
        self.db.refresh(db_session)
        return db_session

    def delete_session(self, session_id: int) -> bool:
        """Deletar sessão psicológica"""
        db_session = self.get_session(session_id)
        if not db_session:
            return False

        self.db.delete(db_session)
        self.db.commit()
        return True

    def add_audio_transcription(self, session_id: int, transcription_text: str, confidence_score: Optional[float] = None) -> Optional[PsychologySession]:
        """Adicionar transcrição de áudio à sessão"""
        db_session = self.get_session(session_id)
        if not db_session:
            return None

        db_session.transcricao_texto = transcription_text
        db_session.transcricao_processada = True
        
        self.db.commit()
        self.db.refresh(db_session)
        return db_session

    def get_patient_progress(self, patient_id: int, limit: int = 10) -> Dict[str, Any]:
        """Obter progresso psicológico do paciente"""
        sessions = self.get_sessions_by_patient(patient_id, limit=limit)
        
        if not sessions:
            return {
                "total_sessions": 0,
                "progress_data": [],
                "average_scores": {},
                "trends": {}
            }

        # Extrair dados de progresso
        progress_data = []
        humor_scores = []
        ansiedade_scores = []
        motivacao_scores = []

        for session in reversed(sessions):  # Ordem cronológica
            data_point = {
                "data": session.data_sessao.isoformat(),
                "humor": session.humor_escala,
                "ansiedade": session.ansiedade_escala,
                "motivacao": session.motivacao_escala,
                "duracao": session.duracao_minutos
            }
            progress_data.append(data_point)
            
            if session.humor_escala:
                humor_scores.append(session.humor_escala)
            if session.ansiedade_escala:
                ansiedade_scores.append(session.ansiedade_escala)
            if session.motivacao_escala:
                motivacao_scores.append(session.motivacao_escala)

        # Calcular médias
        average_scores = {
            "humor": sum(humor_scores) / len(humor_scores) if humor_scores else None,
            "ansiedade": sum(ansiedade_scores) / len(ansiedade_scores) if ansiedade_scores else None,
            "motivacao": sum(motivacao_scores) / len(motivacao_scores) if motivacao_scores else None
        }

        # Calcular tendências (comparar primeira metade com segunda metade)
        trends = {}
        for score_type, scores in [("humor", humor_scores), ("ansiedade", ansiedade_scores), ("motivacao", motivacao_scores)]:
            if len(scores) >= 4:
                mid_point = len(scores) // 2
                first_half_avg = sum(scores[:mid_point]) / mid_point
                second_half_avg = sum(scores[mid_point:]) / (len(scores) - mid_point)
                
                if score_type == "ansiedade":
                    # Para ansiedade, diminuição é melhora
                    trend = "melhorando" if second_half_avg < first_half_avg else "piorando" if second_half_avg > first_half_avg else "estável"
                else:
                    # Para humor e motivação, aumento é melhora
                    trend = "melhorando" if second_half_avg > first_half_avg else "piorando" if second_half_avg < first_half_avg else "estável"
                
                trends[score_type] = {
                    "trend": trend,
                    "change": round(second_half_avg - first_half_avg, 1)
                }

        return {
            "total_sessions": len(sessions),
            "progress_data": progress_data,
            "average_scores": average_scores,
            "trends": trends,
            "last_session_date": sessions[0].data_sessao.isoformat() if sessions else None
        }

    def get_session_statistics(self, psychologist_id: int, period_days: int = 30) -> Dict[str, Any]:
        """Obter estatísticas das sessões do psicólogo"""
        cutoff_date = datetime.utcnow() - timedelta(days=period_days)
        
        sessions = self.db.query(PsychologySession).filter(
            PsychologySession.psicologo_id == psychologist_id,
            PsychologySession.data_sessao >= cutoff_date
        ).all()

        if not sessions:
            return {
                "total_sessions": 0,
                "total_patients": 0,
                "average_duration": 0,
                "sessions_with_audio": 0,
                "transcription_rate": 0
            }

        # Calcular estatísticas
        total_sessions = len(sessions)
        unique_patients = len(set(session.paciente_id for session in sessions))
        
        durations = [session.duracao_minutos for session in sessions if session.duracao_minutos]
        average_duration = sum(durations) / len(durations) if durations else 0
        
        sessions_with_audio = len([s for s in sessions if s.audio_url])
        sessions_with_transcription = len([s for s in sessions if s.transcricao_processada])
        
        transcription_rate = (sessions_with_transcription / sessions_with_audio * 100) if sessions_with_audio > 0 else 0

        return {
            "total_sessions": total_sessions,
            "total_patients": unique_patients,
            "average_duration": round(average_duration, 1),
            "sessions_with_audio": sessions_with_audio,
            "transcription_rate": round(transcription_rate, 1),
            "period_days": period_days
        }

    def search_sessions_by_content(self, psychologist_id: int, query: str, limit: int = 50) -> List[PsychologySession]:
        """Buscar sessões por conteúdo (anotações ou transcrições)"""
        return self.db.query(PsychologySession).filter(
            PsychologySession.psicologo_id == psychologist_id,
            (PsychologySession.anotacoes.ilike(f"%{query}%")) |
            (PsychologySession.transcricao_texto.ilike(f"%{query}%")) |
            (PsychologySession.objetivos_sessao.ilike(f"%{query}%"))
        ).order_by(PsychologySession.data_sessao.desc()).limit(limit).all()

    def get_upcoming_sessions(self, psychologist_id: int, days_ahead: int = 7) -> List[PsychologySession]:
        """Obter próximas sessões agendadas"""
        start_date = datetime.utcnow()
        end_date = start_date + timedelta(days=days_ahead)
        
        return self.db.query(PsychologySession).filter(
            PsychologySession.psicologo_id == psychologist_id,
            PsychologySession.data_proxima_sessao.between(start_date, end_date)
        ).order_by(PsychologySession.data_proxima_sessao).all()

    def generate_session_summary(self, session_id: int) -> Dict[str, Any]:
        """Gerar resumo automático da sessão"""
        session = self.get_session(session_id)
        if not session:
            return {}

        # Resumo básico
        summary = {
            "session_id": session_id,
            "date": session.data_sessao.isoformat(),
            "duration": session.duracao_minutos,
            "patient_id": session.paciente_id,
            "has_audio": bool(session.audio_url),
            "has_transcription": session.transcricao_processada,
            "scores": {
                "humor": session.humor_escala,
                "ansiedade": session.ansiedade_escala,
                "motivacao": session.motivacao_escala
            }
        }

        # Análise de conteúdo (básica)
        content_analysis = {}
        
        if session.anotacoes:
            word_count = len(session.anotacoes.split())
            content_analysis["notes_word_count"] = word_count
            
        if session.transcricao_texto:
            word_count = len(session.transcricao_texto.split())
            content_analysis["transcription_word_count"] = word_count

        if session.tecnicas_utilizadas:
            techniques = [t.strip() for t in session.tecnicas_utilizadas.split(',')]
            content_analysis["techniques_used"] = techniques

        summary["content_analysis"] = content_analysis

        return summary

    def get_patient_session_history(self, patient_id: int) -> Dict[str, Any]:
        """Obter histórico completo de sessões do paciente"""
        sessions = self.get_sessions_by_patient(patient_id)
        
        if not sessions:
            return {
                "total_sessions": 0,
                "first_session": None,
                "last_session": None,
                "session_frequency": None,
                "progress_summary": {}
            }

        # Calcular frequência de sessões
        if len(sessions) > 1:
            first_session = sessions[-1].data_sessao
            last_session = sessions[0].data_sessao
            days_between = (last_session - first_session).days
            session_frequency = days_between / (len(sessions) - 1) if len(sessions) > 1 else None
        else:
            session_frequency = None

        # Resumo de progresso
        progress_summary = self.get_patient_progress(patient_id)

        return {
            "total_sessions": len(sessions),
            "first_session": sessions[-1].data_sessao.isoformat() if sessions else None,
            "last_session": sessions[0].data_sessao.isoformat() if sessions else None,
            "session_frequency": round(session_frequency, 1) if session_frequency else None,
            "progress_summary": progress_summary
        }
