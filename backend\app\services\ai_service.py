"""
Serviço de integração com APIs de IA (OpenAI, etc.)
"""

import os
import json
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime

# Placeholder para integração futura com OpenAI
# import openai

class AIService:
    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.model = "gpt-3.5-turbo"  # ou "gpt-4" para melhor qualidade

    async def generate_report(self, prompt: str, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Gerar relatório usando IA
        """
        try:
            # TODO: Implementar integração real com OpenAI
            # Por enquanto, retorna relatório simulado
            return await self._simulate_ai_response(prompt, patient_data)

            # Código para integração real (descomentado quando configurado):
            # if not self.openai_api_key:
            #     raise ValueError("OpenAI API key não configurada")

            # openai.api_key = self.openai_api_key

            # response = await openai.ChatCompletion.acreate(
            #     model=self.model,
            #     messages=[
            #         {
            #             "role": "system",
            #             "content": "Você é um assistente especializado em relatórios de saúde e fitness. Gere relatórios profissionais, precisos e úteis baseados nos dados fornecidos."
            #         },
            #         {
            #             "role": "user",
            #             "content": prompt
            #         }
            #     ],
            #     max_tokens=2000,
            #     temperature=0.7
            # )

            # return {
            #     "content": response.choices[0].message.content,
            #     "model_used": self.model,
            #     "tokens_used": response.usage.total_tokens,
            #     "generated_at": datetime.utcnow().isoformat()
            # }

        except Exception as e:
            # Fallback para relatório básico em caso de erro
            return await self._generate_fallback_report(prompt, patient_data, str(e))

    async def _simulate_ai_response(self, prompt: str, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Simular resposta da IA para demonstração
        """
        # Simular delay da API
        await asyncio.sleep(2)

        patient = patient_data.get("patient", {})
        stats = self._calculate_stats(patient_data)

        # Gerar relatório simulado mais sofisticado
        report_content = self._generate_advanced_report(patient, stats, patient_data)

        return {
            "content": report_content,
            "model_used": "hypatium-ai-simulator",
            "tokens_used": len(report_content.split()),
            "generated_at": datetime.utcnow().isoformat(),
            "simulation": True
        }

    def _calculate_stats(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calcular estatísticas avançadas dos dados
        """
        assessments = patient_data.get("assessments", [])
        psychology_sessions = patient_data.get("psychology_sessions", [])

        stats = {}

        # Análise de avaliações físicas
        if assessments:
            weights = [a["peso"] for a in assessments if a["peso"]]
            if len(weights) >= 2:
                stats["weight_trend"] = "perda" if weights[0] < weights[-1] else "ganho" if weights[0] > weights[-1] else "estável"
                stats["weight_change"] = weights[0] - weights[-1]
                stats["weight_change_percent"] = (stats["weight_change"] / weights[-1]) * 100

            body_fats = [a["gordura_percentual"] for a in assessments if a["gordura_percentual"]]
            if len(body_fats) >= 2:
                stats["body_fat_trend"] = "redução" if body_fats[0] < body_fats[-1] else "aumento" if body_fats[0] > body_fats[-1] else "estável"
                stats["body_fat_change"] = body_fats[0] - body_fats[-1]

            imcs = [a["imc"] for a in assessments if a["imc"]]
            if imcs:
                stats["avg_imc"] = sum(imcs) / len(imcs)
                stats["imc_classification"] = self._classify_imc(stats["avg_imc"])

        # Análise psicológica
        if psychology_sessions:
            humor_scores = [s["humor_escala"] for s in psychology_sessions if s["humor_escala"]]
            anxiety_scores = [s["ansiedade_escala"] for s in psychology_sessions if s["ansiedade_escala"]]
            motivation_scores = [s["motivacao_escala"] for s in psychology_sessions if s["motivacao_escala"]]

            if humor_scores:
                stats["avg_humor"] = sum(humor_scores) / len(humor_scores)
                stats["humor_trend"] = self._calculate_trend(humor_scores)

            if anxiety_scores:
                stats["avg_anxiety"] = sum(anxiety_scores) / len(anxiety_scores)
                stats["anxiety_trend"] = self._calculate_trend(anxiety_scores, reverse=True)  # Menor é melhor

            if motivation_scores:
                stats["avg_motivation"] = sum(motivation_scores) / len(motivation_scores)
                stats["motivation_trend"] = self._calculate_trend(motivation_scores)

        return stats

    def _classify_imc(self, imc: float) -> str:
        """Classificar IMC"""
        if imc < 18.5:
            return "Abaixo do peso"
        elif imc < 25:
            return "Peso normal"
        elif imc < 30:
            return "Sobrepeso"
        else:
            return "Obesidade"

    def _calculate_trend(self, values: List[float], reverse: bool = False) -> str:
        """Calcular tendência de uma lista de valores"""
        if len(values) < 2:
            return "insuficiente"

        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]

        avg_first = sum(first_half) / len(first_half)
        avg_second = sum(second_half) / len(second_half)

        if reverse:
            if avg_second < avg_first:
                return "melhora"
            elif avg_second > avg_first:
                return "piora"
        else:
            if avg_second > avg_first:
                return "melhora"
            elif avg_second < avg_first:
                return "piora"

        return "estável"

    def _generate_advanced_report(self, patient: Dict[str, Any], stats: Dict[str, Any], patient_data: Dict[str, Any]) -> str:
        """
        Gerar relatório avançado simulando IA
        """
        nome = patient.get("nome", "Paciente")
        periodo = patient_data.get("period_days", 30)

        report = f"""# RELATÓRIO DE ACOMPANHAMENTO INTELIGENTE

## Paciente: {nome}
**Período de Análise:** {periodo} dias
**Data do Relatório:** {datetime.now().strftime('%d/%m/%Y')}

---

## 🎯 RESUMO EXECUTIVO

"""

        # Análise de progresso geral
        if stats.get("weight_trend"):
            if stats["weight_trend"] == "perda":
                report += f"✅ **Progresso Positivo:** {nome} apresentou {stats['weight_trend']} de peso de {abs(stats['weight_change']):.1f}kg ({abs(stats['weight_change_percent']):.1f}%), indicando aderência ao plano estabelecido.\n\n"
            elif stats["weight_trend"] == "ganho":
                report += f"⚠️ **Atenção Necessária:** Observado ganho de peso de {stats['weight_change']:.1f}kg. Recomenda-se revisão da estratégia nutricional.\n\n"
            else:
                report += f"📊 **Manutenção:** Peso mantido estável, adequado para fase de manutenção ou recomposição corporal.\n\n"

        report += "## 📊 ANÁLISE FÍSICA DETALHADA\n\n"

        # Análise de composição corporal
        if stats.get("body_fat_trend"):
            report += f"**Composição Corporal:**\n"
            report += f"- Tendência de gordura corporal: {stats['body_fat_trend']}\n"
            if stats["body_fat_trend"] == "redução":
                report += f"- Redução de {abs(stats['body_fat_change']):.1f}% na gordura corporal - excelente resultado!\n"
            report += "\n"

        if stats.get("avg_imc"):
            report += f"**Índice de Massa Corporal:**\n"
            report += f"- IMC médio: {stats['avg_imc']:.1f} ({stats['imc_classification']})\n"
            report += f"- Classificação dentro dos parâmetros esperados para o objetivo.\n\n"

        # Análise psicológica
        if any(key in stats for key in ["avg_humor", "avg_anxiety", "avg_motivation"]):
            report += "## 🧠 ANÁLISE PSICOLÓGICA\n\n"

            if stats.get("avg_humor"):
                humor_status = "excelente" if stats["avg_humor"] >= 8 else "bom" if stats["avg_humor"] >= 6 else "necessita atenção"
                report += f"**Estado de Humor:**\n"
                report += f"- Pontuação média: {stats['avg_humor']:.1f}/10 ({humor_status})\n"
                report += f"- Tendência: {stats.get('humor_trend', 'estável')}\n\n"

            if stats.get("avg_anxiety"):
                anxiety_status = "baixo" if stats["avg_anxiety"] <= 3 else "moderado" if stats["avg_anxiety"] <= 6 else "alto"
                report += f"**Nível de Ansiedade:**\n"
                report += f"- Pontuação média: {stats['avg_anxiety']:.1f}/10 (nível {anxiety_status})\n"
                report += f"- Tendência: {stats.get('anxiety_trend', 'estável')}\n\n"

            if stats.get("avg_motivation"):
                motivation_status = "alta" if stats["avg_motivation"] >= 8 else "moderada" if stats["avg_motivation"] >= 6 else "baixa"
                report += f"**Motivação:**\n"
                report += f"- Pontuação média: {stats['avg_motivation']:.1f}/10 ({motivation_status})\n"
                report += f"- Tendência: {stats.get('motivation_trend', 'estável')}\n\n"

        # Recomendações inteligentes
        report += "## 💡 RECOMENDAÇÕES PERSONALIZADAS\n\n"

        recommendations = []

        # Recomendações baseadas no progresso físico
        if stats.get("weight_trend") == "perda" and stats.get("body_fat_trend") == "redução":
            recommendations.append("🎯 **Manter estratégia atual:** Os resultados indicam excelente aderência. Continue com o plano estabelecido.")
        elif stats.get("weight_trend") == "ganho":
            recommendations.append("⚖️ **Ajuste nutricional:** Revisar distribuição calórica e macronutrientes. Considerar redução de 10-15% nas calorias.")

        # Recomendações psicológicas
        if stats.get("avg_anxiety", 0) > 6:
            recommendations.append("🧘 **Gestão de ansiedade:** Implementar técnicas de relaxamento e mindfulness. Considerar sessões mais frequentes.")

        if stats.get("avg_motivation", 0) < 6:
            recommendations.append("🚀 **Estímulo motivacional:** Estabelecer metas de curto prazo e sistema de recompensas. Revisar objetivos.")

        if stats.get("avg_humor", 0) < 6:
            recommendations.append("😊 **Bem-estar emocional:** Focar em atividades prazerosas e suporte social. Monitorar sinais de sobrecarga.")

        # Recomendações gerais
        recommendations.append("📅 **Monitoramento contínuo:** Manter frequência de avaliações para ajustes precisos.")
        recommendations.append("🤝 **Comunicação aberta:** Encorajar feedback constante sobre dificuldades e sucessos.")

        for rec in recommendations:
            report += f"{rec}\n\n"

        # Próximos passos
        report += "## 📋 PRÓXIMOS PASSOS\n\n"
        report += "1. **Curto prazo (1-2 semanas):**\n"
        report += "   - Manter registro alimentar detalhado\n"
        report += "   - Monitorar qualidade do sono\n"
        report += "   - Avaliar aderência ao treino\n\n"

        report += "2. **Médio prazo (1 mês):**\n"
        report += "   - Nova avaliação física completa\n"
        report += "   - Ajustes no plano conforme evolução\n"
        report += "   - Revisão de objetivos\n\n"

        report += "3. **Longo prazo (3 meses):**\n"
        report += "   - Avaliação de resultados globais\n"
        report += "   - Planejamento da próxima fase\n"
        report += "   - Estratégias de manutenção\n\n"

        report += "---\n"
        report += f"*Relatório gerado automaticamente pelo sistema Hypatium AI em {datetime.now().strftime('%d/%m/%Y às %H:%M')}*\n"
        report += "*Este relatório foi criado com base em análise inteligente dos dados coletados e deve ser interpretado por profissional qualificado.*"

        return report

    async def _generate_fallback_report(self, prompt: str, patient_data: Dict[str, Any], error: str) -> Dict[str, Any]:
        """
        Gerar relatório básico em caso de falha da IA
        """
        patient = patient_data.get("patient", {})
        nome = patient.get("nome", "Paciente")

        fallback_content = f"""# RELATÓRIO BÁSICO - {nome.upper()}

## AVISO
Este relatório foi gerado em modo básico devido a uma limitação técnica.
Erro: {error}

## DADOS COLETADOS
- Avaliações físicas: {len(patient_data.get('assessments', []))}
- Sessões psicológicas: {len(patient_data.get('psychology_sessions', []))}
- Planos nutricionais: {len(patient_data.get('nutrition_plans', []))}

## RECOMENDAÇÃO
Recomenda-se análise manual dos dados pelo profissional responsável.

---
*Relatório básico gerado em {datetime.now().strftime('%d/%m/%Y às %H:%M')}*
"""

        return {
            "content": fallback_content,
            "model_used": "fallback",
            "tokens_used": len(fallback_content.split()),
            "generated_at": datetime.utcnow().isoformat(),
            "error": error
        }

    async def transcribe_audio(self, audio_file_path: str) -> Dict[str, Any]:
        """
        Transcrever áudio usando IA (Whisper)
        """
        try:
            # TODO: Implementar integração real com OpenAI Whisper
            # Por enquanto, retorna transcrição simulada mais realística

            # Simular delay de processamento
            await asyncio.sleep(3)

            # Gerar transcrição simulada mais elaborada
            transcription_text = self._generate_realistic_transcription()

            return {
                "text": transcription_text,
                "confidence": 0.92,
                "language": "pt-BR",
                "duration": 1800,  # 30 minutos
                "model_used": "whisper-1-simulation",
                "generated_at": datetime.utcnow().isoformat(),
                "segments": self._generate_transcription_segments(transcription_text),
                "summary": self._generate_transcription_summary(transcription_text)
            }

            # Código para integração real (descomentado quando configurado):
            # if not self.openai_api_key:
            #     raise ValueError("OpenAI API key não configurada")

            # import openai
            # openai.api_key = self.openai_api_key

            # with open(audio_file_path, "rb") as audio_file:
            #     transcript = await openai.Audio.atranscribe(
            #         model="whisper-1",
            #         file=audio_file,
            #         language="pt"
            #     )

            # return {
            #     "text": transcript.text,
            #     "confidence": 0.95,
            #     "language": "pt-BR",
            #     "model_used": "whisper-1",
            #     "generated_at": datetime.utcnow().isoformat()
            # }

        except Exception as e:
            return {
                "text": "",
                "confidence": 0.0,
                "language": "pt-BR",
                "error": str(e),
                "model_used": "error",
                "generated_at": datetime.utcnow().isoformat()
            }

    def _generate_realistic_transcription(self) -> str:
        """
        Gerar transcrição simulada realística
        """
        transcriptions = [
            """Paciente: Olá, doutor. Como está?

Psicólogo: Olá! Estou bem, obrigado. Como você tem se sentido desde nossa última conversa?

Paciente: Bem, eu diria que houve uma melhora significativa no meu humor. Tenho conseguido dormir melhor e me sinto mais motivado para as atividades do dia a dia.

Psicólogo: Isso é muito bom de ouvir. Você pode me contar mais sobre essas mudanças? O que especificamente tem ajudado?

Paciente: Acho que as técnicas de respiração que praticamos têm funcionado muito bem. Quando sinto a ansiedade chegando, eu paro e faço os exercícios que você me ensinou.

Psicólogo: Excelente! E como está sua relação com a família e amigos?

Paciente: Melhor também. Tenho conseguido me comunicar de forma mais clara e sem tanta irritabilidade. Minha esposa comentou que nota a diferença.

Psicólogo: Que progresso maravilhoso. Vamos continuar trabalhando essas estratégias e talvez introduzir algumas novas técnicas na próxima sessão.""",

            """Paciente: Doutor, esta semana foi um pouco mais difícil.

Psicólogo: Entendo. Pode me contar o que aconteceu?

Paciente: Tive alguns episódios de ansiedade no trabalho. Principalmente quando tive que apresentar um projeto para a equipe.

Psicólogo: Como você lidou com essas situações?

Paciente: Tentei usar as técnicas que aprendemos, mas foi difícil. Senti o coração acelerado e as mãos suando.

Psicólogo: É normal ter alguns altos e baixos no processo. O importante é que você tentou aplicar as estratégias. Vamos trabalhar mais especificamente situações de apresentação e falar em público.

Paciente: Isso seria muito útil. Tenho evitado essas situações, mas sei que preciso enfrentá-las.

Psicólogo: Vamos fazer isso gradualmente, sem pressa. Que tal começarmos com alguns exercícios de visualização?""",

            """Paciente: Queria falar sobre meus objetivos para os próximos meses.

Psicólogo: Claro, é importante estabelecermos metas claras. O que você tem em mente?

Paciente: Gostaria de conseguir fazer exercícios regularmente. Sei que isso ajuda com o humor e a ansiedade.

Psicólogo: Ótima ideia. A atividade física realmente tem benefícios comprovados para a saúde mental. Você já tem algum plano?

Paciente: Pensei em começar com caminhadas três vezes por semana. Nada muito intenso no início.

Psicólogo: Perfeito. Começar gradualmente é a melhor abordagem. Que tal também incluirmos isso como parte do seu plano de autocuidado?

Paciente: Sim, acho que seria bom ter tudo organizado. Talvez um cronograma?

Psicólogo: Exatamente. Vamos criar um plano estruturado que inclua exercícios, técnicas de relaxamento e momentos de lazer."""
        ]

        import random
        return random.choice(transcriptions)

    def _generate_transcription_segments(self, text: str) -> List[Dict[str, Any]]:
        """
        Gerar segmentos da transcrição com timestamps
        """
        lines = text.split('\n\n')
        segments = []
        current_time = 0

        for i, line in enumerate(lines):
            if line.strip():
                duration = len(line.split()) * 0.5  # ~0.5 segundos por palavra
                segments.append({
                    "id": i,
                    "start": current_time,
                    "end": current_time + duration,
                    "text": line.strip(),
                    "speaker": "Paciente" if line.startswith("Paciente:") else "Psicólogo"
                })
                current_time += duration + 2  # 2 segundos de pausa

        return segments

    def _generate_transcription_summary(self, text: str) -> str:
        """
        Gerar resumo da transcrição
        """
        if "melhora" in text.lower() and "humor" in text.lower():
            return "Sessão focou no progresso do paciente, com relatos de melhora no humor e aplicação das técnicas aprendidas."
        elif "ansiedade" in text.lower() and "trabalho" in text.lower():
            return "Discussão sobre episódios de ansiedade no ambiente de trabalho e estratégias de enfrentamento."
        elif "objetivos" in text.lower() and "exercícios" in text.lower():
            return "Planejamento de objetivos futuros com foco em atividade física e autocuidado."
        else:
            return "Sessão de acompanhamento psicológico com discussão sobre progresso e estratégias terapêuticas."

    def get_ai_status(self) -> Dict[str, Any]:
        """
        Verificar status das integrações de IA
        """
        return {
            "openai_configured": bool(self.openai_api_key),
            "services_available": {
                "report_generation": True,  # Sempre disponível (com fallback)
                "audio_transcription": bool(self.openai_api_key),
                "text_analysis": bool(self.openai_api_key)
            },
            "current_model": self.model,
            "simulation_mode": not bool(self.openai_api_key)
        }
