"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AvaliacaoService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let AvaliacaoService = class AvaliacaoService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    calcularIMC(peso, altura) {
        const alturaMetros = altura / 100;
        const imc = peso / (alturaMetros * alturaMetros);
        let categoria;
        if (imc < 18.5)
            categoria = 'Abaixo do peso';
        else if (imc < 25)
            categoria = 'Peso normal';
        else if (imc < 30)
            categoria = 'Sobrepeso';
        else if (imc < 35)
            categoria = 'Obesidade grau I';
        else if (imc < 40)
            categoria = 'Obesidade grau II';
        else
            categoria = 'Obesidade grau III';
        return { valor: parseFloat(imc.toFixed(2)), categoria };
    }
    calcularGorduraGuedes(dobras, idade, sexo) {
        const { subescapular = 0, tricipital = 0, suprailíaca = 0, abdominal = 0 } = dobras;
        const somaDobras = subescapular + tricipital + suprailíaca + abdominal;
        let densidade;
        if (sexo === 'M') {
            densidade = 1.1714 - (0.0671 * Math.log10(somaDobras));
        }
        else {
            densidade = 1.1549 - (0.0678 * Math.log10(somaDobras));
        }
        const percentualGordura = ((4.95 / densidade) - 4.5) * 100;
        return {
            densidade: parseFloat(densidade.toFixed(4)),
            percentual: parseFloat(percentualGordura.toFixed(2)),
        };
    }
    calcularGorduraPollock3(dobras, idade, sexo) {
        const { tricipital = 0, suprailíaca = 0, coxa = 0 } = dobras;
        const somaDobras = tricipital + suprailíaca + coxa;
        let densidade;
        if (sexo === 'M') {
            densidade = 1.1125025 - (0.0013125 * somaDobras) + (0.0000055 * Math.pow(somaDobras, 2)) - (0.000244 * idade);
        }
        else {
            densidade = 1.0994921 - (0.0009929 * somaDobras) + (0.0000023 * Math.pow(somaDobras, 2)) - (0.0001392 * idade);
        }
        const percentualGordura = ((4.95 / densidade) - 4.5) * 100;
        return {
            densidade: parseFloat(densidade.toFixed(4)),
            percentual: parseFloat(percentualGordura.toFixed(2)),
        };
    }
    calcularGorduraPollock7(dobras, idade, sexo) {
        const { subescapular = 0, tricipital = 0, toracica = 0, axilarMedia = 0, suprailíaca = 0, abdominal = 0, coxa = 0 } = dobras;
        const somaDobras = subescapular + tricipital + toracica + axilarMedia + suprailíaca + abdominal + coxa;
        let densidade;
        if (sexo === 'M') {
            densidade = 1.112 - (0.00043499 * somaDobras) + (0.00000055 * Math.pow(somaDobras, 2)) - (0.00028826 * idade);
        }
        else {
            densidade = 1.097 - (0.00046971 * somaDobras) + (0.00000056 * Math.pow(somaDobras, 2)) - (0.00012828 * idade);
        }
        const percentualGordura = ((4.95 / densidade) - 4.5) * 100;
        return {
            densidade: parseFloat(densidade.toFixed(4)),
            percentual: parseFloat(percentualGordura.toFixed(2)),
        };
    }
    calcularMassas(peso, percentualGordura) {
        const massaGordura = (peso * percentualGordura) / 100;
        const massaMagro = peso - massaGordura;
        return {
            massaGordura: parseFloat(massaGordura.toFixed(2)),
            massaMagro: parseFloat(massaMagro.toFixed(2)),
        };
    }
    async createAvaliacaoComposicao(createAvaliacaoDto, personalId) {
        const aluno = await this.prisma.aluno.findUnique({
            where: { id: createAvaliacaoDto.alunoId },
        });
        if (!aluno) {
            throw new common_1.NotFoundException('Aluno não encontrado');
        }
        if (aluno.personalId !== personalId) {
            throw new common_1.ForbiddenException('Acesso negado a este aluno');
        }
        const dobras = {
            subescapular: createAvaliacaoDto.subescapular || 0,
            tricipital: createAvaliacaoDto.tricipital || 0,
            bicipital: createAvaliacaoDto.bicipital || 0,
            toracica: createAvaliacaoDto.toracica || 0,
            abdominal: createAvaliacaoDto.abdominal || 0,
            axilarMedia: createAvaliacaoDto.axilarMedia || 0,
            suprailíaca: createAvaliacaoDto.suprailíaca || 0,
            coxa: createAvaliacaoDto.coxa || 0,
            panturrilha: createAvaliacaoDto.panturrilha || 0,
        };
        const somaDobras = Object.values(dobras).reduce((sum, value) => sum + value, 0);
        const imc = this.calcularIMC(aluno.pesoAtual, aluno.estatura);
        const sexo = 'M';
        const guedes = this.calcularGorduraGuedes(dobras, aluno.idade, sexo);
        const guedesMassas = this.calcularMassas(aluno.pesoAtual, guedes.percentual);
        const pollock3 = this.calcularGorduraPollock3(dobras, aluno.idade, sexo);
        const pollock3Massas = this.calcularMassas(aluno.pesoAtual, pollock3.percentual);
        const pollock7 = this.calcularGorduraPollock7(dobras, aluno.idade, sexo);
        const pollock7Massas = this.calcularMassas(aluno.pesoAtual, pollock7.percentual);
        const avaliacao = await this.prisma.avaliacaoComposicao.create({
            data: {
                ...createAvaliacaoDto,
                personalId,
                somaDobras,
                valorIMC: imc.valor,
                categoriaIMC: imc.categoria,
                guedesDensidade: guedes.densidade,
                guedesPercentual: guedes.percentual,
                guedesMassaGordura: guedesMassas.massaGordura,
                guedesMassaMagro: guedesMassas.massaMagro,
                pollock3Densidade: pollock3.densidade,
                pollock3Percentual: pollock3.percentual,
                pollock3MassaGordura: pollock3Massas.massaGordura,
                pollock3MassaMagro: pollock3Massas.massaMagro,
                pollock7Densidade: pollock7.densidade,
                pollock7Percentual: pollock7.percentual,
                pollock7MassaGordura: pollock7Massas.massaGordura,
                pollock7MassaMagro: pollock7Massas.massaMagro,
            },
            include: {
                aluno: {
                    select: {
                        nome: true,
                        pesoAtual: true,
                        estatura: true,
                        idade: true,
                    },
                },
            },
        });
        return avaliacao;
    }
    calcularTMB(peso, altura, idade, sexo) {
        const alturaMetros = altura / 100;
        let faoOMS;
        if (sexo === 'M') {
            if (idade >= 18 && idade <= 30)
                faoOMS = (15.3 * peso) + 679;
            else if (idade >= 31 && idade <= 60)
                faoOMS = (11.6 * peso) + 879;
            else
                faoOMS = (13.5 * peso) + 487;
        }
        else {
            if (idade >= 18 && idade <= 30)
                faoOMS = (14.7 * peso) + 496;
            else if (idade >= 31 && idade <= 60)
                faoOMS = (8.7 * peso) + 829;
            else
                faoOMS = (10.5 * peso) + 596;
        }
        let harrisBenedict;
        if (sexo === 'M') {
            harrisBenedict = 88.362 + (13.397 * peso) + (4.799 * altura) - (5.677 * idade);
        }
        else {
            harrisBenedict = 447.593 + (9.247 * peso) + (3.098 * altura) - (4.330 * idade);
        }
        let mifflin;
        if (sexo === 'M') {
            mifflin = (10 * peso) + (6.25 * altura) - (5 * idade) + 5;
        }
        else {
            mifflin = (10 * peso) + (6.25 * altura) - (5 * idade) - 161;
        }
        const percentualGorduraEstimado = sexo === 'M' ? 15 : 25;
        const massaMagra = peso * (1 - percentualGorduraEstimado / 100);
        const cunningham = 500 + (22 * massaMagra);
        const tinsleyMLG = 25.9 * massaMagra + 284;
        let tinsleyPeso;
        if (sexo === 'M') {
            tinsleyPeso = (10 * peso) + (6.25 * altura) - (5 * idade) + 5;
        }
        else {
            tinsleyPeso = (10 * peso) + (6.25 * altura) - (5 * idade) - 161;
        }
        return {
            faoOMS: parseFloat(faoOMS.toFixed(0)),
            harrisBenedict: parseFloat(harrisBenedict.toFixed(0)),
            mifflin: parseFloat(mifflin.toFixed(0)),
            cunningham: parseFloat(cunningham.toFixed(0)),
            tinsleyMLG: parseFloat(tinsleyMLG.toFixed(0)),
            tinsleyPeso: parseFloat(tinsleyPeso.toFixed(0)),
        };
    }
    async createAvaliacaoEnergetica(avaliacaoComposicaoId, createAvaliacaoEnergeticaDto, personalId) {
        const avaliacaoComposicao = await this.prisma.avaliacaoComposicao.findUnique({
            where: { id: avaliacaoComposicaoId },
            include: {
                aluno: true,
            },
        });
        if (!avaliacaoComposicao) {
            throw new common_1.NotFoundException('Avaliação de composição não encontrada');
        }
        if (avaliacaoComposicao.personalId !== personalId) {
            throw new common_1.ForbiddenException('Acesso negado a esta avaliação');
        }
        const sexo = 'M';
        const tmb = this.calcularTMB(avaliacaoComposicao.aluno.pesoAtual, avaliacaoComposicao.aluno.estatura, avaliacaoComposicao.aluno.idade, sexo);
        const fatorAtividade = createAvaliacaoEnergeticaDto.fatorAtividade || 1.4;
        const tmbSelecionado = createAvaliacaoEnergeticaDto.tmbMifflin || tmb.mifflin;
        const valorGET = tmbSelecionado * fatorAtividade;
        const percentualDeficitSuperavit = createAvaliacaoEnergeticaDto.percentualDeficitSuperavit || 0;
        const valorConsumo = valorGET * (1 + percentualDeficitSuperavit / 100);
        const avaliacaoEnergetica = await this.prisma.avaliacaoEnergetica.create({
            data: {
                avaliacaoComposicaoId,
                tmbFAO_OMS: tmb.faoOMS,
                tmbHarrisBenedict: tmb.harrisBenedict,
                tmbMifflin: tmb.mifflin,
                tmbCunningham: tmb.cunningham,
                tmbTinsleyMLG: tmb.tinsleyMLG,
                tmbTinsleyPeso: tmb.tinsleyPeso,
                fatorAtividade,
                valorGET: parseFloat(valorGET.toFixed(0)),
                percentualDeficitSuperavit,
                valorConsumo: parseFloat(valorConsumo.toFixed(0)),
            },
        });
        return avaliacaoEnergetica;
    }
    async findAvaliacoesByAluno(alunoId, personalId) {
        if (personalId) {
            const aluno = await this.prisma.aluno.findUnique({
                where: { id: alunoId },
            });
            if (!aluno || aluno.personalId !== personalId) {
                throw new common_1.ForbiddenException('Acesso negado a este aluno');
            }
        }
        return this.prisma.avaliacaoComposicao.findMany({
            where: { alunoId },
            include: {
                avaliacaoEnergetica: true,
                aluno: {
                    select: {
                        nome: true,
                        pesoAtual: true,
                        estatura: true,
                        idade: true,
                    },
                },
            },
            orderBy: {
                dataAvaliacao: 'desc',
            },
        });
    }
    async findAvaliacaoById(id, personalId) {
        const avaliacao = await this.prisma.avaliacaoComposicao.findUnique({
            where: { id },
            include: {
                avaliacaoEnergetica: true,
                aluno: {
                    select: {
                        nome: true,
                        pesoAtual: true,
                        estatura: true,
                        idade: true,
                    },
                },
                volumeLoads: {
                    orderBy: [
                        { exercicio: 'asc' },
                        { semana: 'asc' },
                    ],
                },
            },
        });
        if (!avaliacao) {
            throw new common_1.NotFoundException('Avaliação não encontrada');
        }
        if (personalId && avaliacao.personalId !== personalId) {
            throw new common_1.ForbiddenException('Acesso negado a esta avaliação');
        }
        return avaliacao;
    }
};
exports.AvaliacaoService = AvaliacaoService;
exports.AvaliacaoService = AvaliacaoService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AvaliacaoService);
//# sourceMappingURL=avaliacao.service.js.map