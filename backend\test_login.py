#!/usr/bin/env python3
"""
Script para testar o login diretamente
"""

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.models import User
from app.core.security import verify_password, create_access_token

def test_login():
    """Testar login diretamente"""
    
    db = SessionLocal()
    
    try:
        # Buscar usuário admin
        user = db.query(User).filter(User.email == "<EMAIL>").first()
        
        if not user:
            print("❌ Usuário admin não encontrado!")
            return False
        
        print(f"✅ Usuário encontrado: {user.nome} ({user.email})")
        print(f"📧 Email: {user.email}")
        print(f"🔑 Tipo: {user.tipo}")
        print(f"✅ Ativo: {user.ativo}")
        
        # Testar senha
        password_correct = verify_password("admin123", user.senha_hash)
        print(f"🔐 Senha correta: {password_correct}")
        
        if password_correct:
            # Criar token
            token = create_access_token(subject=str(user.id))
            print(f"🎫 Token criado: {token[:50]}...")
            return True
        else:
            print("❌ Senha incorreta!")
            return False
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🧪 Testando login...")
    success = test_login()
    if success:
        print("\n✅ Login funcionando corretamente!")
    else:
        print("\n❌ Problema no login!")
