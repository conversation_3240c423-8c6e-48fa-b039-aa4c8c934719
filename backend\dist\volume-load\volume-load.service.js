"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VolumeLoadService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let VolumeLoadService = class VolumeLoadService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    calcularVLSerie(repeticoes, carga) {
        if (!repeticoes || !carga)
            return 0;
        return repeticoes * carga;
    }
    calcularVLExercicio(volumeLoad) {
        const series = [
            this.calcularVLSerie(volumeLoad.serie1Repeticoes, volumeLoad.serie1Carga),
            this.calcularVLSerie(volumeLoad.serie2Repeticoes, volumeLoad.serie2Carga),
            this.calcularVLSerie(volumeLoad.serie3Repeticoes, volumeLoad.serie3Carga),
            this.calcularVLSerie(volumeLoad.serie4Repeticoes, volumeLoad.serie4Carga),
            this.calcularVLSerie(volumeLoad.serie5Repeticoes, volumeLoad.serie5Carga),
            this.calcularVLSerie(volumeLoad.serie6Repeticoes, volumeLoad.serie6Carga),
            this.calcularVLSerie(volumeLoad.serie7Repeticoes, volumeLoad.serie7Carga),
            this.calcularVLSerie(volumeLoad.serie8Repeticoes, volumeLoad.serie8Carga),
            this.calcularVLSerie(volumeLoad.serie9Repeticoes, volumeLoad.serie9Carga),
            this.calcularVLSerie(volumeLoad.serie10Repeticoes, volumeLoad.serie10Carga),
        ];
        return series.reduce((total, vlSerie) => total + vlSerie, 0);
    }
    async create(createVolumeLoadDto, personalId) {
        const avaliacao = await this.prisma.avaliacaoComposicao.findUnique({
            where: { id: createVolumeLoadDto.avaliacaoComposicaoId },
            include: { aluno: true },
        });
        if (!avaliacao) {
            throw new common_1.NotFoundException('Avaliação não encontrada');
        }
        if (avaliacao.personalId !== personalId) {
            throw new common_1.ForbiddenException('Acesso negado a esta avaliação');
        }
        const vlSerie1 = this.calcularVLSerie(createVolumeLoadDto.serie1Repeticoes, createVolumeLoadDto.serie1Carga);
        const vlSerie2 = this.calcularVLSerie(createVolumeLoadDto.serie2Repeticoes, createVolumeLoadDto.serie2Carga);
        const vlSerie3 = this.calcularVLSerie(createVolumeLoadDto.serie3Repeticoes, createVolumeLoadDto.serie3Carga);
        const vlSerie4 = this.calcularVLSerie(createVolumeLoadDto.serie4Repeticoes, createVolumeLoadDto.serie4Carga);
        const vlSerie5 = this.calcularVLSerie(createVolumeLoadDto.serie5Repeticoes, createVolumeLoadDto.serie5Carga);
        const vlSerie6 = this.calcularVLSerie(createVolumeLoadDto.serie6Repeticoes, createVolumeLoadDto.serie6Carga);
        const vlSerie7 = this.calcularVLSerie(createVolumeLoadDto.serie7Repeticoes, createVolumeLoadDto.serie7Carga);
        const vlSerie8 = this.calcularVLSerie(createVolumeLoadDto.serie8Repeticoes, createVolumeLoadDto.serie8Carga);
        const vlSerie9 = this.calcularVLSerie(createVolumeLoadDto.serie9Repeticoes, createVolumeLoadDto.serie9Carga);
        const vlSerie10 = this.calcularVLSerie(createVolumeLoadDto.serie10Repeticoes, createVolumeLoadDto.serie10Carga);
        const vlExercicio = vlSerie1 + vlSerie2 + vlSerie3 + vlSerie4 + vlSerie5 +
            vlSerie6 + vlSerie7 + vlSerie8 + vlSerie9 + vlSerie10;
        const volumeLoad = await this.prisma.volumeLoad.create({
            data: {
                ...createVolumeLoadDto,
                serie1VL: vlSerie1,
                serie2VL: vlSerie2,
                serie3VL: vlSerie3,
                serie4VL: vlSerie4,
                serie5VL: vlSerie5,
                serie6VL: vlSerie6,
                serie7VL: vlSerie7,
                serie8VL: vlSerie8,
                serie9VL: vlSerie9,
                serie10VL: vlSerie10,
                vlExercicio,
            },
        });
        await this.calcularVLMesociclo(createVolumeLoadDto.avaliacaoComposicaoId, createVolumeLoadDto.exercicio);
        return volumeLoad;
    }
    async update(id, updateVolumeLoadDto, personalId) {
        const volumeLoad = await this.prisma.volumeLoad.findUnique({
            where: { id },
            include: {
                avaliacaoComposicao: true,
            },
        });
        if (!volumeLoad) {
            throw new common_1.NotFoundException('Volume Load não encontrado');
        }
        if (volumeLoad.avaliacaoComposicao.personalId !== personalId) {
            throw new common_1.ForbiddenException('Acesso negado a este Volume Load');
        }
        const dadosAtualizados = { ...volumeLoad, ...updateVolumeLoadDto };
        const vlExercicio = this.calcularVLExercicio(dadosAtualizados);
        const updatedVolumeLoad = await this.prisma.volumeLoad.update({
            where: { id },
            data: {
                ...updateVolumeLoadDto,
                vlExercicio,
            },
        });
        await this.calcularVLMesociclo(volumeLoad.avaliacaoComposicaoId, volumeLoad.exercicio);
        return updatedVolumeLoad;
    }
    async calcularVLMesociclo(avaliacaoComposicaoId, exercicio) {
        const volumeLoads = await this.prisma.volumeLoad.findMany({
            where: {
                avaliacaoComposicaoId,
                exercicio,
            },
        });
        const vlMesociclo = volumeLoads.reduce((total, vl) => total + (vl.vlExercicio || 0), 0);
        await this.prisma.volumeLoad.updateMany({
            where: {
                avaliacaoComposicaoId,
                exercicio,
            },
            data: {
                vlMesociclo,
            },
        });
    }
    async findByAvaliacao(avaliacaoComposicaoId, personalId) {
        if (personalId) {
            const avaliacao = await this.prisma.avaliacaoComposicao.findUnique({
                where: { id: avaliacaoComposicaoId },
            });
            if (!avaliacao || avaliacao.personalId !== personalId) {
                throw new common_1.ForbiddenException('Acesso negado a esta avaliação');
            }
        }
        return this.prisma.volumeLoad.findMany({
            where: { avaliacaoComposicaoId },
            orderBy: [
                { exercicio: 'asc' },
                { semana: 'asc' },
            ],
        });
    }
    async getVolumeLoadStats(avaliacaoComposicaoId, personalId) {
        const volumeLoads = await this.findByAvaliacao(avaliacaoComposicaoId, personalId);
        const porExercicio = volumeLoads.reduce((acc, vl) => {
            if (!acc[vl.exercicio]) {
                acc[vl.exercicio] = {
                    exercicio: vl.exercicio,
                    semanas: [],
                    vlTotal: 0,
                };
            }
            acc[vl.exercicio].semanas.push({
                semana: vl.semana,
                vlExercicio: vl.vlExercicio || 0,
            });
            acc[vl.exercicio].vlTotal += vl.vlExercicio || 0;
            return acc;
        }, {});
        const porSemana = volumeLoads.reduce((acc, vl) => {
            if (!acc[vl.semana]) {
                acc[vl.semana] = {
                    semana: vl.semana,
                    exercicios: [],
                    vlTotal: 0,
                };
            }
            acc[vl.semana].exercicios.push({
                exercicio: vl.exercicio,
                vlExercicio: vl.vlExercicio || 0,
            });
            acc[vl.semana].vlTotal += vl.vlExercicio || 0;
            return acc;
        }, {});
        return {
            porExercicio: Object.values(porExercicio),
            porSemana: Object.values(porSemana),
            vlTotalMesociclo: volumeLoads.reduce((total, vl) => total + (vl.vlExercicio || 0), 0),
        };
    }
    async remove(id, personalId) {
        const volumeLoad = await this.prisma.volumeLoad.findUnique({
            where: { id },
            include: {
                avaliacaoComposicao: true,
            },
        });
        if (!volumeLoad) {
            throw new common_1.NotFoundException('Volume Load não encontrado');
        }
        if (volumeLoad.avaliacaoComposicao.personalId !== personalId) {
            throw new common_1.ForbiddenException('Acesso negado a este Volume Load');
        }
        await this.prisma.volumeLoad.delete({
            where: { id },
        });
        await this.calcularVLMesociclo(volumeLoad.avaliacaoComposicaoId, volumeLoad.exercicio);
        return { message: 'Volume Load removido com sucesso' };
    }
};
exports.VolumeLoadService = VolumeLoadService;
exports.VolumeLoadService = VolumeLoadService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VolumeLoadService);
//# sourceMappingURL=volume-load.service.js.map