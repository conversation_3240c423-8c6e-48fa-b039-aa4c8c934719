import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  ArrowLeft,
  Edit,
  Utensils,
  Calendar,
  Phone,
  Mail,
  User,
  FileText,
  Plus,
  Scale,
  Target
} from 'lucide-react'
import Layout from '../../../components/Layout'

interface Patient {
  id: number
  nome: string
  email: string
  telefone: string
  data_nascimento: string
  sexo: string
  peso: number
  altura: number
  objetivo_nutricional: string
  restricoes_alimentares: string
  alergias: string
  medicamentos: string
  nivel_atividade: string
  observacoes: string
  created_at: string
}

export default function NutritionPatientDetails() {
  const router = useRouter()
  const { id } = router.query
  const [patient, setPatient] = useState<Patient | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (id) {
      fetchPatient()
    }
  }, [id])

  const fetchPatient = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Mock data - em produção viria da API
      setPatient({
        id: Number(id),
        nome: 'Maria Silva',
        email: '<EMAIL>',
        telefone: '(11) 98888-8888',
        data_nascimento: '1985-07-15',
        sexo: 'feminino',
        peso: 68.5,
        altura: 1.65,
        objetivo_nutricional: 'Emagrecimento saudável e melhora da composição corporal',
        restricoes_alimentares: 'Vegetariana, evita glúten',
        alergias: 'Amendoim, frutos do mar',
        medicamentos: 'Vitamina D3 - 2000UI diário',
        nivel_atividade: 'Moderado - 3x por semana',
        observacoes: 'Paciente muito dedicada, segue bem as orientações nutricionais',
        created_at: '2024-01-15'
      })
    } catch (error) {
      console.error('Erro ao carregar paciente:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const calculateAge = (birthDate: string) => {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age
  }

  const calculateIMC = (peso: number, altura: number) => {
    const imc = peso / (altura * altura)
    return imc.toFixed(1)
  }

  const getIMCCategory = (imc: number) => {
    if (imc < 18.5) return { category: 'Abaixo do peso', color: 'text-blue-600' }
    if (imc < 25) return { category: 'Peso normal', color: 'text-green-600' }
    if (imc < 30) return { category: 'Sobrepeso', color: 'text-yellow-600' }
    return { category: 'Obesidade', color: 'text-red-600' }
  }

  if (loading) {
    return (
      <Layout title="Carregando...">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        </div>
      </Layout>
    )
  }

  if (!patient) {
    return (
      <Layout title="Paciente não encontrado">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">Paciente não encontrado</h2>
          <Link href="/nutrition/patients" className="text-green-600 hover:text-green-800">
            Voltar para lista de pacientes
          </Link>
        </div>
      </Layout>
    )
  }

  const imc = parseFloat(calculateIMC(patient.peso, patient.altura))
  const imcInfo = getIMCCategory(imc)

  return (
    <Layout title={`${patient.nome} - Nutrição`}>
      <Head>
        <title>{patient.nome} - Nutrição | Hypatium</title>
      </Head>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Link
                  href="/nutrition/patients"
                  className="mr-4 p-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                    <Utensils className="h-6 w-6 text-green-600 mr-2" />
                    {patient.nome}
                  </h1>
                  <p className="text-gray-600">Paciente de Nutrição</p>
                </div>
              </div>
              <div className="flex space-x-3">
                <Link
                  href={`/nutrition/patients/${patient.id}/plans`}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Ver Planos
                </Link>
                <Link
                  href={`/nutrition/plans/new?patient=${patient.id}`}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Novo Plano
                </Link>
                <Link
                  href={`/nutrition/patients/${patient.id}/edit`}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </Link>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Informações Principais */}
            <div className="lg:col-span-2 space-y-6">
              {/* Dados Pessoais */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <User className="h-5 w-5 text-green-600 mr-2" />
                  Informações Pessoais
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Nome</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.nome}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Idade</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {calculateAge(patient.data_nascimento)} anos
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Sexo</label>
                    <p className="mt-1 text-sm text-gray-900 capitalize">{patient.sexo}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Data de Nascimento</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(patient.data_nascimento)}</p>
                  </div>
                </div>
              </div>

              {/* Dados Antropométricos */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Scale className="h-5 w-5 text-green-600 mr-2" />
                  Dados Antropométricos
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Peso</label>
                    <p className="mt-1 text-lg font-semibold text-gray-900">{patient.peso} kg</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Altura</label>
                    <p className="mt-1 text-lg font-semibold text-gray-900">{patient.altura} m</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">IMC</label>
                    <p className={`mt-1 text-lg font-semibold ${imcInfo.color}`}>
                      {calculateIMC(patient.peso, patient.altura)}
                    </p>
                    <p className={`text-xs ${imcInfo.color}`}>{imcInfo.category}</p>
                  </div>
                </div>
              </div>

              {/* Informações Nutricionais */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Utensils className="h-5 w-5 text-green-600 mr-2" />
                  Informações Nutricionais
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Objetivo Nutricional</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.objetivo_nutricional}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Restrições Alimentares</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.restricoes_alimentares}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Alergias</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.alergias}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Nível de Atividade</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.nivel_atividade}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Medicamentos</label>
                    <p className="mt-1 text-sm text-gray-900">{patient.medicamentos}</p>
                  </div>
                  {patient.observacoes && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Observações</label>
                      <p className="mt-1 text-sm text-gray-900">{patient.observacoes}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Contato */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Contato</h3>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-gray-400 mr-3" />
                    <span className="text-sm text-gray-900">{patient.email}</span>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 text-gray-400 mr-3" />
                    <span className="text-sm text-gray-900">{patient.telefone}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-400 mr-3" />
                    <span className="text-sm text-gray-900">
                      Cadastrado em {formatDate(patient.created_at)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Ações Rápidas */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Ações Rápidas</h3>
                <div className="space-y-3">
                  <Link
                    href={`/nutrition/plans/new?patient=${patient.id}`}
                    className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Novo Plano
                  </Link>
                  <Link
                    href={`/nutrition/patients/${patient.id}/plans`}
                    className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Ver Histórico
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
