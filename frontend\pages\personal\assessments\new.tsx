import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  TrendingUp, 
  User, 
  Calendar,
  Ruler,
  Weight,
  Calculator,
  Save,
  X,
  Info
} from 'lucide-react'

const assessmentSchema = z.object({
  peso: z.number().min(20, 'Peso deve ser maior que 20kg').max(300, 'Peso deve ser menor que 300kg'),
  altura: z.number().min(1.0, 'Altura deve ser maior que 1.0m').max(2.5, 'Altura deve ser menor que 2.5m'),
  data_avaliacao: z.string().min(1, 'Data da avaliação é obrigatória'),
  
  // Dobras cutâneas (opcional)
  peitoral: z.number().optional(),
  abdominal: z.number().optional(),
  coxa: z.number().optional(),
  triceps: z.number().optional(),
  supra_iliaca: z.number().optional(),
  subescapular: z.number().optional(),
  axilar_media: z.number().optional(),
  
  // Circunferências (opcional)
  braco: z.number().optional(),
  cintura: z.number().optional(),
  quadril: z.number().optional(),
  coxa_circ: z.number().optional(),
  panturrilha: z.number().optional(),
  
  metodo_utilizado: z.enum(['pollock_3', 'pollock_7']).optional(),
  observacoes: z.string().optional(),
})

type AssessmentForm = z.infer<typeof assessmentSchema>

interface Patient {
  id: number
  nome: string
  nascimento?: string
  sexo?: string
}

export default function NewAssessmentPage() {
  const router = useRouter()
  const { patient: patientId } = router.query
  const [patient, setPatient] = useState<Patient | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [calculations, setCalculations] = useState<any>(null)

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<AssessmentForm>({
    resolver: zodResolver(assessmentSchema),
    defaultValues: {
      data_avaliacao: new Date().toISOString().split('T')[0],
      metodo_utilizado: 'pollock_3'
    }
  })

  const watchedValues = watch()

  useEffect(() => {
    if (patientId) {
      fetchPatient()
    }
  }, [patientId])

  useEffect(() => {
    // Calcular em tempo real quando houver dados suficientes
    if (watchedValues.peso && watchedValues.altura) {
      calculatePreview()
    }
  }, [watchedValues.peso, watchedValues.altura, watchedValues.peitoral, watchedValues.abdominal, watchedValues.coxa])

  const fetchPatient = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/patients/${patientId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (response.ok) {
        const patientData = await response.json()
        setPatient(patientData)
      }
    } catch (error) {
      console.error('Erro ao buscar paciente:', error)
    }
  }

  const calculatePreview = async () => {
    if (!watchedValues.peso || !watchedValues.altura || !patient) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const calculationData = {
        peso: watchedValues.peso,
        altura: watchedValues.altura,
        idade: patient.nascimento ? calculateAge(patient.nascimento) : 30,
        sexo: patient.sexo || 'feminino',
        dobras_cutaneas: {
          peitoral: watchedValues.peitoral || 0,
          abdominal: watchedValues.abdominal || 0,
          coxa: watchedValues.coxa || 0,
          triceps: watchedValues.triceps || 0,
          supra_iliaca: watchedValues.supra_iliaca || 0,
        },
        metodo: watchedValues.metodo_utilizado || 'pollock_3'
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/assessments/calculate/body-composition`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(calculationData),
        }
      )

      if (response.ok) {
        const result = await response.json()
        setCalculations(result)
      }
    } catch (error) {
      console.error('Erro ao calcular:', error)
    }
  }

  const calculateAge = (birthDate: string) => {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age
  }

  const onSubmit = async (data: AssessmentForm) => {
    setIsLoading(true)
    setError('')

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Preparar dados da avaliação
      const assessmentData = {
        peso: data.peso,
        altura: data.altura,
        data_avaliacao: new Date(data.data_avaliacao).toISOString(),
        dobras_cutaneas: {
          ...(data.peitoral && { peitoral: data.peitoral }),
          ...(data.abdominal && { abdominal: data.abdominal }),
          ...(data.coxa && { coxa: data.coxa }),
          ...(data.triceps && { triceps: data.triceps }),
          ...(data.supra_iliaca && { supra_iliaca: data.supra_iliaca }),
          ...(data.subescapular && { subescapular: data.subescapular }),
          ...(data.axilar_media && { axilar_media: data.axilar_media }),
        },
        circunferencias: {
          ...(data.braco && { braco: data.braco }),
          ...(data.cintura && { cintura: data.cintura }),
          ...(data.quadril && { quadril: data.quadril }),
          ...(data.coxa_circ && { coxa: data.coxa_circ }),
          ...(data.panturrilha && { panturrilha: data.panturrilha }),
        },
        metodo_utilizado: data.metodo_utilizado,
        observacoes: data.observacoes,
        paciente_id: parseInt(patientId as string),
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/assessments/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(assessmentData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Erro ao criar avaliação')
      }

      const assessment = await response.json()
      router.push(`/personal/patients/${patientId}`)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao criar avaliação')
    } finally {
      setIsLoading(false)
    }
  }

  if (!patient) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Nova Avaliação - {patient.nome} - Hypatium</title>
        <meta name="description" content="Criar nova avaliação física" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href={`/personal/patients/${patientId}`} className="text-blue-600 hover:text-blue-800">
                  ← Voltar para {patient.nome}
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <TrendingUp className="h-6 w-6 mr-2" />
                Nova Avaliação Física
              </h1>
              <div></div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Form */}
              <div className="lg:col-span-2">
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Dados Básicos */}
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="mb-6">
                      <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center">
                        <User className="h-5 w-5 mr-2" />
                        Dados Básicos
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Informações fundamentais da avaliação física.
                      </p>
                    </div>

                    {error && (
                      <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                        {error}
                      </div>
                    )}

                    <div className="grid grid-cols-6 gap-6">
                      <div className="col-span-6 sm:col-span-2">
                        <label htmlFor="peso" className="block text-sm font-medium text-gray-700">
                          Peso (kg) *
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Weight className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            {...register('peso', { valueAsNumber: true })}
                            type="number"
                            step="0.1"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="70.5"
                          />
                        </div>
                        {errors.peso && (
                          <p className="mt-1 text-sm text-red-600">{errors.peso.message}</p>
                        )}
                      </div>

                      <div className="col-span-6 sm:col-span-2">
                        <label htmlFor="altura" className="block text-sm font-medium text-gray-700">
                          Altura (m) *
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Ruler className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            {...register('altura', { valueAsNumber: true })}
                            type="number"
                            step="0.01"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="1.65"
                          />
                        </div>
                        {errors.altura && (
                          <p className="mt-1 text-sm text-red-600">{errors.altura.message}</p>
                        )}
                      </div>

                      <div className="col-span-6 sm:col-span-2">
                        <label htmlFor="data_avaliacao" className="block text-sm font-medium text-gray-700">
                          Data da Avaliação *
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Calendar className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            {...register('data_avaliacao')}
                            type="date"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        {errors.data_avaliacao && (
                          <p className="mt-1 text-sm text-red-600">{errors.data_avaliacao.message}</p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Dobras Cutâneas */}
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="mb-6">
                      <h3 className="text-lg font-medium leading-6 text-gray-900">
                        Dobras Cutâneas (mm)
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Medidas para cálculo do percentual de gordura corporal.
                      </p>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="metodo_utilizado" className="block text-sm font-medium text-gray-700">
                        Método de Cálculo
                      </label>
                      <select
                        {...register('metodo_utilizado')}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="pollock_3">Pollock 3 Dobras</option>
                        <option value="pollock_7">Pollock 7 Dobras</option>
                      </select>
                    </div>

                    <div className="grid grid-cols-6 gap-6">
                      {/* Dobras principais (Pollock 3) */}
                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Peitoral
                        </label>
                        <input
                          {...register('peitoral', { valueAsNumber: true })}
                          type="number"
                          step="0.1"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="12.5"
                        />
                      </div>

                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Abdominal
                        </label>
                        <input
                          {...register('abdominal', { valueAsNumber: true })}
                          type="number"
                          step="0.1"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="18.0"
                        />
                      </div>

                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Coxa
                        </label>
                        <input
                          {...register('coxa', { valueAsNumber: true })}
                          type="number"
                          step="0.1"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="15.5"
                        />
                      </div>

                      {/* Dobras adicionais (Pollock 7) */}
                      {watchedValues.metodo_utilizado === 'pollock_7' && (
                        <>
                          <div className="col-span-6 sm:col-span-2">
                            <label className="block text-sm font-medium text-gray-700">
                              Tríceps
                            </label>
                            <input
                              {...register('triceps', { valueAsNumber: true })}
                              type="number"
                              step="0.1"
                              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="14.0"
                            />
                          </div>

                          <div className="col-span-6 sm:col-span-2">
                            <label className="block text-sm font-medium text-gray-700">
                              Supra-ilíaca
                            </label>
                            <input
                              {...register('supra_iliaca', { valueAsNumber: true })}
                              type="number"
                              step="0.1"
                              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="16.0"
                            />
                          </div>

                          <div className="col-span-6 sm:col-span-2">
                            <label className="block text-sm font-medium text-gray-700">
                              Subescapular
                            </label>
                            <input
                              {...register('subescapular', { valueAsNumber: true })}
                              type="number"
                              step="0.1"
                              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="13.0"
                            />
                          </div>

                          <div className="col-span-6 sm:col-span-2">
                            <label className="block text-sm font-medium text-gray-700">
                              Axilar Média
                            </label>
                            <input
                              {...register('axilar_media', { valueAsNumber: true })}
                              type="number"
                              step="0.1"
                              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="11.0"
                            />
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Circunferências */}
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="mb-6">
                      <h3 className="text-lg font-medium leading-6 text-gray-900">
                        Circunferências (cm)
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Medidas de circunferências corporais (opcional).
                      </p>
                    </div>

                    <div className="grid grid-cols-6 gap-6">
                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Braço
                        </label>
                        <input
                          {...register('braco', { valueAsNumber: true })}
                          type="number"
                          step="0.1"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="32.0"
                        />
                      </div>

                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Cintura
                        </label>
                        <input
                          {...register('cintura', { valueAsNumber: true })}
                          type="number"
                          step="0.1"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="78.0"
                        />
                      </div>

                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Quadril
                        </label>
                        <input
                          {...register('quadril', { valueAsNumber: true })}
                          type="number"
                          step="0.1"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="95.0"
                        />
                      </div>

                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Coxa
                        </label>
                        <input
                          {...register('coxa_circ', { valueAsNumber: true })}
                          type="number"
                          step="0.1"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="58.0"
                        />
                      </div>

                      <div className="col-span-6 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Panturrilha
                        </label>
                        <input
                          {...register('panturrilha', { valueAsNumber: true })}
                          type="number"
                          step="0.1"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="36.0"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Observações */}
                  <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="mb-6">
                      <h3 className="text-lg font-medium leading-6 text-gray-900">
                        Observações
                      </h3>
                    </div>

                    <div>
                      <textarea
                        {...register('observacoes')}
                        rows={4}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Observações sobre a avaliação, condições especiais, etc..."
                      />
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-3">
                    <Link
                      href={`/personal/patients/${patientId}`}
                      className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancelar
                    </Link>
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      {isLoading ? (
                        <div className="loading-spinner mr-2"></div>
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      Salvar Avaliação
                    </button>
                  </div>
                </form>
              </div>

              {/* Preview/Calculations */}
              <div className="lg:col-span-1">
                <div className="bg-white shadow rounded-lg p-6 sticky top-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <Calculator className="h-5 w-5 mr-2" />
                    Cálculos em Tempo Real
                  </h3>

                  {calculations ? (
                    <div className="space-y-4">
                      <div className="text-center">
                        <p className="text-3xl font-bold text-blue-600">
                          {calculations.imc?.toFixed(1)}
                        </p>
                        <p className="text-sm text-gray-500">IMC</p>
                        <p className="text-xs text-gray-400">
                          {calculations.classificacao_imc}
                        </p>
                      </div>

                      {calculations.gordura_percentual && (
                        <div className="text-center">
                          <p className="text-2xl font-bold text-green-600">
                            {calculations.gordura_percentual?.toFixed(1)}%
                          </p>
                          <p className="text-sm text-gray-500">Gordura Corporal</p>
                          <p className="text-xs text-gray-400">
                            {calculations.classificacao_gordura}
                          </p>
                        </div>
                      )}

                      {calculations.massa_magra && (
                        <div className="grid grid-cols-2 gap-4 text-center">
                          <div>
                            <p className="text-lg font-bold text-red-600">
                              {calculations.massa_gorda?.toFixed(1)}kg
                            </p>
                            <p className="text-xs text-gray-500">Massa Gorda</p>
                          </div>
                          <div>
                            <p className="text-lg font-bold text-purple-600">
                              {calculations.massa_magra?.toFixed(1)}kg
                            </p>
                            <p className="text-xs text-gray-500">Massa Magra</p>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500">
                      <Info className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-sm">
                        Preencha peso e altura para ver os cálculos
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
