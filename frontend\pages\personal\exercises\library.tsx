import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  Du<PERSON>bell, 
  Plus, 
  Search, 
  Filter,
  Eye,
  Edit,
  Trash2,
  Play,
  Image,
  Tag,
  Grid,
  List
} from 'lucide-react'

interface Exercise {
  id: number
  nome: string
  grupo_muscular: string
  descricao?: string
  instrucoes?: string
  video_url?: string
  imagem_url?: string
  equipamento?: string
  nivel_dificuldade?: string
  created_at: string
}

const muscleGroups = [
  'Todos',
  'Peito',
  'Costas', 
  'Ombros',
  'Bíceps',
  'Tríceps',
  'Pernas',
  'Glúteos',
  'Abdômen',
  'Cardio',
  'Funcional'
]

const difficultyLevels = [
  'Todos',
  'Iniciante',
  'Intermediário',
  'Avançado'
]

export default function ExerciseLibraryPage() {
  const router = useRouter()
  const [exercises, setExercises] = useState<Exercise[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedMuscleGroup, setSelectedMuscleGroup] = useState('Todos')
  const [selectedDifficulty, setSelectedDifficulty] = useState('Todos')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  useEffect(() => {
    fetchExercises()
  }, [])

  const fetchExercises = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Por enquanto, vamos criar exercícios de exemplo
      // TODO: Implementar endpoint real no backend
      const mockExercises: Exercise[] = [
        {
          id: 1,
          nome: 'Supino Reto',
          grupo_muscular: 'Peito',
          descricao: 'Exercício fundamental para desenvolvimento do peitoral',
          instrucoes: 'Deite no banco, segure a barra com pegada pronada, desça controladamente até o peito e empurre para cima.',
          video_url: 'https://example.com/supino-reto.mp4',
          equipamento: 'Barra, anilhas, banco',
          nivel_dificuldade: 'Intermediário',
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          nome: 'Agachamento Livre',
          grupo_muscular: 'Pernas',
          descricao: 'Exercício composto para pernas e glúteos',
          instrucoes: 'Posicione a barra nos ombros, desça flexionando joelhos e quadril, mantenha o tronco ereto.',
          equipamento: 'Barra, anilhas, rack',
          nivel_dificuldade: 'Avançado',
          created_at: new Date().toISOString()
        },
        {
          id: 3,
          nome: 'Flexão de Braço',
          grupo_muscular: 'Peito',
          descricao: 'Exercício de peso corporal para peito, ombros e tríceps',
          instrucoes: 'Apoie mãos e pés no chão, mantenha corpo alinhado, desça e suba controladamente.',
          equipamento: 'Peso corporal',
          nivel_dificuldade: 'Iniciante',
          created_at: new Date().toISOString()
        },
        {
          id: 4,
          nome: 'Remada Curvada',
          grupo_muscular: 'Costas',
          descricao: 'Exercício para desenvolvimento das costas',
          instrucoes: 'Incline o tronco, segure a barra, puxe em direção ao abdômen.',
          equipamento: 'Barra, anilhas',
          nivel_dificuldade: 'Intermediário',
          created_at: new Date().toISOString()
        },
        {
          id: 5,
          nome: 'Desenvolvimento com Halteres',
          grupo_muscular: 'Ombros',
          descricao: 'Exercício para deltoides',
          instrucoes: 'Sentado ou em pé, empurre os halteres acima da cabeça.',
          equipamento: 'Halteres, banco',
          nivel_dificuldade: 'Intermediário',
          created_at: new Date().toISOString()
        },
        {
          id: 6,
          nome: 'Prancha',
          grupo_muscular: 'Abdômen',
          descricao: 'Exercício isométrico para core',
          instrucoes: 'Apoie antebraços e pés, mantenha corpo alinhado.',
          equipamento: 'Peso corporal',
          nivel_dificuldade: 'Iniciante',
          created_at: new Date().toISOString()
        }
      ]

      setExercises(mockExercises)
    } catch (error) {
      console.error('Erro ao buscar exercícios:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredExercises = exercises.filter(exercise => {
    const matchesSearch = exercise.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         exercise.descricao?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesMuscleGroup = selectedMuscleGroup === 'Todos' || exercise.grupo_muscular === selectedMuscleGroup
    const matchesDifficulty = selectedDifficulty === 'Todos' || exercise.nivel_dificuldade === selectedDifficulty
    
    return matchesSearch && matchesMuscleGroup && matchesDifficulty
  })

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'Iniciante':
        return 'bg-green-100 text-green-800'
      case 'Intermediário':
        return 'bg-yellow-100 text-yellow-800'
      case 'Avançado':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getMuscleGroupColor = (group: string) => {
    const colors = {
      'Peito': 'bg-blue-100 text-blue-800',
      'Costas': 'bg-green-100 text-green-800',
      'Ombros': 'bg-purple-100 text-purple-800',
      'Bíceps': 'bg-pink-100 text-pink-800',
      'Tríceps': 'bg-indigo-100 text-indigo-800',
      'Pernas': 'bg-orange-100 text-orange-800',
      'Glúteos': 'bg-red-100 text-red-800',
      'Abdômen': 'bg-yellow-100 text-yellow-800',
      'Cardio': 'bg-teal-100 text-teal-800',
      'Funcional': 'bg-gray-100 text-gray-800'
    }
    return colors[group as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Biblioteca de Exercícios - Hypatium</title>
        <meta name="description" content="Biblioteca completa de exercícios" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href="/dashboard" className="text-blue-600 hover:text-blue-800">
                  ← Voltar ao Dashboard
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Dumbbell className="h-6 w-6 mr-2" />
                Biblioteca de Exercícios
              </h1>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
                <Plus className="h-4 w-4 mr-2" />
                Novo Exercício
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Filters */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="Buscar exercícios..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="flex items-center">
                    <Filter className="h-4 w-4 mr-2 text-gray-500" />
                    <select
                      value={selectedMuscleGroup}
                      onChange={(e) => setSelectedMuscleGroup(e.target.value)}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                    >
                      {muscleGroups.map(group => (
                        <option key={group} value={group}>{group}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="flex items-center">
                    <select
                      value={selectedDifficulty}
                      onChange={(e) => setSelectedDifficulty(e.target.value)}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                    >
                      {difficultyLevels.map(level => (
                        <option key={level} value={level}>{level}</option>
                      ))}
                    </select>
                  </div>

                  <div className="flex items-center border border-gray-300 rounded-md">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-500'}`}
                    >
                      <Grid className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-500'}`}
                    >
                      <List className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Dumbbell className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total de Exercícios</p>
                    <p className="text-2xl font-bold text-gray-900">{exercises.length}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Tag className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Grupos Musculares</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {new Set(exercises.map(e => e.grupo_muscular)).size}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Play className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Com Vídeo</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {exercises.filter(e => e.video_url).length}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Search className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Filtrados</p>
                    <p className="text-2xl font-bold text-gray-900">{filteredExercises.length}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Exercises Grid/List */}
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredExercises.map((exercise) => (
                  <div key={exercise.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                    {/* Exercise Image/Video Placeholder */}
                    <div className="h-48 bg-gray-200 rounded-t-lg flex items-center justify-center">
                      {exercise.video_url ? (
                        <Play className="h-12 w-12 text-gray-400" />
                      ) : (
                        <Image className="h-12 w-12 text-gray-400" />
                      )}
                    </div>
                    
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{exercise.nome}</h3>
                        <div className="flex space-x-1">
                          <button className="text-blue-600 hover:text-blue-800">
                            <Eye className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-800">
                            <Edit className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mb-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getMuscleGroupColor(exercise.grupo_muscular)}`}>
                          {exercise.grupo_muscular}
                        </span>
                        {exercise.nivel_dificuldade && (
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getDifficultyColor(exercise.nivel_dificuldade)}`}>
                            {exercise.nivel_dificuldade}
                          </span>
                        )}
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {exercise.descricao}
                      </p>
                      
                      {exercise.equipamento && (
                        <p className="text-xs text-gray-500">
                          <strong>Equipamento:</strong> {exercise.equipamento}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <ul className="divide-y divide-gray-200">
                  {filteredExercises.map((exercise) => (
                    <li key={exercise.id}>
                      <div className="px-6 py-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="flex-shrink-0">
                              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                <Dumbbell className="h-5 w-5 text-blue-600" />
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="flex items-center">
                                <p className="text-sm font-medium text-gray-900">{exercise.nome}</p>
                                <div className="ml-2 flex space-x-1">
                                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getMuscleGroupColor(exercise.grupo_muscular)}`}>
                                    {exercise.grupo_muscular}
                                  </span>
                                  {exercise.nivel_dificuldade && (
                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getDifficultyColor(exercise.nivel_dificuldade)}`}>
                                      {exercise.nivel_dificuldade}
                                    </span>
                                  )}
                                  {exercise.video_url && (
                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                      <Play className="h-3 w-3 mr-1" />
                                      Vídeo
                                    </span>
                                  )}
                                </div>
                              </div>
                              <p className="text-sm text-gray-600 mt-1">
                                {exercise.descricao}
                              </p>
                              {exercise.equipamento && (
                                <p className="text-xs text-gray-500 mt-1">
                                  <strong>Equipamento:</strong> {exercise.equipamento}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button className="text-blue-600 hover:text-blue-900">
                              <Eye className="h-4 w-4" />
                            </button>
                            <button className="text-gray-600 hover:text-gray-900">
                              <Edit className="h-4 w-4" />
                            </button>
                            <button className="text-red-600 hover:text-red-900">
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {filteredExercises.length === 0 && (
              <div className="bg-white p-12 rounded-lg shadow text-center">
                <Dumbbell className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum exercício encontrado</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || selectedMuscleGroup !== 'Todos' || selectedDifficulty !== 'Todos'
                    ? 'Tente ajustar os filtros de busca.'
                    : 'Comece adicionando exercícios à biblioteca.'}
                </p>
                <div className="mt-6">
                  <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Adicionar Exercício
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}
