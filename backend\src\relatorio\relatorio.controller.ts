import {
  Controller,
  Get,
  Post,
  Param,
  UseGuards,
  Request
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RelatorioService } from './relatorio.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('relatorios')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('relatorios')
export class RelatorioController {
  constructor(private readonly relatorioService: RelatorioService) {}

  @ApiOperation({ summary: 'Gerar relatório de avaliação física (apenas Personal Trainer)' })
  @ApiResponse({ status: 201, description: 'Relatório gerado com sucesso' })
  @ApiResponse({ status: 404, description: 'Avaliação não encontrada' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Roles('personal')
  @Post('avaliacao/:avaliacaoId')
  gerarRelatorio(@Param('avaliacaoId') avaliacaoId: string, @Request() req) {
    return this.relatorioService.gerarRelatorio(avaliacaoId, req.user.id);
  }

  @ApiOperation({ summary: 'Gerar relatório de Volume Load (apenas Personal Trainer)' })
  @ApiResponse({ status: 200, description: 'Relatório de Volume Load gerado' })
  @ApiResponse({ status: 404, description: 'Avaliação não encontrada' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Roles('personal')
  @Post('volume-load/:avaliacaoId')
  gerarRelatorioVolumeLoad(@Param('avaliacaoId') avaliacaoId: string, @Request() req) {
    return this.relatorioService.gerarRelatorioVolumeLoad(avaliacaoId, req.user.id);
  }

  @ApiOperation({ summary: 'Buscar relatórios por aluno' })
  @ApiResponse({ status: 200, description: 'Relatórios encontrados' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Get('aluno/:alunoId')
  findByAluno(@Param('alunoId') alunoId: string, @Request() req) {
    const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
    return this.relatorioService.findRelatoriosByAluno(alunoId, personalId);
  }

  @ApiOperation({ summary: 'Buscar relatório por ID' })
  @ApiResponse({ status: 200, description: 'Relatório encontrado' })
  @ApiResponse({ status: 404, description: 'Relatório não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    const personalId = req.user.userType === 'personal' ? req.user.id : undefined;
    return this.relatorioService.findRelatorioById(id, personalId);
  }
}
