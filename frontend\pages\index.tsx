import Head from 'next/head'
import Link from 'next/link'
import { Users, Brain, Utensils, FileText, ArrowRight } from 'lucide-react'
import Logo from '../components/Logo'

export default function Home() {
  return (
    <>
      <Head>
        <title>Hypatium - Plataforma Integrada para Profissionais de Saúde</title>
        <meta name="description" content="Plataforma completa para personal trainers, nutricionistas e psicólogos" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <Logo />
              <div className="flex space-x-4">
                <Link href="/login" className="text-gray-600 hover:text-gray-900">
                  Login
                </Link>
                <Link href="/register" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                  Cadastrar
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              Plataforma Integrada para
              <span className="text-blue-600"> Profissionais de Saúde</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Una personal trainers, nutricionistas e psicólogos em uma única plataforma.
              Gerencie pacientes, crie planos personalizados e gere relatórios automáticos com IA.
            </p>
            <div className="flex justify-center space-x-4">
              <Link href="/register" className="bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 flex items-center">
                Começar Agora
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <Link href="/demo" className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-50">
                Ver Demo
              </Link>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Tudo que você precisa em um só lugar
              </h2>
              <p className="text-lg text-gray-600">
                Ferramentas especializadas para cada área da saúde
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Personal Trainer */}
              <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                <Users className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Personal Trainer</h3>
                <p className="text-gray-600">
                  Avaliações físicas, treinos com vídeo, acompanhamento de progresso
                </p>
              </div>

              {/* Nutricionista */}
              <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                <Utensils className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Nutricionista</h3>
                <p className="text-gray-600">
                  Planos alimentares, cálculo de macros, PDFs automáticos
                </p>
              </div>

              {/* Psicólogo */}
              <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                <Brain className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Psicólogo</h3>
                <p className="text-gray-600">
                  Sessões, anotações, gravações com transcrição automática
                </p>
              </div>

              {/* Relatórios IA */}
              <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                <FileText className="h-12 w-12 text-orange-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Relatórios IA</h3>
                <p className="text-gray-600">
                  Análises integradas geradas automaticamente por inteligência artificial
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-white py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="flex justify-center mb-4">
              <Logo textClassName="ml-2 text-2xl font-bold text-white" />
            </div>
            <p className="text-gray-400">
              © 2024 Hypatium. Todos os direitos reservados.
            </p>
          </div>
        </footer>
      </main>
    </>
  )
}
