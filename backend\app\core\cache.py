"""
Sistema de cache com Redis
"""

import json
import pickle
from typing import Any, Optional, Union
from datetime import datetime, timedelta
import os

# Placeholder para Redis - será implementado quando Redis estiver disponível
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

class CacheService:
    """Serviço de cache com fallback para memória local"""
    
    def __init__(self):
        self.redis_client = None
        self.local_cache = {}
        self.cache_ttl = {}
        
        # Tentar conectar ao Redis
        if REDIS_AVAILABLE:
            try:
                redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
                self.redis_client = redis.from_url(redis_url, decode_responses=True)
                # Testar conexão
                self.redis_client.ping()
                print("✅ Redis conectado com sucesso")
            except Exception as e:
                print(f"⚠️ Redis não disponível, usando cache local: {str(e)}")
                self.redis_client = None
        else:
            print("⚠️ Redis não instalado, usando cache local")
    
    def _serialize_value(self, value: Any) -> str:
        """Serializar valor para armazenamento"""
        try:
            # Tentar JSON primeiro (mais eficiente)
            return json.dumps(value)
        except (TypeError, ValueError):
            # Fallback para pickle
            return pickle.dumps(value).hex()
    
    def _deserialize_value(self, value: str) -> Any:
        """Deserializar valor do armazenamento"""
        try:
            # Tentar JSON primeiro
            return json.loads(value)
        except (json.JSONDecodeError, ValueError):
            # Fallback para pickle
            try:
                return pickle.loads(bytes.fromhex(value))
            except:
                return value
    
    def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """Definir valor no cache"""
        try:
            serialized_value = self._serialize_value(value)
            
            if self.redis_client:
                # Usar Redis
                return self.redis_client.setex(key, ttl, serialized_value)
            else:
                # Usar cache local
                self.local_cache[key] = serialized_value
                self.cache_ttl[key] = datetime.utcnow() + timedelta(seconds=ttl)
                return True
                
        except Exception as e:
            print(f"Erro ao definir cache {key}: {str(e)}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """Obter valor do cache"""
        try:
            if self.redis_client:
                # Usar Redis
                value = self.redis_client.get(key)
                if value is not None:
                    return self._deserialize_value(value)
                return None
            else:
                # Usar cache local
                if key in self.local_cache:
                    # Verificar TTL
                    if key in self.cache_ttl and datetime.utcnow() > self.cache_ttl[key]:
                        # Expirado
                        del self.local_cache[key]
                        del self.cache_ttl[key]
                        return None
                    
                    return self._deserialize_value(self.local_cache[key])
                return None
                
        except Exception as e:
            print(f"Erro ao obter cache {key}: {str(e)}")
            return None
    
    def delete(self, key: str) -> bool:
        """Deletar valor do cache"""
        try:
            if self.redis_client:
                # Usar Redis
                return bool(self.redis_client.delete(key))
            else:
                # Usar cache local
                if key in self.local_cache:
                    del self.local_cache[key]
                if key in self.cache_ttl:
                    del self.cache_ttl[key]
                return True
                
        except Exception as e:
            print(f"Erro ao deletar cache {key}: {str(e)}")
            return False
    
    def exists(self, key: str) -> bool:
        """Verificar se chave existe no cache"""
        try:
            if self.redis_client:
                return bool(self.redis_client.exists(key))
            else:
                if key in self.local_cache:
                    # Verificar TTL
                    if key in self.cache_ttl and datetime.utcnow() > self.cache_ttl[key]:
                        del self.local_cache[key]
                        del self.cache_ttl[key]
                        return False
                    return True
                return False
                
        except Exception as e:
            print(f"Erro ao verificar cache {key}: {str(e)}")
            return False
    
    def clear(self) -> bool:
        """Limpar todo o cache"""
        try:
            if self.redis_client:
                return bool(self.redis_client.flushdb())
            else:
                self.local_cache.clear()
                self.cache_ttl.clear()
                return True
                
        except Exception as e:
            print(f"Erro ao limpar cache: {str(e)}")
            return False
    
    def get_stats(self) -> dict:
        """Obter estatísticas do cache"""
        try:
            if self.redis_client:
                info = self.redis_client.info()
                return {
                    "type": "redis",
                    "connected": True,
                    "keys": self.redis_client.dbsize(),
                    "memory_used": info.get("used_memory_human", "N/A"),
                    "hits": info.get("keyspace_hits", 0),
                    "misses": info.get("keyspace_misses", 0)
                }
            else:
                # Limpar chaves expiradas
                current_time = datetime.utcnow()
                expired_keys = [
                    key for key, ttl in self.cache_ttl.items()
                    if current_time > ttl
                ]
                for key in expired_keys:
                    if key in self.local_cache:
                        del self.local_cache[key]
                    del self.cache_ttl[key]
                
                return {
                    "type": "local",
                    "connected": True,
                    "keys": len(self.local_cache),
                    "memory_used": "N/A",
                    "hits": "N/A",
                    "misses": "N/A"
                }
                
        except Exception as e:
            return {
                "type": "error",
                "connected": False,
                "error": str(e)
            }

# Instância global do cache
cache = CacheService()

# Decorador para cache de funções
def cached(ttl: int = 3600, key_prefix: str = ""):
    """Decorador para cache automático de funções"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Gerar chave do cache
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Tentar obter do cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Executar função e cachear resultado
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            return result
        
        return wrapper
    return decorator

# Funções de conveniência para cache específico
class PatientCache:
    """Cache específico para dados de pacientes"""
    
    @staticmethod
    def get_patient(patient_id: int) -> Optional[dict]:
        """Obter dados do paciente do cache"""
        return cache.get(f"patient:{patient_id}")
    
    @staticmethod
    def set_patient(patient_id: int, patient_data: dict, ttl: int = 1800) -> bool:
        """Armazenar dados do paciente no cache"""
        return cache.set(f"patient:{patient_id}", patient_data, ttl)
    
    @staticmethod
    def invalidate_patient(patient_id: int) -> bool:
        """Invalidar cache do paciente"""
        return cache.delete(f"patient:{patient_id}")

class AssessmentCache:
    """Cache específico para avaliações"""
    
    @staticmethod
    def get_patient_assessments(patient_id: int) -> Optional[list]:
        """Obter avaliações do paciente do cache"""
        return cache.get(f"assessments:patient:{patient_id}")
    
    @staticmethod
    def set_patient_assessments(patient_id: int, assessments: list, ttl: int = 900) -> bool:
        """Armazenar avaliações do paciente no cache"""
        return cache.set(f"assessments:patient:{patient_id}", assessments, ttl)
    
    @staticmethod
    def invalidate_patient_assessments(patient_id: int) -> bool:
        """Invalidar cache de avaliações do paciente"""
        return cache.delete(f"assessments:patient:{patient_id}")

class ReportCache:
    """Cache específico para relatórios"""
    
    @staticmethod
    def get_report(report_id: int) -> Optional[dict]:
        """Obter relatório do cache"""
        return cache.get(f"report:{report_id}")
    
    @staticmethod
    def set_report(report_id: int, report_data: dict, ttl: int = 3600) -> bool:
        """Armazenar relatório no cache"""
        return cache.set(f"report:{report_id}", report_data, ttl)
    
    @staticmethod
    def invalidate_report(report_id: int) -> bool:
        """Invalidar cache do relatório"""
        return cache.delete(f"report:{report_id}")

class StatsCache:
    """Cache específico para estatísticas"""
    
    @staticmethod
    def get_user_stats(user_id: int) -> Optional[dict]:
        """Obter estatísticas do usuário do cache"""
        return cache.get(f"stats:user:{user_id}")
    
    @staticmethod
    def set_user_stats(user_id: int, stats: dict, ttl: int = 1800) -> bool:
        """Armazenar estatísticas do usuário no cache"""
        return cache.set(f"stats:user:{user_id}", stats, ttl)
    
    @staticmethod
    def invalidate_user_stats(user_id: int) -> bool:
        """Invalidar cache de estatísticas do usuário"""
        return cache.delete(f"stats:user:{user_id}")

# Função para limpar caches relacionados
def invalidate_patient_related_caches(patient_id: int):
    """Invalidar todos os caches relacionados a um paciente"""
    PatientCache.invalidate_patient(patient_id)
    AssessmentCache.invalidate_patient_assessments(patient_id)
    # Adicionar outros caches relacionados conforme necessário

def invalidate_user_related_caches(user_id: int):
    """Invalidar todos os caches relacionados a um usuário"""
    StatsCache.invalidate_user_stats(user_id)
    # Adicionar outros caches relacionados conforme necessário
