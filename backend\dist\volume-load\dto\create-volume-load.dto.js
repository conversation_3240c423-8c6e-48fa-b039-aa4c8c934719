"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVolumeLoadDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class CreateVolumeLoadDto {
    avaliacaoComposicaoId;
    exercicio;
    semana;
    serie1Repeticoes;
    serie1Carga;
    serie2Repeticoes;
    serie2Carga;
    serie3Repeticoes;
    serie3Carga;
    serie4Repeticoes;
    serie4Carga;
    serie5Repeticoes;
    serie5Carga;
    serie6Repeticoes;
    serie6Carga;
    serie7Repeticoes;
    serie7Carga;
    serie8Repeticoes;
    serie8Carga;
    serie9Repeticoes;
    serie9Carga;
    serie10Repeticoes;
    serie10Carga;
}
exports.CreateVolumeLoadDto = CreateVolumeLoadDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da avaliação de composição',
        example: 'clp123abc456',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateVolumeLoadDto.prototype, "avaliacaoComposicaoId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número do exercício (1-15)',
        example: 1,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(15),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "exercicio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número da semana (1-8)',
        example: 1,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(8),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "semana", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Repetições da série 1',
        example: 12,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie1Repeticoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Carga da série 1 em kg',
        example: 50.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie1Carga", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Repetições da série 2',
        example: 10,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie2Repeticoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Carga da série 2 em kg',
        example: 52.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie2Carga", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Repetições da série 3',
        example: 8,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie3Repeticoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Carga da série 3 em kg',
        example: 55.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie3Carga", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Repetições da série 4',
        example: 8,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie4Repeticoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Carga da série 4 em kg',
        example: 55.0,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie4Carga", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Repetições da série 5',
        example: 6,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie5Repeticoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Carga da série 5 em kg',
        example: 57.5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie5Carga", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Repetições da série 6',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie6Repeticoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Carga da série 6 em kg',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie6Carga", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Repetições da série 7',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie7Repeticoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Carga da série 7 em kg',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie7Carga", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Repetições da série 8',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie8Repeticoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Carga da série 8 em kg',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie8Carga", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Repetições da série 9',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie9Repeticoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Carga da série 9 em kg',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie9Carga", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Repetições da série 10',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie10Repeticoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Carga da série 10 em kg',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1000),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseFloat(value) : undefined),
    __metadata("design:type", Number)
], CreateVolumeLoadDto.prototype, "serie10Carga", void 0);
//# sourceMappingURL=create-volume-load.dto.js.map