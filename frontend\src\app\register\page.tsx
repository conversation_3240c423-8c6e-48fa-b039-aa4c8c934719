'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    nome: '',
    email: '',
    telefone: '',
    senha: '',
    confirmarSenha: '',
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    // Limpar erro quando o usuário começar a digitar
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.nome.trim()) {
      newErrors.nome = 'Nome é obrigatório';
    }

    if (!formData.email) {
      newErrors.email = 'Email é obrigatório';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (!formData.senha) {
      newErrors.senha = 'Senha é obrigatória';
    } else if (formData.senha.length < 6) {
      newErrors.senha = 'Senha deve ter pelo menos 6 caracteres';
    }

    if (!formData.confirmarSenha) {
      newErrors.confirmarSenha = 'Confirmação de senha é obrigatória';
    } else if (formData.senha !== formData.confirmarSenha) {
      newErrors.confirmarSenha = 'Senhas não coincidem';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    try {
      // TODO: Implementar chamada para API de registro
      console.log('Register data:', formData);
      
      // Simular delay da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // TODO: Redirecionar para login ou dashboard
      alert('Cadastro realizado com sucesso!');
    } catch (error) {
      console.error('Erro no cadastro:', error);
      setErrors({ general: 'Erro ao fazer cadastro. Tente novamente.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#E8F8F5] to-[#D1F2EB] p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-[#333333] mb-2">
            Hypatium Core
          </h1>
          <p className="text-gray-600">
            Cadastre-se como Personal Trainer
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-center text-[#333333]">
              Criar sua conta
            </CardTitle>
            <CardDescription className="text-center">
              Preencha os dados para se cadastrar no sistema
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {errors.general && (
                <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-lg">
                  {errors.general}
                </div>
              )}

              <Input
                label="Nome completo"
                type="text"
                name="nome"
                value={formData.nome}
                onChange={handleInputChange}
                error={errors.nome}
                placeholder="Seu nome completo"
                required
              />

              <Input
                label="Email"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                error={errors.email}
                placeholder="<EMAIL>"
                required
              />

              <Input
                label="Telefone"
                type="tel"
                name="telefone"
                value={formData.telefone}
                onChange={handleInputChange}
                error={errors.telefone}
                placeholder="(11) 99999-9999"
              />

              <Input
                label="Senha"
                type="password"
                name="senha"
                value={formData.senha}
                onChange={handleInputChange}
                error={errors.senha}
                placeholder="••••••••"
                helperText="Mínimo de 6 caracteres"
                required
              />

              <Input
                label="Confirmar senha"
                type="password"
                name="confirmarSenha"
                value={formData.confirmarSenha}
                onChange={handleInputChange}
                error={errors.confirmarSenha}
                placeholder="••••••••"
                required
              />

              <Button
                type="submit"
                className="w-full"
                loading={loading}
                disabled={loading}
              >
                {loading ? 'Cadastrando...' : 'Criar conta'}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Já tem uma conta?{' '}
                <a
                  href="/login"
                  className="font-medium text-[#1ABC9C] hover:text-[#17A085] transition-colors"
                >
                  Faça login aqui
                </a>
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="mt-8 text-center text-sm text-gray-500">
          <p>© 2024 Hypatium Core. Todos os direitos reservados.</p>
        </div>
      </div>
    </div>
  );
}
