"""
Schemas Pydantic para nutrição
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime

class NutritionalPlanBase(BaseModel):
    """Schema base para plano nutricional"""
    data_criacao: datetime
    tmb_calculado: Optional[float] = Field(None, gt=0)
    get_calculado: Optional[float] = Field(None, gt=0)
    superavit_deficit_percentual: Optional[float] = Field(None, ge=-50, le=50)
    calorias_alvo: Optional[float] = Field(None, gt=0)
    proteinas_g: Optional[float] = Field(None, ge=0)
    carboidratos_g: Optional[float] = Field(None, ge=0)
    gorduras_g: Optional[float] = Field(None, ge=0)
    plano_alimentar: Optional[Dict[str, Any]] = None
    pdf_url: Optional[str] = None
    observacoes: Optional[str] = None
    ativo: bool = True

class NutritionalPlanCreate(NutritionalPlanBase):
    """Schema para criação de plano nutricional"""
    paciente_id: int
    nutricionista_id: int

class NutritionalPlanUpdate(BaseModel):
    """Schema para atualização de plano nutricional"""
    tmb_calculado: Optional[float] = Field(None, gt=0)
    get_calculado: Optional[float] = Field(None, gt=0)
    superavit_deficit_percentual: Optional[float] = Field(None, ge=-50, le=50)
    calorias_alvo: Optional[float] = Field(None, gt=0)
    proteinas_g: Optional[float] = Field(None, ge=0)
    carboidratos_g: Optional[float] = Field(None, ge=0)
    gorduras_g: Optional[float] = Field(None, ge=0)
    plano_alimentar: Optional[Dict[str, Any]] = None
    observacoes: Optional[str] = None
    ativo: Optional[bool] = None

class NutritionalPlan(NutritionalPlanBase):
    """Schema para resposta de plano nutricional"""
    id: int
    paciente_id: int
    nutricionista_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Schemas para cálculos nutricionais
class NutritionalCalculation(BaseModel):
    """Schema para cálculos nutricionais"""
    peso: float = Field(..., gt=0)
    altura: float = Field(..., gt=0)
    idade: int = Field(..., gt=0, le=120)
    sexo: str = Field(..., pattern="^(masculino|feminino)$")
    nivel_atividade: str = Field(
        default="sedentario",
        pattern="^(sedentario|leve|moderado|intenso|muito_intenso)$"
    )
    objetivo: str = Field(
        default="manutencao",
        pattern="^(deficit|manutencao|superavit)$"
    )
    percentual_ajuste: Optional[float] = Field(None, ge=-50, le=50)

class MacronutrientDistribution(BaseModel):
    """Schema para distribuição de macronutrientes"""
    calorias_totais: float
    proteinas_percentual: float = Field(default=25, ge=10, le=40)
    carboidratos_percentual: float = Field(default=45, ge=20, le=65)
    gorduras_percentual: float = Field(default=30, ge=15, le=40)

class FoodItem(BaseModel):
    """Schema para item alimentar"""
    nome: str
    quantidade: float
    unidade: str  # g, ml, unidade, etc.
    calorias_por_100g: float
    proteinas_por_100g: float
    carboidratos_por_100g: float
    gorduras_por_100g: float

class Meal(BaseModel):
    """Schema para refeição"""
    nome: str  # café da manhã, almoço, etc.
    horario: str
    alimentos: List[FoodItem]
    calorias_total: Optional[float] = None
    proteinas_total: Optional[float] = None
    carboidratos_total: Optional[float] = None
    gorduras_total: Optional[float] = None

class DailyMealPlan(BaseModel):
    """Schema para plano alimentar diário"""
    dia: str
    refeicoes: List[Meal]
    calorias_dia: Optional[float] = None
    proteinas_dia: Optional[float] = None
    carboidratos_dia: Optional[float] = None
    gorduras_dia: Optional[float] = None

class WeeklyMealPlan(BaseModel):
    """Schema para plano alimentar semanal"""
    semana: int
    dias: List[DailyMealPlan]
    observacoes: Optional[str] = None

class NutritionalReport(BaseModel):
    """Schema para relatório nutricional"""
    paciente_id: int
    periodo_inicio: datetime
    periodo_fim: datetime
    peso_inicial: Optional[float] = None
    peso_final: Optional[float] = None
    variacao_peso: Optional[float] = None
    aderencia_plano: Optional[float] = None  # percentual de aderência
    observacoes: Optional[str] = None
