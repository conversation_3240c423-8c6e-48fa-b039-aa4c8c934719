import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAlunoDto } from './dto/create-aluno.dto';
import { UpdateAlunoDto } from './dto/update-aluno.dto';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class AlunoService {
  constructor(private prisma: PrismaService) {}

  async create(createAlunoDto: CreateAlunoDto, personalId: string) {
    const hashedPassword = await bcrypt.hash(createAlunoDto.senha, 10);

    const aluno = await this.prisma.aluno.create({
      data: {
        ...createAlunoDto,
        senha: hashedPassword,
        personalId,
      },
    });

    const { senha, ...result } = aluno;
    return result;
  }

  async findAllByPersonal(personalId: string) {
    return this.prisma.aluno.findMany({
      where: { personalId },
      select: {
        id: true,
        nome: true,
        email: true,
        telefone: true,
        pesoAtual: true,
        estatura: true,
        idade: true,
        dataNascimento: true,
        habitos: true,
        objetivos: true,
        dataCadastro: true,
        _count: {
          select: {
            avaliacoes: true,
          },
        },
      },
      orderBy: {
        dataCadastro: 'desc',
      },
    });
  }

  async findOne(id: string, personalId?: string) {
    const aluno = await this.prisma.aluno.findUnique({
      where: { id },
      include: {
        personal: {
          select: {
            id: true,
            nome: true,
            email: true,
          },
        },
        avaliacoes: {
          orderBy: {
            dataAvaliacao: 'desc',
          },
          take: 5,
          select: {
            id: true,
            dataAvaliacao: true,
            valorIMC: true,
            categoriaIMC: true,
            guedesPercentual: true,
            pollock3Percentual: true,
            pollock7Percentual: true,
          },
        },
        _count: {
          select: {
            avaliacoes: true,
          },
        },
      },
    });

    if (!aluno) {
      throw new NotFoundException('Aluno não encontrado');
    }

    // Verificar se o personal tem acesso a este aluno
    if (personalId && aluno.personalId !== personalId) {
      throw new ForbiddenException('Acesso negado a este aluno');
    }

    const { senha, ...result } = aluno;
    return result;
  }

  async update(id: string, updateAlunoDto: UpdateAlunoDto, personalId?: string) {
    // Verificar se o aluno existe e se o personal tem acesso
    const aluno = await this.findOne(id, personalId);

    const updateData: any = { ...updateAlunoDto };

    // Se a senha foi fornecida, fazer hash
    if (updateAlunoDto.senha) {
      updateData.senha = await bcrypt.hash(updateAlunoDto.senha, 10);
    }

    const updatedAluno = await this.prisma.aluno.update({
      where: { id },
      data: updateData,
    });

    const { senha, ...result } = updatedAluno;
    return result;
  }

  async remove(id: string, personalId: string) {
    // Verificar se o aluno existe e se o personal tem acesso
    await this.findOne(id, personalId);

    await this.prisma.aluno.delete({
      where: { id },
    });

    return { message: 'Aluno removido com sucesso' };
  }

  async getAlunoStats(id: string, personalId?: string) {
    // Verificar se o aluno existe e se o personal tem acesso
    await this.findOne(id, personalId);

    const [totalAvaliacoes, ultimaAvaliacao, primeiraAvaliacao] = await Promise.all([
      this.prisma.avaliacaoComposicao.count({
        where: { alunoId: id },
      }),
      this.prisma.avaliacaoComposicao.findFirst({
        where: { alunoId: id },
        orderBy: { dataAvaliacao: 'desc' },
        select: {
          id: true,
          dataAvaliacao: true,
          valorIMC: true,
          categoriaIMC: true,
          guedesPercentual: true,
          pollock3Percentual: true,
          pollock7Percentual: true,
        },
      }),
      this.prisma.avaliacaoComposicao.findFirst({
        where: { alunoId: id },
        orderBy: { dataAvaliacao: 'asc' },
        select: {
          id: true,
          dataAvaliacao: true,
          valorIMC: true,
          categoriaIMC: true,
          guedesPercentual: true,
          pollock3Percentual: true,
          pollock7Percentual: true,
        },
      }),
    ]);

    return {
      totalAvaliacoes,
      ultimaAvaliacao,
      primeiraAvaliacao,
      evolucao: primeiraAvaliacao && ultimaAvaliacao ? {
        imc: {
          inicial: primeiraAvaliacao.valorIMC,
          atual: ultimaAvaliacao.valorIMC,
          diferenca: ultimaAvaliacao.valorIMC ?
            (ultimaAvaliacao.valorIMC - (primeiraAvaliacao.valorIMC || 0)) : null,
        },
        gordura: {
          inicial: primeiraAvaliacao.guedesPercentual || primeiraAvaliacao.pollock3Percentual || primeiraAvaliacao.pollock7Percentual,
          atual: ultimaAvaliacao.guedesPercentual || ultimaAvaliacao.pollock3Percentual || ultimaAvaliacao.pollock7Percentual,
        },
      } : null,
    };
  }
}
