import { RelatorioService } from './relatorio.service';
export declare class RelatorioController {
    private readonly relatorioService;
    constructor(relatorioService: RelatorioService);
    gerarRelatorio(avaliacaoId: string, req: any): Promise<any>;
    gerarRelatorioVolumeLoad(avaliacaoId: string, req: any): Promise<{
        aluno: any;
        analise: string;
        dadosVolumeLoad: unknown[];
        graficos: {
            vlPorExercicio: {
                exercicio: string;
                vlTotal: any;
            }[];
            progressaoPorSemana: any;
        };
    }>;
    findByAluno(alunoId: string, req: any): Promise<any>;
    findOne(id: string, req: any): Promise<any>;
}
