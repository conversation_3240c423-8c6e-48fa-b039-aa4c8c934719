import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  Bell, 
  Mail, 
  Smartphone,
  Calendar,
  Activity,
  Utensils,
  FileText,
  TrendingUp,
  Save,
  TestTube
} from 'lucide-react'

interface NotificationPreferences {
  email_enabled: boolean
  push_enabled: boolean
  appointment_reminders: boolean
  workout_reminders: boolean
  nutrition_reminders: boolean
  progress_updates: boolean
  report_notifications: boolean
  reminder_hours_before: number
}

interface NotificationStatus {
  email_service: {
    enabled: boolean
    smtp_configured: boolean
  }
  push_service: {
    enabled: boolean
    firebase_configured: boolean
  }
  services_available: {
    [key: string]: boolean
  }
}

export default function NotificationSettingsPage() {
  const router = useRouter()
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    email_enabled: true,
    push_enabled: true,
    appointment_reminders: true,
    workout_reminders: true,
    nutrition_reminders: true,
    progress_updates: true,
    report_notifications: true,
    reminder_hours_before: 24
  })
  const [status, setStatus] = useState<NotificationStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [message, setMessage] = useState('')
  const [currentUser, setCurrentUser] = useState<any>(null)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Buscar dados do usuário atual
      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/users/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (userResponse.ok) {
        const userData = await userResponse.json()
        setCurrentUser(userData)

        // Buscar preferências de notificação
        const prefsResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/v1/notifications/preferences/${userData.id}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          }
        )

        if (prefsResponse.ok) {
          const prefsData = await prefsResponse.json()
          setPreferences(prefsData)
        }
      }

      // Buscar status do sistema de notificações
      const statusResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/notifications/status`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (statusResponse.ok) {
        const statusData = await statusResponse.json()
        setStatus(statusData)
      }

    } catch (error) {
      console.error('Erro ao buscar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    if (!currentUser) return

    setIsSaving(true)
    setMessage('')

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/notifications/preferences/${currentUser.id}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(preferences),
        }
      )

      if (response.ok) {
        setMessage('Preferências salvas com sucesso!')
        setTimeout(() => setMessage(''), 3000)
      } else {
        throw new Error('Erro ao salvar preferências')
      }

    } catch (error) {
      setMessage('Erro ao salvar preferências')
      setTimeout(() => setMessage(''), 3000)
    } finally {
      setIsSaving(false)
    }
  }

  const handleTest = async () => {
    if (!currentUser) return

    setIsTesting(true)
    setMessage('')

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/notifications/test/${currentUser.id}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (response.ok) {
        setMessage('Notificação de teste enviada!')
        setTimeout(() => setMessage(''), 3000)
      } else {
        throw new Error('Erro ao enviar teste')
      }

    } catch (error) {
      setMessage('Erro ao enviar notificação de teste')
      setTimeout(() => setMessage(''), 3000)
    } finally {
      setIsTesting(false)
    }
  }

  const updatePreference = (key: keyof NotificationPreferences, value: boolean | number) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }))
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Configurações de Notificações - Hypatium</title>
        <meta name="description" content="Configure suas preferências de notificações" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href="/dashboard" className="text-blue-600 hover:text-blue-800">
                  ← Voltar ao Dashboard
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Bell className="h-6 w-6 mr-2" />
                Configurações de Notificações
              </h1>
              <div></div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Message */}
            {message && (
              <div className={`mb-6 p-4 rounded-md ${
                message.includes('Erro') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'
              }`}>
                {message}
              </div>
            )}

            {/* Status do Sistema */}
            {status && (
              <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6 mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Status do Sistema</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <Mail className="h-5 w-5 mr-3 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Email</p>
                      <p className={`text-sm ${status.email_service.enabled ? 'text-green-600' : 'text-red-600'}`}>
                        {status.email_service.enabled ? 'Configurado' : 'Não configurado'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Smartphone className="h-5 w-5 mr-3 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Push Notifications</p>
                      <p className={`text-sm ${status.push_service.enabled ? 'text-green-600' : 'text-red-600'}`}>
                        {status.push_service.enabled ? 'Configurado' : 'Não configurado'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Canais de Notificação */}
            <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Canais de Notificação</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Mail className="h-5 w-5 mr-3 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Notificações por Email</p>
                      <p className="text-sm text-gray-500">Receber notificações no seu email</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.email_enabled}
                      onChange={(e) => updatePreference('email_enabled', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Smartphone className="h-5 w-5 mr-3 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Push Notifications</p>
                      <p className="text-sm text-gray-500">Receber notificações no dispositivo</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.push_enabled}
                      onChange={(e) => updatePreference('push_enabled', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>
            </div>

            {/* Tipos de Notificação */}
            <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Tipos de Notificação</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 mr-3 text-blue-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Lembretes de Consultas</p>
                      <p className="text-sm text-gray-500">Lembrar sobre consultas agendadas</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.appointment_reminders}
                      onChange={(e) => updatePreference('appointment_reminders', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Activity className="h-5 w-5 mr-3 text-green-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Lembretes de Treino</p>
                      <p className="text-sm text-gray-500">Lembrar sobre treinos programados</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.workout_reminders}
                      onChange={(e) => updatePreference('workout_reminders', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Utensils className="h-5 w-5 mr-3 text-orange-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Lembretes Nutricionais</p>
                      <p className="text-sm text-gray-500">Lembrar sobre plano alimentar</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.nutrition_reminders}
                      onChange={(e) => updatePreference('nutrition_reminders', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <TrendingUp className="h-5 w-5 mr-3 text-purple-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Atualizações de Progresso</p>
                      <p className="text-sm text-gray-500">Notificar sobre mudanças no progresso</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.progress_updates}
                      onChange={(e) => updatePreference('progress_updates', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 mr-3 text-indigo-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Relatórios Prontos</p>
                      <p className="text-sm text-gray-500">Notificar quando relatórios estiverem prontos</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.report_notifications}
                      onChange={(e) => updatePreference('report_notifications', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>
            </div>

            {/* Configurações Avançadas */}
            <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6 mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Configurações Avançadas</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Antecedência dos Lembretes (horas)
                  </label>
                  <select
                    value={preferences.reminder_hours_before}
                    onChange={(e) => updatePreference('reminder_hours_before', parseInt(e.target.value))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={1}>1 hora antes</option>
                    <option value={2}>2 horas antes</option>
                    <option value={6}>6 horas antes</option>
                    <option value={12}>12 horas antes</option>
                    <option value={24}>24 horas antes</option>
                    <option value={48}>48 horas antes</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-between">
              <button
                onClick={handleTest}
                disabled={isTesting}
                className="bg-gray-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isTesting ? (
                  <div className="loading-spinner mr-2"></div>
                ) : (
                  <TestTube className="h-4 w-4 mr-2" />
                )}
                Testar Notificação
              </button>

              <button
                onClick={handleSave}
                disabled={isSaving}
                className="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isSaving ? (
                  <div className="loading-spinner mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Salvar Configurações
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
