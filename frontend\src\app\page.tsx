import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { <PERSON>, Card<PERSON>ead<PERSON>, CardTitle, CardDescription } from "@/components/ui/Card";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#E8F8F5] to-[#D1F2EB]">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <nav className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <h1 className="text-2xl font-bold text-[#333333]">Hypatium Core</h1>
          </div>
          <div className="flex space-x-4">
            <Link href="/login">
              <Button variant="outline">Entrar</Button>
            </Link>
            <Link href="/register">
              <Button>Cadastrar</Button>
            </Link>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-[#333333] mb-6">
            Sistema Completo para
            <span className="text-[#1ABC9C]"> Personal Trainers</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Gerencie seus alunos, realize avaliações físicas completas,
            acompanhe o progresso e gere relatórios profissionais com IA.
          </p>
          <div className="flex justify-center space-x-4">
            <Link href="/register">
              <Button size="lg">Começar Agora</Button>
            </Link>
            <Link href="/login">
              <Button variant="outline" size="lg">Fazer Login</Button>
            </Link>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="text-[#1ABC9C]">📊 Avaliações Completas</CardTitle>
              <CardDescription>
                Realize avaliações de composição corporal com cálculos automáticos de IMC,
                % de gordura e análise energética.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-[#27AE60]">🏋️ Volume Load</CardTitle>
              <CardDescription>
                Acompanhe o progresso dos treinos com cálculos automáticos de Volume Load
                por exercício, semana e mesociclo.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-[#2ECC71]">🤖 Relatórios com IA</CardTitle>
              <CardDescription>
                Gere relatórios profissionais automaticamente com análises detalhadas
                e recomendações personalizadas.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-white rounded-2xl p-12 shadow-lg">
          <h2 className="text-3xl font-bold text-[#333333] mb-4">
            Pronto para revolucionar seu trabalho?
          </h2>
          <p className="text-gray-600 mb-8">
            Junte-se aos Personal Trainers que já estão usando o Hypatium Core
            para otimizar seus resultados.
          </p>
          <Link href="/register">
            <Button size="lg">Cadastre-se Gratuitamente</Button>
          </Link>
        </div>
      </main>

      {/* Footer */}
      <footer className="container mx-auto px-4 py-8 text-center text-gray-600">
        <p>© 2024 Hypatium Core. Todos os direitos reservados.</p>
      </footer>
    </div>
  );
}
