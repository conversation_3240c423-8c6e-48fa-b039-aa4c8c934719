import { AlunoService } from './aluno.service';
import { CreateAlunoDto } from './dto/create-aluno.dto';
import { UpdateAlunoDto } from './dto/update-aluno.dto';
export declare class AlunoController {
    private readonly alunoService;
    constructor(alunoService: AlunoService);
    create(createAlunoDto: CreateAlunoDto, req: any): Promise<any>;
    findAllByPersonal(req: any): Promise<any>;
    findOne(id: string, req: any): Promise<any>;
    update(id: string, updateAlunoDto: UpdateAlunoDto, req: any): Promise<any>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
    getStats(id: string, req: any): Promise<{
        totalAvaliacoes: any;
        ultimaAvaliacao: any;
        primeiraAvaliacao: any;
        evolucao: {
            imc: {
                inicial: any;
                atual: any;
                diferenca: number | null;
            };
            gordura: {
                inicial: any;
                atual: any;
            };
        } | null;
    }>;
}
