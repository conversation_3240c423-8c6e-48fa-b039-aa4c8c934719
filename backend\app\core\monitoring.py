"""
Sistema de monitoramento e logging
"""

import os
import time
import logging
import psutil
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.core.database import get_db

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/hypatium.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class SystemMonitor:
    """Monitor de sistema para métricas de performance"""
    
    def __init__(self):
        self.start_time = time.time()
        
    def get_system_metrics(self) -> Dict[str, Any]:
        """Obter métricas do sistema"""
        try:
            # CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Memória
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / (1024**3)
            memory_total_gb = memory.total / (1024**3)
            
            # Disco
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_used_gb = disk.used / (1024**3)
            disk_total_gb = disk.total / (1024**3)
            
            # Rede
            network = psutil.net_io_counters()
            
            # Uptime
            uptime_seconds = time.time() - self.start_time
            uptime_hours = uptime_seconds / 3600
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "cpu": {
                    "percent": cpu_percent,
                    "count": cpu_count
                },
                "memory": {
                    "percent": memory_percent,
                    "used_gb": round(memory_used_gb, 2),
                    "total_gb": round(memory_total_gb, 2),
                    "available_gb": round((memory.total - memory.used) / (1024**3), 2)
                },
                "disk": {
                    "percent": disk_percent,
                    "used_gb": round(disk_used_gb, 2),
                    "total_gb": round(disk_total_gb, 2),
                    "free_gb": round((disk.total - disk.used) / (1024**3), 2)
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                "uptime": {
                    "seconds": round(uptime_seconds, 2),
                    "hours": round(uptime_hours, 2)
                }
            }
            
        except Exception as e:
            logger.error(f"Erro ao obter métricas do sistema: {str(e)}")
            return {"error": str(e)}
    
    def get_database_metrics(self, db: Session) -> Dict[str, Any]:
        """Obter métricas do banco de dados"""
        try:
            # Conexões ativas
            active_connections = db.execute(
                text("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
            ).scalar()
            
            # Total de conexões
            total_connections = db.execute(
                text("SELECT count(*) FROM pg_stat_activity")
            ).scalar()
            
            # Tamanho do banco
            db_size = db.execute(
                text("SELECT pg_size_pretty(pg_database_size(current_database()))")
            ).scalar()
            
            # Estatísticas de tabelas principais
            tables_stats = {}
            main_tables = ['users', 'patients', 'physical_assessments', 'psychology_sessions', 'automatic_reports']
            
            for table in main_tables:
                try:
                    count = db.execute(text(f"SELECT count(*) FROM {table}")).scalar()
                    tables_stats[table] = count
                except:
                    tables_stats[table] = 0
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "connections": {
                    "active": active_connections,
                    "total": total_connections
                },
                "database_size": db_size,
                "tables": tables_stats
            }
            
        except Exception as e:
            logger.error(f"Erro ao obter métricas do banco: {str(e)}")
            return {"error": str(e)}
    
    def get_application_metrics(self, db: Session) -> Dict[str, Any]:
        """Obter métricas da aplicação"""
        try:
            # Usuários ativos (últimas 24h)
            yesterday = datetime.utcnow() - timedelta(days=1)
            
            # Contadores básicos
            total_users = db.execute(text("SELECT count(*) FROM users")).scalar()
            total_patients = db.execute(text("SELECT count(*) FROM patients")).scalar()
            
            # Atividade recente
            recent_assessments = db.execute(
                text("SELECT count(*) FROM physical_assessments WHERE data_avaliacao >= :date"),
                {"date": yesterday}
            ).scalar()
            
            recent_sessions = db.execute(
                text("SELECT count(*) FROM psychology_sessions WHERE data_sessao >= :date"),
                {"date": yesterday}
            ).scalar()
            
            recent_reports = db.execute(
                text("SELECT count(*) FROM automatic_reports WHERE data_geracao >= :date"),
                {"date": yesterday}
            ).scalar()
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "totals": {
                    "users": total_users,
                    "patients": total_patients
                },
                "activity_24h": {
                    "assessments": recent_assessments,
                    "psychology_sessions": recent_sessions,
                    "reports_generated": recent_reports
                }
            }
            
        except Exception as e:
            logger.error(f"Erro ao obter métricas da aplicação: {str(e)}")
            return {"error": str(e)}

class HealthChecker:
    """Verificador de saúde do sistema"""
    
    def __init__(self):
        self.checks = {
            "database": self._check_database,
            "disk_space": self._check_disk_space,
            "memory": self._check_memory,
            "cpu": self._check_cpu
        }
    
    def _check_database(self, db: Session) -> Dict[str, Any]:
        """Verificar saúde do banco de dados"""
        try:
            # Teste de conexão simples
            db.execute(text("SELECT 1"))
            
            # Verificar conexões
            active_connections = db.execute(
                text("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
            ).scalar()
            
            status = "healthy"
            if active_connections > 50:  # Limite configurável
                status = "warning"
            if active_connections > 100:
                status = "critical"
            
            return {
                "status": status,
                "active_connections": active_connections,
                "message": "Database connection OK"
            }
            
        except Exception as e:
            return {
                "status": "critical",
                "error": str(e),
                "message": "Database connection failed"
            }
    
    def _check_disk_space(self, db: Session = None) -> Dict[str, Any]:
        """Verificar espaço em disco"""
        try:
            disk = psutil.disk_usage('/')
            percent_used = disk.percent
            
            status = "healthy"
            if percent_used > 80:
                status = "warning"
            if percent_used > 90:
                status = "critical"
            
            return {
                "status": status,
                "percent_used": percent_used,
                "free_gb": round((disk.total - disk.used) / (1024**3), 2),
                "message": f"Disk usage: {percent_used}%"
            }
            
        except Exception as e:
            return {
                "status": "critical",
                "error": str(e),
                "message": "Disk check failed"
            }
    
    def _check_memory(self, db: Session = None) -> Dict[str, Any]:
        """Verificar uso de memória"""
        try:
            memory = psutil.virtual_memory()
            percent_used = memory.percent
            
            status = "healthy"
            if percent_used > 80:
                status = "warning"
            if percent_used > 90:
                status = "critical"
            
            return {
                "status": status,
                "percent_used": percent_used,
                "available_gb": round(memory.available / (1024**3), 2),
                "message": f"Memory usage: {percent_used}%"
            }
            
        except Exception as e:
            return {
                "status": "critical",
                "error": str(e),
                "message": "Memory check failed"
            }
    
    def _check_cpu(self, db: Session = None) -> Dict[str, Any]:
        """Verificar uso de CPU"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            
            status = "healthy"
            if cpu_percent > 80:
                status = "warning"
            if cpu_percent > 95:
                status = "critical"
            
            return {
                "status": status,
                "percent_used": cpu_percent,
                "cpu_count": psutil.cpu_count(),
                "message": f"CPU usage: {cpu_percent}%"
            }
            
        except Exception as e:
            return {
                "status": "critical",
                "error": str(e),
                "message": "CPU check failed"
            }
    
    def run_health_checks(self, db: Session) -> Dict[str, Any]:
        """Executar todas as verificações de saúde"""
        results = {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "healthy",
            "checks": {}
        }
        
        critical_count = 0
        warning_count = 0
        
        for check_name, check_func in self.checks.items():
            try:
                result = check_func(db)
                results["checks"][check_name] = result
                
                if result["status"] == "critical":
                    critical_count += 1
                elif result["status"] == "warning":
                    warning_count += 1
                    
            except Exception as e:
                results["checks"][check_name] = {
                    "status": "critical",
                    "error": str(e),
                    "message": f"Health check {check_name} failed"
                }
                critical_count += 1
        
        # Determinar status geral
        if critical_count > 0:
            results["overall_status"] = "critical"
        elif warning_count > 0:
            results["overall_status"] = "warning"
        
        results["summary"] = {
            "total_checks": len(self.checks),
            "healthy": len(self.checks) - warning_count - critical_count,
            "warnings": warning_count,
            "critical": critical_count
        }
        
        return results

class PerformanceLogger:
    """Logger de performance para endpoints"""
    
    def __init__(self):
        self.performance_logger = logging.getLogger("performance")
        handler = logging.FileHandler('logs/performance.log')
        formatter = logging.Formatter('%(asctime)s - %(message)s')
        handler.setFormatter(formatter)
        self.performance_logger.addHandler(handler)
        self.performance_logger.setLevel(logging.INFO)
    
    def log_request(self, method: str, path: str, duration: float, status_code: int, user_id: Optional[int] = None):
        """Log de requisição"""
        log_data = {
            "method": method,
            "path": path,
            "duration_ms": round(duration * 1000, 2),
            "status_code": status_code,
            "user_id": user_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.performance_logger.info(f"REQUEST: {log_data}")
    
    def log_database_query(self, query: str, duration: float, rows_affected: int = 0):
        """Log de query do banco"""
        log_data = {
            "query": query[:100] + "..." if len(query) > 100 else query,
            "duration_ms": round(duration * 1000, 2),
            "rows_affected": rows_affected,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.performance_logger.info(f"DATABASE: {log_data}")

# Instâncias globais
system_monitor = SystemMonitor()
health_checker = HealthChecker()
performance_logger = PerformanceLogger()

def get_system_status(db: Session) -> Dict[str, Any]:
    """Obter status completo do sistema"""
    return {
        "system_metrics": system_monitor.get_system_metrics(),
        "database_metrics": system_monitor.get_database_metrics(db),
        "application_metrics": system_monitor.get_application_metrics(db),
        "health_checks": health_checker.run_health_checks(db)
    }
