"""
Schemas Pydantic para avaliações físicas
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

class PhysicalAssessmentBase(BaseModel):
    """Schema base para avaliação física"""
    peso: float = Field(..., gt=0, description="Peso em kg")
    altura: float = Field(..., gt=0, description="Altura em metros")
    data_avaliacao: datetime
    dobras_cutaneas: Optional[Dict[str, float]] = None
    circunferencias: Optional[Dict[str, float]] = None
    metodo_utilizado: Optional[str] = None
    observacoes: Optional[str] = None

class PhysicalAssessmentCreate(PhysicalAssessmentBase):
    """Schema para criação de avaliação física"""
    paciente_id: int
    avaliador_id: int

class PhysicalAssessmentUpdate(BaseModel):
    """Schema para atualização de avaliação física"""
    peso: Optional[float] = Field(None, gt=0)
    altura: Optional[float] = Field(None, gt=0)
    data_avaliacao: Optional[datetime] = None
    dobras_cutaneas: Optional[Dict[str, float]] = None
    circunferencias: Optional[Dict[str, float]] = None
    metodo_utilizado: Optional[str] = None
    observacoes: Optional[str] = None

class PhysicalAssessment(PhysicalAssessmentBase):
    """Schema para resposta de avaliação física"""
    id: int
    paciente_id: int
    avaliador_id: int
    
    # Cálculos automáticos
    imc: Optional[float] = None
    gordura_percentual: Optional[float] = None
    massa_gorda: Optional[float] = None
    massa_magra: Optional[float] = None
    tmb: Optional[float] = None
    get: Optional[float] = None
    
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class PhysicalAssessmentWithCalculations(PhysicalAssessment):
    """Schema para avaliação física com cálculos detalhados"""
    classificacao_imc: Optional[str] = None
    classificacao_gordura: Optional[str] = None
    recomendacoes: Optional[str] = None

# Schemas para cálculos específicos
class BodyCompositionCalculation(BaseModel):
    """Schema para cálculos de composição corporal"""
    peso: float
    altura: float
    idade: int
    sexo: str
    dobras_cutaneas: Optional[Dict[str, float]] = None
    metodo: str = "pollock"  # pollock, guedes, etc.

class MetabolicCalculation(BaseModel):
    """Schema para cálculos metabólicos"""
    peso: float
    altura: float
    idade: int
    sexo: str
    nivel_atividade: str = "sedentario"  # sedentario, leve, moderado, intenso, muito_intenso
    objetivo: str = "manutencao"  # deficit, manutencao, superavit
