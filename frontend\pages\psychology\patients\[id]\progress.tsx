import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  Brain, 
  TrendingUp, 
  Calendar,
  User,
  BarChart3,
  Heart,
  Zap,
  AlertTriangle
} from 'lucide-react'
import Progress<PERSON><PERSON> from '../../../../components/ProgressChart'

interface Patient {
  id: number
  nome: string
}

interface PsychologyProgress {
  total_sessions: number
  progress_data: Array<{
    data: string
    humor: number
    ansiedade: number
    motivacao: number
    duracao: number
  }>
  average_scores: {
    humor?: number
    ansiedade?: number
    motivacao?: number
  }
  trends: {
    [key: string]: {
      trend: string
      change: number
    }
  }
  last_session_date?: string
}

export default function PsychologyProgressPage() {
  const router = useRouter()
  const { id } = router.query
  const [patient, setPatient] = useState<Patient | null>(null)
  const [progressData, setProgressData] = useState<PsychologyProgress | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('3months')

  useEffect(() => {
    if (id) {
      fetchData()
    }
  }, [id, timeRange])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Buscar dados do paciente
      const patientResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/patients/${id}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (patientResponse.ok) {
        const patientData = await patientResponse.json()
        setPatient(patientData)
      }

      // Buscar progresso psicológico
      const limit = timeRange === 'all' ? 100 : timeRange === '1year' ? 50 : timeRange === '6months' ? 30 : 15
      
      const progressResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/psychology/patient/${id}/progress?limit=${limit}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (progressResponse.ok) {
        const progressData = await progressResponse.json()
        setProgressData(progressData)
      }
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getFilteredData = () => {
    if (!progressData || timeRange === 'all') return progressData?.progress_data || []

    const now = new Date()
    const cutoffDate = new Date()
    
    switch (timeRange) {
      case '1month':
        cutoffDate.setMonth(now.getMonth() - 1)
        break
      case '3months':
        cutoffDate.setMonth(now.getMonth() - 3)
        break
      case '6months':
        cutoffDate.setMonth(now.getMonth() - 6)
        break
      case '1year':
        cutoffDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        return progressData?.progress_data || []
    }

    return progressData?.progress_data.filter(item => new Date(item.data) >= cutoffDate) || []
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'melhorando':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'piorando':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return <BarChart3 className="h-4 w-4 text-gray-600" />
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'melhorando':
        return 'text-green-600'
      case 'piorando':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600'
    if (score >= 6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreLabel = (score: number) => {
    if (score >= 8) return 'Excelente'
    if (score >= 6) return 'Bom'
    if (score >= 4) return 'Regular'
    return 'Baixo'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  if (!patient) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Paciente não encontrado</h2>
          <Link href="/psychology/sessions" className="text-blue-600 hover:text-blue-800 mt-4 inline-block">
            ← Voltar para sessões
          </Link>
        </div>
      </div>
    )
  }

  const filteredData = getFilteredData()

  return (
    <>
      <Head>
        <title>Progresso Psicológico - {patient.nome} - Hypatium</title>
        <meta name="description" content={`Progresso psicológico do paciente ${patient.nome}`} />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href="/psychology/sessions" className="text-blue-600 hover:text-blue-800">
                  ← Voltar para Sessões
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Brain className="h-6 w-6 mr-2" />
                Progresso Psicológico - {patient.nome}
              </h1>
              <Link
                href={`/psychology/sessions/new?patient=${id}`}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
              >
                Nova Sessão
              </Link>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Controls */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div className="flex items-center space-x-4">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    Período:
                  </label>
                  <select
                    value={timeRange}
                    onChange={(e) => setTimeRange(e.target.value)}
                    className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="1month">Último mês</option>
                    <option value="3months">Últimos 3 meses</option>
                    <option value="6months">Últimos 6 meses</option>
                    <option value="1year">Último ano</option>
                    <option value="all">Todos os dados</option>
                  </select>
                </div>

                <div className="text-sm text-gray-600">
                  Total de sessões: {progressData?.total_sessions || 0}
                </div>
              </div>
            </div>

            {/* Summary Cards */}
            {progressData && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                {/* Humor */}
                <div className="bg-white p-6 rounded-lg shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Heart className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Humor Médio</p>
                        <p className={`text-2xl font-bold ${getScoreColor(progressData.average_scores.humor || 0)}`}>
                          {progressData.average_scores.humor?.toFixed(1) || 'N/A'}
                        </p>
                        <p className="text-sm text-gray-500">
                          {progressData.average_scores.humor ? getScoreLabel(progressData.average_scores.humor) : ''}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      {progressData.trends.humor && (
                        <>
                          {getTrendIcon(progressData.trends.humor.trend)}
                          <span className={`ml-1 text-sm ${getTrendColor(progressData.trends.humor.trend)}`}>
                            {progressData.trends.humor.change > 0 ? '+' : ''}{progressData.trends.humor.change.toFixed(1)}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* Ansiedade */}
                <div className="bg-white p-6 rounded-lg shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="p-2 bg-yellow-100 rounded-lg">
                        <AlertTriangle className="h-6 w-6 text-yellow-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Ansiedade Média</p>
                        <p className={`text-2xl font-bold ${getScoreColor(10 - (progressData.average_scores.ansiedade || 10))}`}>
                          {progressData.average_scores.ansiedade?.toFixed(1) || 'N/A'}
                        </p>
                        <p className="text-sm text-gray-500">
                          {progressData.average_scores.ansiedade ? getScoreLabel(10 - progressData.average_scores.ansiedade) : ''}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      {progressData.trends.ansiedade && (
                        <>
                          {getTrendIcon(progressData.trends.ansiedade.trend)}
                          <span className={`ml-1 text-sm ${getTrendColor(progressData.trends.ansiedade.trend)}`}>
                            {progressData.trends.ansiedade.change > 0 ? '+' : ''}{progressData.trends.ansiedade.change.toFixed(1)}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* Motivação */}
                <div className="bg-white p-6 rounded-lg shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <Zap className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Motivação Média</p>
                        <p className={`text-2xl font-bold ${getScoreColor(progressData.average_scores.motivacao || 0)}`}>
                          {progressData.average_scores.motivacao?.toFixed(1) || 'N/A'}
                        </p>
                        <p className="text-sm text-gray-500">
                          {progressData.average_scores.motivacao ? getScoreLabel(progressData.average_scores.motivacao) : ''}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      {progressData.trends.motivacao && (
                        <>
                          {getTrendIcon(progressData.trends.motivacao.trend)}
                          <span className={`ml-1 text-sm ${getTrendColor(progressData.trends.motivacao.trend)}`}>
                            {progressData.trends.motivacao.change > 0 ? '+' : ''}{progressData.trends.motivacao.change.toFixed(1)}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Progress Chart */}
            {filteredData.length > 0 ? (
              <div className="mb-6">
                <ProgressChart
                  data={filteredData.map(item => ({
                    data: item.data,
                    peso: item.humor, // Usando humor como peso para compatibilidade
                    imc: item.ansiedade,
                    gordura_percentual: item.motivacao,
                  }))}
                  metrics={['peso', 'imc', 'gordura_percentual']}
                  title="Evolução Psicológica"
                  height={400}
                />
              </div>
            ) : (
              <div className="bg-white p-6 rounded-lg shadow mb-6">
                <div className="text-center text-gray-500">
                  <Brain className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Sem dados suficientes</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Não há dados suficientes para exibir o gráfico de progresso.
                  </p>
                  <div className="mt-6">
                    <Link
                      href={`/psychology/sessions/new?patient=${id}`}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      Criar primeira sessão
                    </Link>
                  </div>
                </div>
              </div>
            )}

            {/* Sessions History */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Histórico de Sessões</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Data
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Duração
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Humor
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ansiedade
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Motivação
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredData.map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(item.data).toLocaleDateString('pt-BR')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.duracao}min
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`font-medium ${getScoreColor(item.humor)}`}>
                            {item.humor}/10
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`font-medium ${getScoreColor(10 - item.ansiedade)}`}>
                            {item.ansiedade}/10
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`font-medium ${getScoreColor(item.motivacao)}`}>
                            {item.motivacao}/10
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
