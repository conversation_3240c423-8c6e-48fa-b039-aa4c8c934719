"""
Serviço para geração de PDFs
"""

import os
import io
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path

# Placeholder para bibliotecas de PDF
# from reportlab.lib.pagesizes import letter, A4
# from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
# from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
# from reportlab.lib.units import inch
# from reportlab.lib import colors
# from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

class PDFService:
    def __init__(self):
        self.output_dir = Path("static/pdfs")
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Configurações padrão
        self.page_size = "A4"  # A4 ou letter
        self.margin = 72  # 1 inch

    def generate_report_pdf(self, report_data: Dict[str, Any], patient_name: str) -> Dict[str, Any]:
        """
        Gerar PDF de relatório
        """
        try:
            # Por enquanto, gerar PDF simulado
            # TODO: Implementar geração real com ReportLab

            filename = f"relatorio_{patient_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            file_path = self.output_dir / filename

            # Simular geração de PDF
            pdf_content = self._generate_mock_pdf_content(report_data, patient_name)

            # Salvar arquivo simulado
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(pdf_content)

            return {
                'success': True,
                'file_path': str(file_path),
                'file_url': f"/static/pdfs/{filename}",
                'filename': filename,
                'size_bytes': len(pdf_content.encode('utf-8'))
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_mock_pdf_content(self, report_data: Dict[str, Any], patient_name: str) -> str:
        """
        Gerar conteúdo simulado de PDF (será substituído por ReportLab)
        """
        content = f"""
RELATÓRIO MÉDICO - {patient_name.upper()}
{'=' * 50}

Data de Geração: {datetime.now().strftime('%d/%m/%Y às %H:%M')}
Tipo de Relatório: {report_data.get('tipo_relatorio', 'Não especificado')}

CONTEÚDO DO RELATÓRIO:
{'-' * 30}

{report_data.get('conteudo_gerado', 'Conteúdo não disponível')}

{'=' * 50}
Relatório gerado automaticamente pelo sistema Hypatium
Este documento é confidencial e destinado exclusivamente ao paciente e profissionais autorizados.
"""
        return content

    def generate_assessment_pdf(self, assessment_data: Dict[str, Any], patient_name: str) -> Dict[str, Any]:
        """
        Gerar PDF de avaliação física
        """
        try:
            filename = f"avaliacao_{patient_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            file_path = self.output_dir / filename

            # Simular geração de PDF de avaliação
            pdf_content = self._generate_assessment_pdf_content(assessment_data, patient_name)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(pdf_content)

            return {
                'success': True,
                'file_path': str(file_path),
                'file_url': f"/static/pdfs/{filename}",
                'filename': filename,
                'size_bytes': len(pdf_content.encode('utf-8'))
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_assessment_pdf_content(self, assessment_data: Dict[str, Any], patient_name: str) -> str:
        """
        Gerar conteúdo de PDF de avaliação física
        """
        content = f"""
AVALIAÇÃO FÍSICA - {patient_name.upper()}
{'=' * 50}

Data da Avaliação: {assessment_data.get('data_avaliacao', 'Não informado')}
Profissional: {assessment_data.get('profissional', 'Não informado')}

DADOS ANTROPOMÉTRICOS:
{'-' * 30}
Peso: {assessment_data.get('peso', 'N/A')} kg
Altura: {assessment_data.get('altura', 'N/A')} m
IMC: {assessment_data.get('imc', 'N/A')}

COMPOSIÇÃO CORPORAL:
{'-' * 30}
Gordura Corporal: {assessment_data.get('gordura_percentual', 'N/A')}%
Massa Gorda: {assessment_data.get('massa_gorda', 'N/A')} kg
Massa Magra: {assessment_data.get('massa_magra', 'N/A')} kg

DOBRAS CUTÂNEAS:
{'-' * 30}
Método: {assessment_data.get('metodo_dobras', 'N/A')}
"""

        # Adicionar dobras se disponíveis
        dobras = ['peitoral', 'abdominal', 'coxa', 'triceps', 'subescapular', 'suprailiaca', 'axilar_media']
        for dobra in dobras:
            if assessment_data.get(f'dobra_{dobra}'):
                content += f"{dobra.replace('_', ' ').title()}: {assessment_data[f'dobra_{dobra}']} mm\n"

        content += f"""

OBSERVAÇÕES:
{'-' * 30}
{assessment_data.get('observacoes', 'Nenhuma observação registrada')}

{'=' * 50}
Avaliação realizada no sistema Hypatium
Este documento é confidencial e destinado exclusivamente ao paciente e profissionais autorizados.
"""
        return content

    def generate_nutrition_plan_pdf(self, plan_data: Dict[str, Any], patient_name: str) -> Dict[str, Any]:
        """
        Gerar PDF de plano nutricional
        """
        try:
            filename = f"plano_nutricional_{patient_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            file_path = self.output_dir / filename

            pdf_content = self._generate_nutrition_pdf_content(plan_data, patient_name)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(pdf_content)

            return {
                'success': True,
                'file_path': str(file_path),
                'file_url': f"/static/pdfs/{filename}",
                'filename': filename,
                'size_bytes': len(pdf_content.encode('utf-8'))
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_nutrition_pdf_content(self, plan_data: Dict[str, Any], patient_name: str) -> str:
        """
        Gerar conteúdo de PDF de plano nutricional
        """
        content = f"""
PLANO NUTRICIONAL - {patient_name.upper()}
{'=' * 50}

Data de Criação: {plan_data.get('data_criacao', 'Não informado')}
Nutricionista: {plan_data.get('nutricionista', 'Não informado')}

NECESSIDADES CALÓRICAS:
{'-' * 30}
TMB (Taxa Metabólica Basal): {plan_data.get('tmb_calculado', 'N/A')} kcal
GET (Gasto Energético Total): {plan_data.get('get_calculado', 'N/A')} kcal
Calorias Alvo: {plan_data.get('calorias_alvo', 'N/A')} kcal

DISTRIBUIÇÃO DE MACRONUTRIENTES:
{'-' * 30}
Proteínas: {plan_data.get('proteinas_g', 'N/A')} g
Carboidratos: {plan_data.get('carboidratos_g', 'N/A')} g
Gorduras: {plan_data.get('gorduras_g', 'N/A')} g

OBSERVAÇÕES:
{'-' * 30}
{plan_data.get('observacoes', 'Nenhuma observação registrada')}

{'=' * 50}
Plano nutricional gerado no sistema Hypatium
Este documento é confidencial e destinado exclusivamente ao paciente e profissionais autorizados.
"""
        return content

    def generate_workout_pdf(self, workout_data: Dict[str, Any], patient_name: str) -> Dict[str, Any]:
        """
        Gerar PDF de treino
        """
        try:
            filename = f"treino_{patient_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            file_path = self.output_dir / filename

            pdf_content = self._generate_workout_pdf_content(workout_data, patient_name)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(pdf_content)

            return {
                'success': True,
                'file_path': str(file_path),
                'file_url': f"/static/pdfs/{filename}",
                'filename': filename,
                'size_bytes': len(pdf_content.encode('utf-8'))
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_workout_pdf_content(self, workout_data: Dict[str, Any], patient_name: str) -> str:
        """
        Gerar conteúdo de PDF de treino
        """
        content = f"""
FICHA DE TREINO - {patient_name.upper()}
{'=' * 50}

Nome do Treino: {workout_data.get('nome', 'Não informado')}
Data de Início: {workout_data.get('data_inicio', 'Não informado')}
Personal Trainer: {workout_data.get('personal', 'Não informado')}

DESCRIÇÃO:
{'-' * 30}
{workout_data.get('descricao', 'Nenhuma descrição disponível')}

EXERCÍCIOS:
{'-' * 30}
"""

        # Adicionar exercícios se disponíveis
        exercicios = workout_data.get('exercicios', [])
        for i, exercicio in enumerate(exercicios, 1):
            content += f"""
{i}. {exercicio.get('nome_exercicio', 'Exercício não informado')}
   Grupo Muscular: {exercicio.get('grupo_muscular', 'N/A')}
   Séries: {exercicio.get('series', 'N/A')}
   Repetições: {exercicio.get('repeticoes', 'N/A')}
   Carga: {exercicio.get('carga', 'N/A')}
   Descanso: {exercicio.get('descanso', 'N/A')}
   Observações: {exercicio.get('observacoes', 'Nenhuma')}
"""

        content += f"""

OBSERVAÇÕES GERAIS:
{'-' * 30}
{workout_data.get('observacoes', 'Nenhuma observação registrada')}

{'=' * 50}
Treino criado no sistema Hypatium
Este documento é confidencial e destinado exclusivamente ao paciente e profissionais autorizados.
"""
        return content

    def list_generated_pdfs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Listar PDFs gerados
        """
        pdfs = []

        if not self.output_dir.exists():
            return pdfs

        for pdf_file in self.output_dir.glob("*.pdf"):
            try:
                stat = pdf_file.stat()
                pdfs.append({
                    'filename': pdf_file.name,
                    'file_path': str(pdf_file),
                    'file_url': f"/static/pdfs/{pdf_file.name}",
                    'size_bytes': stat.st_size,
                    'size_mb': round(stat.st_size / (1024 * 1024), 2),
                    'created_at': stat.st_ctime,
                    'modified_at': stat.st_mtime
                })
            except Exception:
                continue

        # Ordenar por data de criação (mais recente primeiro)
        pdfs.sort(key=lambda x: x['created_at'], reverse=True)

        return pdfs[:limit]

    def delete_pdf(self, filename: str) -> bool:
        """
        Deletar PDF
        """
        try:
            file_path = self.output_dir / filename
            if file_path.exists() and file_path.is_file():
                file_path.unlink()
                return True
            return False
        except Exception:
            return False

    def cleanup_old_pdfs(self, days_old: int = 30) -> Dict[str, Any]:
        """
        Limpar PDFs antigos
        """
        import time

        cutoff_time = time.time() - (days_old * 24 * 60 * 60)
        deleted_files = []
        errors = []

        if not self.output_dir.exists():
            return {'deleted_count': 0, 'deleted_files': [], 'errors': []}

        for pdf_file in self.output_dir.glob("*.pdf"):
            try:
                if pdf_file.stat().st_mtime < cutoff_time:
                    pdf_file.unlink()
                    deleted_files.append(str(pdf_file))
            except Exception as e:
                errors.append(f"Erro ao deletar {pdf_file}: {str(e)}")

        return {
            'deleted_count': len(deleted_files),
            'deleted_files': deleted_files,
            'errors': errors
        }

    def get_pdf_stats(self) -> Dict[str, Any]:
        """
        Obter estatísticas de PDFs
        """
        if not self.output_dir.exists():
            return {
                'total_files': 0,
                'total_size': 0,
                'total_size_mb': 0
            }

        pdf_files = list(self.output_dir.glob("*.pdf"))
        total_size = sum(f.stat().st_size for f in pdf_files if f.exists())

        return {
            'total_files': len(pdf_files),
            'total_size': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2)
        }
