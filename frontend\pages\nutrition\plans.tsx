import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  Utensils, 
  Plus, 
  Search, 
  Filter,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Target,
  Activity
} from 'lucide-react'

interface Patient {
  id: number
  nome: string
}

interface NutritionalPlan {
  id: number
  data_criacao: string
  tmb_calculado?: number
  get_calculado?: number
  calorias_alvo?: number
  proteinas_g?: number
  carboidratos_g?: number
  gorduras_g?: number
  observacoes?: string
  ativo: boolean
  paciente_id: number
  paciente?: Patient
}

export default function NutritionPlansPage() {
  const router = useRouter()
  const [plans, setPlans] = useState<NutritionalPlan[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterActive, setFilterActive] = useState(true)

  useEffect(() => {
    fetchPlans()
  }, [])

  const fetchPlans = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Buscar planos do nutricionista atual
      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/users/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (!userResponse.ok) {
        throw new Error('Erro ao buscar dados do usuário')
      }

      const userData = await userResponse.json()

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/nutrition/nutritionist/${userData.id}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (!response.ok) {
        throw new Error('Erro ao buscar planos nutricionais')
      }

      const data = await response.json()
      
      // Buscar dados dos pacientes para cada plano
      const plansWithPatients = await Promise.all(
        data.map(async (plan: NutritionalPlan) => {
          try {
            const patientResponse = await fetch(
              `${process.env.NEXT_PUBLIC_API_URL}/api/v1/patients/${plan.paciente_id}`,
              {
                headers: {
                  'Authorization': `Bearer ${token}`,
                },
              }
            )
            
            if (patientResponse.ok) {
              const patientData = await patientResponse.json()
              return { ...plan, paciente: patientData }
            }
            return plan
          } catch {
            return plan
          }
        })
      )

      setPlans(plansWithPatients)
    } catch (error) {
      console.error('Erro ao buscar planos:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredPlans = plans.filter(plan => {
    const matchesSearch = plan.paciente?.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plan.observacoes?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterActive ? plan.ativo : true
    return matchesSearch && matchesFilter
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const createAutoPlan = async (patientId: number) => {
    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/nutrition/patient/${patientId}/auto-create?objetivo=manutencao`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (response.ok) {
        fetchPlans() // Recarregar lista
      }
    } catch (error) {
      console.error('Erro ao criar plano automático:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Planos Nutricionais - Hypatium</title>
        <meta name="description" content="Gerencie planos nutricionais" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href="/dashboard" className="text-blue-600 hover:text-blue-800">
                  ← Voltar ao Dashboard
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Utensils className="h-6 w-6 mr-2" />
                Planos Nutricionais
              </h1>
              <Link
                href="/nutrition/calculator"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                Nova Calculadora
              </Link>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Filters */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="Buscar planos..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filterActive}
                      onChange={(e) => setFilterActive(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Apenas ativos</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Utensils className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total de Planos</p>
                    <p className="text-2xl font-bold text-gray-900">{plans.length}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Activity className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Planos Ativos</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {plans.filter(p => p.ativo).length}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Calendar className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Este Mês</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {plans.filter(p => {
                        const created = new Date(p.data_criacao)
                        const now = new Date()
                        return created.getMonth() === now.getMonth() && 
                               created.getFullYear() === now.getFullYear()
                      }).length}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Plans List */}
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {filteredPlans.length === 0 ? (
                  <li className="px-6 py-12 text-center">
                    <Utensils className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum plano encontrado</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {searchTerm ? 'Tente ajustar sua busca.' : 'Comece criando um novo plano nutricional.'}
                    </p>
                    {!searchTerm && (
                      <div className="mt-6">
                        <Link
                          href="/nutrition/calculator"
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Nova Calculadora
                        </Link>
                      </div>
                    )}
                  </li>
                ) : (
                  filteredPlans.map((plan) => (
                    <li key={plan.id}>
                      <div className="px-6 py-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="flex-shrink-0">
                              <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                <Utensils className="h-5 w-5 text-green-600" />
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="flex items-center">
                                <p className="text-sm font-medium text-gray-900">
                                  {plan.paciente?.nome || `Paciente ${plan.paciente_id}`}
                                </p>
                                {!plan.ativo && (
                                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Inativo
                                  </span>
                                )}
                                {plan.ativo && (
                                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Ativo
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center text-sm text-gray-500 space-x-4">
                                <div className="flex items-center">
                                  <Calendar className="h-4 w-4 mr-1" />
                                  {formatDate(plan.data_criacao)}
                                </div>
                                {plan.calorias_alvo && (
                                  <div className="flex items-center">
                                    <Target className="h-4 w-4 mr-1" />
                                    {plan.calorias_alvo} kcal
                                  </div>
                                )}
                              </div>
                              {plan.observacoes && (
                                <p className="text-sm text-gray-600 mt-1 truncate max-w-md">
                                  {plan.observacoes}
                                </p>
                              )}
                              
                              {/* Macronutrientes */}
                              {(plan.proteinas_g || plan.carboidratos_g || plan.gorduras_g) && (
                                <div className="flex items-center space-x-4 mt-2">
                                  {plan.proteinas_g && (
                                    <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                                      P: {plan.proteinas_g}g
                                    </span>
                                  )}
                                  {plan.carboidratos_g && (
                                    <span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">
                                      C: {plan.carboidratos_g}g
                                    </span>
                                  )}
                                  {plan.gorduras_g && (
                                    <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">
                                      G: {plan.gorduras_g}g
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => router.push(`/nutrition/plans/${plan.id}`)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => router.push(`/nutrition/plans/${plan.id}/edit`)}
                              className="text-gray-600 hover:text-gray-900"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button className="text-red-600 hover:text-red-900">
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
