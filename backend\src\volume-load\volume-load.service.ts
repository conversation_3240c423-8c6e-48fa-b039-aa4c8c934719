import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateVolumeLoadDto } from './dto/create-volume-load.dto';
import { UpdateVolumeLoadDto } from './dto/update-volume-load.dto';

@Injectable()
export class VolumeLoadService {
  constructor(private prisma: PrismaService) {}

  // Calcular Volume Load de uma série
  private calcularVLSerie(repeticoes?: number, carga?: number): number {
    if (!repeticoes || !carga) return 0;
    return repeticoes * carga;
  }

  // Calcular Volume Load total do exercício
  private calcularVLExercicio(volumeLoad: any): number {
    const series = [
      this.calcularVLSerie(volumeLoad.serie1Repeticoes, volumeLoad.serie1Carga),
      this.calcularVLSerie(volumeLoad.serie2Repeticoes, volumeLoad.serie2Carga),
      this.calcularVLSerie(volumeLoad.serie3Repeticoes, volumeLoad.serie3Carga),
      this.calcularVLSerie(volumeLoad.serie4Repeticoes, volumeLoad.serie4Carga),
      this.calcularVLSerie(volumeLoad.serie5Repeticoes, volumeLoad.serie5Carga),
      this.calcularVLSerie(volumeLoad.serie6Repeticoes, volumeLoad.serie6Carga),
      this.calcularVLSerie(volumeLoad.serie7Repeticoes, volumeLoad.serie7Carga),
      this.calcularVLSerie(volumeLoad.serie8Repeticoes, volumeLoad.serie8Carga),
      this.calcularVLSerie(volumeLoad.serie9Repeticoes, volumeLoad.serie9Carga),
      this.calcularVLSerie(volumeLoad.serie10Repeticoes, volumeLoad.serie10Carga),
    ];

    return series.reduce((total, vlSerie) => total + vlSerie, 0);
  }

  async create(createVolumeLoadDto: CreateVolumeLoadDto, personalId: string) {
    // Verificar se a avaliação existe e pertence ao personal
    const avaliacao = await this.prisma.avaliacaoComposicao.findUnique({
      where: { id: createVolumeLoadDto.avaliacaoComposicaoId },
      include: { aluno: true },
    });

    if (!avaliacao) {
      throw new NotFoundException('Avaliação não encontrada');
    }

    if (avaliacao.personalId !== personalId) {
      throw new ForbiddenException('Acesso negado a esta avaliação');
    }

    // Calcular VL das séries
    const vlSerie1 = this.calcularVLSerie(createVolumeLoadDto.serie1Repeticoes, createVolumeLoadDto.serie1Carga);
    const vlSerie2 = this.calcularVLSerie(createVolumeLoadDto.serie2Repeticoes, createVolumeLoadDto.serie2Carga);
    const vlSerie3 = this.calcularVLSerie(createVolumeLoadDto.serie3Repeticoes, createVolumeLoadDto.serie3Carga);
    const vlSerie4 = this.calcularVLSerie(createVolumeLoadDto.serie4Repeticoes, createVolumeLoadDto.serie4Carga);
    const vlSerie5 = this.calcularVLSerie(createVolumeLoadDto.serie5Repeticoes, createVolumeLoadDto.serie5Carga);
    const vlSerie6 = this.calcularVLSerie(createVolumeLoadDto.serie6Repeticoes, createVolumeLoadDto.serie6Carga);
    const vlSerie7 = this.calcularVLSerie(createVolumeLoadDto.serie7Repeticoes, createVolumeLoadDto.serie7Carga);
    const vlSerie8 = this.calcularVLSerie(createVolumeLoadDto.serie8Repeticoes, createVolumeLoadDto.serie8Carga);
    const vlSerie9 = this.calcularVLSerie(createVolumeLoadDto.serie9Repeticoes, createVolumeLoadDto.serie9Carga);
    const vlSerie10 = this.calcularVLSerie(createVolumeLoadDto.serie10Repeticoes, createVolumeLoadDto.serie10Carga);

    // Calcular VL do exercício
    const vlExercicio = vlSerie1 + vlSerie2 + vlSerie3 + vlSerie4 + vlSerie5 + 
                       vlSerie6 + vlSerie7 + vlSerie8 + vlSerie9 + vlSerie10;

    // Criar o registro
    const volumeLoad = await this.prisma.volumeLoad.create({
      data: {
        ...createVolumeLoadDto,
        serie1VL: vlSerie1,
        serie2VL: vlSerie2,
        serie3VL: vlSerie3,
        serie4VL: vlSerie4,
        serie5VL: vlSerie5,
        serie6VL: vlSerie6,
        serie7VL: vlSerie7,
        serie8VL: vlSerie8,
        serie9VL: vlSerie9,
        serie10VL: vlSerie10,
        vlExercicio,
      },
    });

    // Calcular VL do mesociclo (soma de todas as semanas deste exercício)
    await this.calcularVLMesociclo(createVolumeLoadDto.avaliacaoComposicaoId, createVolumeLoadDto.exercicio);

    return volumeLoad;
  }

  async update(id: string, updateVolumeLoadDto: UpdateVolumeLoadDto, personalId: string) {
    // Verificar se o volume load existe
    const volumeLoad = await this.prisma.volumeLoad.findUnique({
      where: { id },
      include: {
        avaliacaoComposicao: true,
      },
    });

    if (!volumeLoad) {
      throw new NotFoundException('Volume Load não encontrado');
    }

    if (volumeLoad.avaliacaoComposicao.personalId !== personalId) {
      throw new ForbiddenException('Acesso negado a este Volume Load');
    }

    // Recalcular VLs
    const dadosAtualizados = { ...volumeLoad, ...updateVolumeLoadDto };
    const vlExercicio = this.calcularVLExercicio(dadosAtualizados);

    const updatedVolumeLoad = await this.prisma.volumeLoad.update({
      where: { id },
      data: {
        ...updateVolumeLoadDto,
        vlExercicio,
      },
    });

    // Recalcular VL do mesociclo
    await this.calcularVLMesociclo(volumeLoad.avaliacaoComposicaoId, volumeLoad.exercicio);

    return updatedVolumeLoad;
  }

  private async calcularVLMesociclo(avaliacaoComposicaoId: string, exercicio: number) {
    // Buscar todos os volume loads deste exercício
    const volumeLoads = await this.prisma.volumeLoad.findMany({
      where: {
        avaliacaoComposicaoId,
        exercicio,
      },
    });

    // Calcular VL total do mesociclo
    const vlMesociclo = volumeLoads.reduce((total, vl) => total + (vl.vlExercicio || 0), 0);

    // Atualizar todos os registros deste exercício com o VL do mesociclo
    await this.prisma.volumeLoad.updateMany({
      where: {
        avaliacaoComposicaoId,
        exercicio,
      },
      data: {
        vlMesociclo,
      },
    });
  }

  async findByAvaliacao(avaliacaoComposicaoId: string, personalId?: string) {
    // Verificar acesso se personalId fornecido
    if (personalId) {
      const avaliacao = await this.prisma.avaliacaoComposicao.findUnique({
        where: { id: avaliacaoComposicaoId },
      });

      if (!avaliacao || avaliacao.personalId !== personalId) {
        throw new ForbiddenException('Acesso negado a esta avaliação');
      }
    }

    return this.prisma.volumeLoad.findMany({
      where: { avaliacaoComposicaoId },
      orderBy: [
        { exercicio: 'asc' },
        { semana: 'asc' },
      ],
    });
  }

  async getVolumeLoadStats(avaliacaoComposicaoId: string, personalId?: string) {
    const volumeLoads = await this.findByAvaliacao(avaliacaoComposicaoId, personalId);

    // Agrupar por exercício
    const porExercicio = volumeLoads.reduce((acc, vl) => {
      if (!acc[vl.exercicio]) {
        acc[vl.exercicio] = {
          exercicio: vl.exercicio,
          semanas: [],
          vlTotal: 0,
        };
      }
      
      acc[vl.exercicio].semanas.push({
        semana: vl.semana,
        vlExercicio: vl.vlExercicio || 0,
      });
      
      acc[vl.exercicio].vlTotal += vl.vlExercicio || 0;
      
      return acc;
    }, {} as any);

    // Agrupar por semana
    const porSemana = volumeLoads.reduce((acc, vl) => {
      if (!acc[vl.semana]) {
        acc[vl.semana] = {
          semana: vl.semana,
          exercicios: [],
          vlTotal: 0,
        };
      }
      
      acc[vl.semana].exercicios.push({
        exercicio: vl.exercicio,
        vlExercicio: vl.vlExercicio || 0,
      });
      
      acc[vl.semana].vlTotal += vl.vlExercicio || 0;
      
      return acc;
    }, {} as any);

    return {
      porExercicio: Object.values(porExercicio),
      porSemana: Object.values(porSemana),
      vlTotalMesociclo: volumeLoads.reduce((total, vl) => total + (vl.vlExercicio || 0), 0),
    };
  }

  async remove(id: string, personalId: string) {
    const volumeLoad = await this.prisma.volumeLoad.findUnique({
      where: { id },
      include: {
        avaliacaoComposicao: true,
      },
    });

    if (!volumeLoad) {
      throw new NotFoundException('Volume Load não encontrado');
    }

    if (volumeLoad.avaliacaoComposicao.personalId !== personalId) {
      throw new ForbiddenException('Acesso negado a este Volume Load');
    }

    await this.prisma.volumeLoad.delete({
      where: { id },
    });

    // Recalcular VL do mesociclo
    await this.calcularVLMesociclo(volumeLoad.avaliacaoComposicaoId, volumeLoad.exercicio);

    return { message: 'Volume Load removido com sucesso' };
  }
}
