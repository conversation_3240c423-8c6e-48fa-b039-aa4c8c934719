"""
Versão simplificada do main.py para teste
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Criar aplicação FastAPI
app = FastAPI(
    title="Hypatium API",
    description="API para plataforma de saúde integrada",
    version="1.0.0"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    return {"message": "Hypatium API está funcionando!"}

@app.get("/health")
def health_check():
    return {"status": "healthy", "message": "Sistema operacional"}

@app.get("/api/v1/test")
def test_endpoint():
    return {"message": "Endpoint de teste funcionando", "version": "1.0.0"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
