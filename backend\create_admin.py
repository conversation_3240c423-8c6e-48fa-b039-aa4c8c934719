"""
Script para criar usuário administrador inicial
"""

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine
from app.models.models import Base, User, UserType
from app.core.security import get_password_hash

def create_admin_user():
    """Criar usuário administrador inicial"""
    
    # Criar tabelas se não existirem
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    
    try:
        # Verificar se já existe um admin
        admin_user = db.query(User).filter(
            User.email == "<EMAIL>"
        ).first()
        
        if admin_user:
            print("Usuário admin já existe!")
            return
        
        # Criar usuário admin
        admin_user = User(
            nome="Administrador",
            email="<EMAIL>",
            senha_hash=get_password_hash("admin123"),
            tipo=UserType.ADMIN,
            ativo=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print("Usuário administrador criado com sucesso!")
        print("Email: <EMAIL>")
        print("Senha: admin123")
        print("IMPORTANTE: Altere a senha após o primeiro login!")
        
    except Exception as e:
        print(f"Erro ao criar usuário admin: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
