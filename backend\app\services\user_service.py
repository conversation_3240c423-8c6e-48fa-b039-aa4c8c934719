"""
Serviço para gerenciamento de usuários
"""

from typing import Any, Dict, Optional, Union
from sqlalchemy.orm import Session
from app.core.security import get_password_hash, verify_password
from app.models.models import User
from app.schemas.user import UserCreate, UserUpdate

class UserService:
    def __init__(self, db: Session):
        self.db = db

    def get(self, id: int) -> Optional[User]:
        """Obter usuário por ID"""
        return self.db.query(User).filter(User.id == id).first()

    def get_by_email(self, *, email: str) -> Optional[User]:
        """Obter usuário por email"""
        return self.db.query(User).filter(User.email == email).first()

    def create(self, *, obj_in: UserCreate) -> User:
        """Criar novo usuário"""
        db_obj = User(
            nome=obj_in.nome,
            email=obj_in.email,
            senha_hash=get_password_hash(obj_in.senha),
            tipo=obj_in.tipo,
            telefone=obj_in.telefone,
            ativo=obj_in.ativo,
        )
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj

    def update(
        self, *, db_obj: User, obj_in: Union[UserUpdate, Dict[str, Any]]
    ) -> User:
        """Atualizar usuário"""
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj

    def authenticate(self, *, email: str, password: str) -> Optional[User]:
        """Autenticar usuário"""
        user = self.get_by_email(email=email)
        if not user:
            return None
        if not verify_password(password, user.senha_hash):
            return None
        return user

    def is_active(self, user: User) -> bool:
        """Verificar se usuário está ativo"""
        return user.ativo
