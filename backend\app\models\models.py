"""
Modelos de dados do Hypatium
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON, ForeignKey, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

Base = declarative_base()

class UserType(enum.Enum):
    """Tipos de usuário do sistema"""
    PERSONAL = "personal"
    NUTRICIONISTA = "nutricionista"
    PSICOLOGO = "psicologo"
    ALUNO = "aluno"
    ADMIN = "admin"

class Gender(enum.Enum):
    """Gênero do paciente"""
    MASCULINO = "masculino"
    FEMININO = "feminino"
    OUTRO = "outro"

class User(Base):
    """Modelo de usuário do sistema"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    nome = Column(String(100), nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    senha_hash = Column(String(255), nullable=False)
    tipo = Column(Enum(UserType), nullable=False)
    telefone = Column(String(20))
    ativo = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relacionamentos
    pacientes = relationship("Patient", back_populates="responsavel")
    avaliacoes_criadas = relationship("PhysicalAssessment", back_populates="avaliador")
    treinos_criados = relationship("Workout", back_populates="personal")
    planos_nutricionais = relationship("NutritionalPlan", back_populates="nutricionista")
    sessoes_psicologicas = relationship("PsychologySession", back_populates="psicologo")

class Patient(Base):
    """Modelo de paciente"""
    __tablename__ = "patients"

    id = Column(Integer, primary_key=True, index=True)
    nome = Column(String(100), nullable=False)
    email = Column(String(100))
    telefone = Column(String(20))
    nascimento = Column(DateTime)
    sexo = Column(Enum(Gender))
    profissao = Column(String(100))
    objetivo = Column(Text)
    observacoes = Column(Text)
    ativo = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Chave estrangeira
    responsavel_id = Column(Integer, ForeignKey("users.id"))

    # Relacionamentos
    responsavel = relationship("User", back_populates="pacientes")
    avaliacoes_fisicas = relationship("PhysicalAssessment", back_populates="paciente")
    treinos = relationship("Workout", back_populates="paciente")
    planos_nutricionais = relationship("NutritionalPlan", back_populates="paciente")
    sessoes_psicologicas = relationship("PsychologySession", back_populates="paciente")
    relatorios = relationship("AutomaticReport", back_populates="paciente")

class PhysicalAssessment(Base):
    """Modelo de avaliação física"""
    __tablename__ = "physical_assessments"

    id = Column(Integer, primary_key=True, index=True)
    peso = Column(Float, nullable=False)
    altura = Column(Float, nullable=False)
    data_avaliacao = Column(DateTime, nullable=False)

    # Dobras cutâneas (JSON para flexibilidade)
    dobras_cutaneas = Column(JSON)

    # Circunferências (JSON para flexibilidade)
    circunferencias = Column(JSON)

    # Cálculos automáticos
    imc = Column(Float)
    gordura_percentual = Column(Float)
    massa_gorda = Column(Float)
    massa_magra = Column(Float)
    tmb = Column(Float)  # Taxa Metabólica Basal
    get = Column(Float)  # Gasto Energético Total

    # Método utilizado para cálculos
    metodo_utilizado = Column(String(50))

    # Observações
    observacoes = Column(Text)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Chaves estrangeiras
    paciente_id = Column(Integer, ForeignKey("patients.id"))
    avaliador_id = Column(Integer, ForeignKey("users.id"))

    # Relacionamentos
    paciente = relationship("Patient", back_populates="avaliacoes_fisicas")
    avaliador = relationship("User", back_populates="avaliacoes_criadas")

class Workout(Base):
    """Modelo de treino"""
    __tablename__ = "workouts"

    id = Column(Integer, primary_key=True, index=True)
    nome = Column(String(100), nullable=False)  # A, B, C, D, etc.
    descricao = Column(Text)
    data_inicio = Column(DateTime, nullable=False)
    data_fim = Column(DateTime)
    ativo = Column(Boolean, default=True)
    observacoes = Column(Text)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Chaves estrangeiras
    paciente_id = Column(Integer, ForeignKey("patients.id"))
    personal_id = Column(Integer, ForeignKey("users.id"))

    # Relacionamentos
    paciente = relationship("Patient", back_populates="treinos")
    personal = relationship("User", back_populates="treinos_criados")
    exercicios = relationship("WorkoutExercise", back_populates="treino")

class WorkoutExercise(Base):
    """Modelo de exercício do treino"""
    __tablename__ = "workout_exercises"

    id = Column(Integer, primary_key=True, index=True)
    nome_exercicio = Column(String(100), nullable=False)
    grupo_muscular = Column(String(50))
    video_url = Column(String(255))
    imagem_url = Column(String(255))

    # Progressão por semana
    semana = Column(Integer, default=1)
    series = Column(Integer, nullable=False)
    repeticoes = Column(String(20))  # Ex: "8-12", "15", "até falha"
    carga = Column(Float)
    descanso = Column(String(20))  # Ex: "60s", "2min"

    # Observações específicas do exercício
    observacoes = Column(Text)
    ordem = Column(Integer, default=1)  # Ordem no treino

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Chave estrangeira
    treino_id = Column(Integer, ForeignKey("workouts.id"))

    # Relacionamento
    treino = relationship("Workout", back_populates="exercicios")

class NutritionalPlan(Base):
    """Modelo de plano nutricional"""
    __tablename__ = "nutritional_plans"

    id = Column(Integer, primary_key=True, index=True)
    data_criacao = Column(DateTime, nullable=False)

    # Cálculos energéticos
    tmb_calculado = Column(Float)  # Taxa Metabólica Basal
    get_calculado = Column(Float)  # Gasto Energético Total
    superavit_deficit_percentual = Column(Float)  # % de superávit ou déficit
    calorias_alvo = Column(Float)  # Calorias finais do plano

    # Distribuição de macronutrientes
    proteinas_g = Column(Float)
    carboidratos_g = Column(Float)
    gorduras_g = Column(Float)

    # Plano alimentar (JSON flexível)
    plano_alimentar = Column(JSON)  # Refeições, alimentos, quantidades

    # URLs de documentos gerados
    pdf_url = Column(String(255))

    # Observações
    observacoes = Column(Text)
    ativo = Column(Boolean, default=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Chaves estrangeiras
    paciente_id = Column(Integer, ForeignKey("patients.id"))
    nutricionista_id = Column(Integer, ForeignKey("users.id"))

    # Relacionamentos
    paciente = relationship("Patient", back_populates="planos_nutricionais")
    nutricionista = relationship("User", back_populates="planos_nutricionais")

class PsychologySession(Base):
    """Modelo de sessão psicológica"""
    __tablename__ = "psychology_sessions"

    id = Column(Integer, primary_key=True, index=True)
    data_sessao = Column(DateTime, nullable=False)
    duracao_minutos = Column(Integer)

    # Conteúdo da sessão
    anotacoes = Column(Text)
    objetivos_sessao = Column(Text)
    tecnicas_utilizadas = Column(Text)
    observacoes_comportamentais = Column(Text)

    # Arquivos de áudio
    audio_url = Column(String(255))
    transcricao_texto = Column(Text)
    transcricao_processada = Column(Boolean, default=False)

    # Avaliações e escalas
    humor_escala = Column(Integer)  # 1-10
    ansiedade_escala = Column(Integer)  # 1-10
    motivacao_escala = Column(Integer)  # 1-10

    # Próximos passos
    proximos_passos = Column(Text)
    data_proxima_sessao = Column(DateTime)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Chaves estrangeiras
    paciente_id = Column(Integer, ForeignKey("patients.id"))
    psicologo_id = Column(Integer, ForeignKey("users.id"))

    # Relacionamentos
    paciente = relationship("Patient", back_populates="sessoes_psicologicas")
    psicologo = relationship("User", back_populates="sessoes_psicologicas")

class AutomaticReport(Base):
    """Modelo de relatório automático gerado por LLM"""
    __tablename__ = "automatic_reports"

    id = Column(Integer, primary_key=True, index=True)
    data_geracao = Column(DateTime, nullable=False)

    # Tipo de relatório
    tipo = Column(String(50), nullable=False)  # fisico, mental, nutricional, integrado

    # Período analisado
    data_inicio_analise = Column(DateTime)
    data_fim_analise = Column(DateTime)

    # Conteúdo do relatório
    titulo = Column(String(200))
    resumo_executivo = Column(Text)
    analise_detalhada = Column(Text)
    recomendacoes = Column(Text)
    conclusoes = Column(Text)

    # Dados utilizados na análise (JSON)
    dados_fonte = Column(JSON)

    # URLs de arquivos gerados
    pdf_url = Column(String(255))
    html_content = Column(Text)

    # Metadados
    gerado_por_ia = Column(Boolean, default=True)
    modelo_ia_utilizado = Column(String(50))
    versao_prompt = Column(String(20))

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Chave estrangeira
    paciente_id = Column(Integer, ForeignKey("patients.id"))

    # Relacionamento
    paciente = relationship("Patient", back_populates="relatorios")
