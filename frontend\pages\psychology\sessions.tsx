import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { 
  Brain, 
  Plus, 
  Search, 
  Filter,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Clock,
  Mic,
  FileText,
  TrendingUp
} from 'lucide-react'

interface Patient {
  id: number
  nome: string
}

interface PsychologySession {
  id: number
  data_sessao: string
  duracao_minutos?: number
  humor_escala?: number
  ansiedade_escala?: number
  motivacao_escala?: number
  objetivos_sessao?: string
  anotacoes?: string
  audio_url?: string
  transcricao_processada: boolean
  data_proxima_sessao?: string
  paciente_id: number
  paciente?: Patient
}

export default function PsychologySessionsPage() {
  const router = useRouter()
  const [sessions, setSessions] = useState<PsychologySession[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterPeriod, setFilterPeriod] = useState('all')

  useEffect(() => {
    fetchSessions()
  }, [])

  const fetchSessions = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        router.push('/login')
        return
      }

      // Buscar sessões do psicólogo atual
      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/users/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (!userResponse.ok) {
        throw new Error('Erro ao buscar dados do usuário')
      }

      const userData = await userResponse.json()

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/v1/psychology/psychologist/${userData.id}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      )

      if (!response.ok) {
        throw new Error('Erro ao buscar sessões')
      }

      const data = await response.json()
      
      // Buscar dados dos pacientes para cada sessão
      const sessionsWithPatients = await Promise.all(
        data.map(async (session: PsychologySession) => {
          try {
            const patientResponse = await fetch(
              `${process.env.NEXT_PUBLIC_API_URL}/api/v1/patients/${session.paciente_id}`,
              {
                headers: {
                  'Authorization': `Bearer ${token}`,
                },
              }
            )
            
            if (patientResponse.ok) {
              const patientData = await patientResponse.json()
              return { ...session, paciente: patientData }
            }
            return session
          } catch {
            return session
          }
        })
      )

      setSessions(sessionsWithPatients)
    } catch (error) {
      console.error('Erro ao buscar sessões:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredSessions = sessions.filter(session => {
    const matchesSearch = session.paciente?.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.objetivos_sessao?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.anotacoes?.toLowerCase().includes(searchTerm.toLowerCase())
    
    let matchesPeriod = true
    if (filterPeriod !== 'all') {
      const sessionDate = new Date(session.data_sessao)
      const now = new Date()
      const daysDiff = Math.floor((now.getTime() - sessionDate.getTime()) / (1000 * 60 * 60 * 24))
      
      switch (filterPeriod) {
        case 'week':
          matchesPeriod = daysDiff <= 7
          break
        case 'month':
          matchesPeriod = daysDiff <= 30
          break
        case 'quarter':
          matchesPeriod = daysDiff <= 90
          break
      }
    }
    
    return matchesSearch && matchesPeriod
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getScaleColor = (value?: number) => {
    if (!value) return 'text-gray-400'
    if (value <= 3) return 'text-green-600'
    if (value <= 6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScaleLabel = (value?: number) => {
    if (!value) return 'N/A'
    if (value <= 3) return 'Baixo'
    if (value <= 6) return 'Moderado'
    return 'Alto'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Sessões Psicológicas - Hypatium</title>
        <meta name="description" content="Gerencie sessões psicológicas" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <Link href="/dashboard" className="text-blue-600 hover:text-blue-800">
                  ← Voltar ao Dashboard
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Brain className="h-6 w-6 mr-2" />
                Sessões Psicológicas
              </h1>
              <Link
                href="/psychology/sessions/new"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                Nova Sessão
              </Link>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Filters */}
            <div className="bg-white p-4 rounded-lg shadow mb-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="Buscar sessões..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center">
                    <Filter className="h-4 w-4 mr-2 text-gray-500" />
                    <select
                      value={filterPeriod}
                      onChange={(e) => setFilterPeriod(e.target.value)}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="all">Todas as sessões</option>
                      <option value="week">Última semana</option>
                      <option value="month">Último mês</option>
                      <option value="quarter">Últimos 3 meses</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Brain className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total de Sessões</p>
                    <p className="text-2xl font-bold text-gray-900">{sessions.length}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <User className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Pacientes Ativos</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {new Set(sessions.map(s => s.paciente_id)).size}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Mic className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Com Áudio</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {sessions.filter(s => s.audio_url).length}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Calendar className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Este Mês</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {sessions.filter(s => {
                        const sessionDate = new Date(s.data_sessao)
                        const now = new Date()
                        return sessionDate.getMonth() === now.getMonth() && 
                               sessionDate.getFullYear() === now.getFullYear()
                      }).length}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Sessions List */}
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {filteredSessions.length === 0 ? (
                  <li className="px-6 py-12 text-center">
                    <Brain className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma sessão encontrada</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {searchTerm ? 'Tente ajustar sua busca.' : 'Comece criando uma nova sessão.'}
                    </p>
                    {!searchTerm && (
                      <div className="mt-6">
                        <Link
                          href="/psychology/sessions/new"
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Nova Sessão
                        </Link>
                      </div>
                    )}
                  </li>
                ) : (
                  filteredSessions.map((session) => (
                    <li key={session.id}>
                      <div className="px-6 py-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="flex-shrink-0">
                              <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                                <Brain className="h-5 w-5 text-purple-600" />
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="flex items-center">
                                <p className="text-sm font-medium text-gray-900">
                                  {session.paciente?.nome || `Paciente ${session.paciente_id}`}
                                </p>
                                {session.audio_url && (
                                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    <Mic className="h-3 w-3 mr-1" />
                                    Áudio
                                  </span>
                                )}
                                {session.transcricao_processada && (
                                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <FileText className="h-3 w-3 mr-1" />
                                    Transcrito
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center text-sm text-gray-500 space-x-4">
                                <div className="flex items-center">
                                  <Calendar className="h-4 w-4 mr-1" />
                                  {formatDate(session.data_sessao)} às {formatTime(session.data_sessao)}
                                </div>
                                {session.duracao_minutos && (
                                  <div className="flex items-center">
                                    <Clock className="h-4 w-4 mr-1" />
                                    {session.duracao_minutos}min
                                  </div>
                                )}
                              </div>
                              
                              {/* Escalas */}
                              <div className="flex items-center space-x-4 mt-2">
                                {session.humor_escala && (
                                  <div className="flex items-center">
                                    <span className="text-xs text-gray-500 mr-1">Humor:</span>
                                    <span className={`text-xs font-medium ${getScaleColor(session.humor_escala)}`}>
                                      {session.humor_escala}/10 ({getScaleLabel(session.humor_escala)})
                                    </span>
                                  </div>
                                )}
                                {session.ansiedade_escala && (
                                  <div className="flex items-center">
                                    <span className="text-xs text-gray-500 mr-1">Ansiedade:</span>
                                    <span className={`text-xs font-medium ${getScaleColor(session.ansiedade_escala)}`}>
                                      {session.ansiedade_escala}/10 ({getScaleLabel(session.ansiedade_escala)})
                                    </span>
                                  </div>
                                )}
                                {session.motivacao_escala && (
                                  <div className="flex items-center">
                                    <span className="text-xs text-gray-500 mr-1">Motivação:</span>
                                    <span className={`text-xs font-medium ${getScaleColor(session.motivacao_escala)}`}>
                                      {session.motivacao_escala}/10 ({getScaleLabel(session.motivacao_escala)})
                                    </span>
                                  </div>
                                )}
                              </div>

                              {session.objetivos_sessao && (
                                <p className="text-sm text-gray-600 mt-1 truncate max-w-md">
                                  <strong>Objetivos:</strong> {session.objetivos_sessao}
                                </p>
                              )}
                              
                              {session.data_proxima_sessao && (
                                <p className="text-sm text-blue-600 mt-1">
                                  <strong>Próxima sessão:</strong> {formatDate(session.data_proxima_sessao)}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Link
                              href={`/psychology/sessions/${session.id}`}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <Eye className="h-4 w-4" />
                            </Link>
                            <Link
                              href={`/psychology/sessions/${session.id}/edit`}
                              className="text-gray-600 hover:text-gray-900"
                            >
                              <Edit className="h-4 w-4" />
                            </Link>
                            <Link
                              href={`/psychology/patients/${session.paciente_id}/progress`}
                              className="text-green-600 hover:text-green-900"
                            >
                              <TrendingUp className="h-4 w-4" />
                            </Link>
                            <button className="text-red-600 hover:text-red-900">
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
