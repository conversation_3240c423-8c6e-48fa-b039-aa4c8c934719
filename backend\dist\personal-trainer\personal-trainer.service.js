"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonalTrainerService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let PersonalTrainerService = class PersonalTrainerService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findOne(id) {
        const personal = await this.prisma.personalTrainer.findUnique({
            where: { id },
            include: {
                alunos: {
                    select: {
                        id: true,
                        nome: true,
                        email: true,
                        telefone: true,
                        pesoAtual: true,
                        estatura: true,
                        idade: true,
                        dataCadastro: true,
                    },
                },
                _count: {
                    select: {
                        alunos: true,
                        avaliacoes: true,
                    },
                },
            },
        });
        if (!personal) {
            throw new common_1.NotFoundException('Personal Trainer não encontrado');
        }
        const { senha, ...result } = personal;
        return result;
    }
    async update(id, updatePersonalTrainerDto) {
        const personal = await this.prisma.personalTrainer.update({
            where: { id },
            data: updatePersonalTrainerDto,
        });
        const { senha, ...result } = personal;
        return result;
    }
    async getDashboardStats(personalId) {
        const [totalAlunos, totalAvaliacoes, avaliacoesRecentes] = await Promise.all([
            this.prisma.aluno.count({
                where: { personalId },
            }),
            this.prisma.avaliacaoComposicao.count({
                where: { personalId },
            }),
            this.prisma.avaliacaoComposicao.findMany({
                where: { personalId },
                include: {
                    aluno: {
                        select: {
                            nome: true,
                        },
                    },
                },
                orderBy: {
                    dataAvaliacao: 'desc',
                },
                take: 5,
            }),
        ]);
        return {
            totalAlunos,
            totalAvaliacoes,
            avaliacoesRecentes,
        };
    }
};
exports.PersonalTrainerService = PersonalTrainerService;
exports.PersonalTrainerService = PersonalTrainerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PersonalTrainerService);
//# sourceMappingURL=personal-trainer.service.js.map