"""
Serviço de notificações push e email
"""

import os
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.models.models import User, Patient, PsychologySession, Workout, NutritionalPlan

class NotificationService:
    def __init__(self, db: Session):
        self.db = db
        self.email_enabled = bool(os.getenv("SMTP_HOST"))
        self.push_enabled = bool(os.getenv("FIREBASE_CREDENTIALS"))
        
    def send_appointment_reminder(self, session_id: int, hours_before: int = 24) -> Dict[str, Any]:
        """
        Enviar lembrete de consulta
        """
        try:
            session = self.db.query(PsychologySession).filter(
                PsychologySession.id == session_id
            ).first()
            
            if not session or not session.data_proxima_sessao:
                return {'success': False, 'error': 'Sessão não encontrada ou sem próxima data'}
            
            # Verificar se é hora de enviar o lembrete
            reminder_time = session.data_proxima_sessao - timedelta(hours=hours_before)
            if datetime.utcnow() < reminder_time:
                return {'success': False, 'error': 'Ainda não é hora do lembrete'}
            
            # Buscar dados do paciente
            patient = self.db.query(Patient).filter(Patient.id == session.paciente_id).first()
            if not patient:
                return {'success': False, 'error': 'Paciente não encontrado'}
            
            # Buscar dados do profissional
            professional = self.db.query(User).filter(User.id == session.psicologo_id).first()
            if not professional:
                return {'success': False, 'error': 'Profissional não encontrado'}
            
            # Preparar dados da notificação
            notification_data = {
                'type': 'appointment_reminder',
                'title': 'Lembrete de Consulta',
                'message': f'Você tem uma consulta agendada com {professional.nome} em {session.data_proxima_sessao.strftime("%d/%m/%Y às %H:%M")}',
                'patient_id': patient.id,
                'professional_id': professional.id,
                'session_id': session.id,
                'appointment_date': session.data_proxima_sessao.isoformat()
            }
            
            # Enviar notificação
            result = self._send_notification(patient, notification_data)
            
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def send_workout_reminder(self, patient_id: int, workout_id: int) -> Dict[str, Any]:
        """
        Enviar lembrete de treino
        """
        try:
            patient = self.db.query(Patient).filter(Patient.id == patient_id).first()
            if not patient:
                return {'success': False, 'error': 'Paciente não encontrado'}
            
            workout = self.db.query(Workout).filter(Workout.id == workout_id).first()
            if not workout:
                return {'success': False, 'error': 'Treino não encontrado'}
            
            professional = self.db.query(User).filter(User.id == workout.personal_id).first()
            
            notification_data = {
                'type': 'workout_reminder',
                'title': 'Hora do Treino!',
                'message': f'Não esqueça do seu treino: {workout.nome}',
                'patient_id': patient.id,
                'professional_id': workout.personal_id,
                'workout_id': workout.id,
                'workout_name': workout.nome
            }
            
            result = self._send_notification(patient, notification_data)
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def send_nutrition_reminder(self, patient_id: int, plan_id: int) -> Dict[str, Any]:
        """
        Enviar lembrete nutricional
        """
        try:
            patient = self.db.query(Patient).filter(Patient.id == patient_id).first()
            if not patient:
                return {'success': False, 'error': 'Paciente não encontrado'}
            
            plan = self.db.query(NutritionalPlan).filter(NutritionalPlan.id == plan_id).first()
            if not plan:
                return {'success': False, 'error': 'Plano nutricional não encontrado'}
            
            notification_data = {
                'type': 'nutrition_reminder',
                'title': 'Lembrete Nutricional',
                'message': f'Lembre-se de seguir seu plano nutricional: {plan.calorias_alvo} kcal/dia',
                'patient_id': patient.id,
                'professional_id': plan.nutricionista_id,
                'plan_id': plan.id,
                'calories': plan.calorias_alvo
            }
            
            result = self._send_notification(patient, notification_data)
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def send_progress_update(self, patient_id: int, professional_id: int, progress_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enviar atualização de progresso
        """
        try:
            patient = self.db.query(Patient).filter(Patient.id == patient_id).first()
            if not patient:
                return {'success': False, 'error': 'Paciente não encontrado'}
            
            professional = self.db.query(User).filter(User.id == professional_id).first()
            if not professional:
                return {'success': False, 'error': 'Profissional não encontrado'}
            
            # Determinar tipo de progresso
            progress_type = progress_data.get('type', 'general')
            
            messages = {
                'weight_loss': f'Parabéns! Você perdeu {abs(progress_data.get("weight_change", 0)):.1f}kg!',
                'weight_gain': f'Ótimo progresso! Você ganhou {progress_data.get("weight_change", 0):.1f}kg de massa!',
                'body_fat_reduction': f'Excelente! Sua gordura corporal reduziu {abs(progress_data.get("body_fat_change", 0)):.1f}%!',
                'mood_improvement': f'Que bom! Seu humor melhorou significativamente!',
                'general': 'Seu progresso foi atualizado. Continue assim!'
            }
            
            notification_data = {
                'type': 'progress_update',
                'title': 'Atualização de Progresso',
                'message': messages.get(progress_type, messages['general']),
                'patient_id': patient.id,
                'professional_id': professional.id,
                'progress_data': progress_data
            }
            
            result = self._send_notification(patient, notification_data)
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def send_report_ready(self, patient_id: int, professional_id: int, report_id: int) -> Dict[str, Any]:
        """
        Notificar que relatório está pronto
        """
        try:
            patient = self.db.query(Patient).filter(Patient.id == patient_id).first()
            if not patient:
                return {'success': False, 'error': 'Paciente não encontrado'}
            
            professional = self.db.query(User).filter(User.id == professional_id).first()
            if not professional:
                return {'success': False, 'error': 'Profissional não encontrado'}
            
            notification_data = {
                'type': 'report_ready',
                'title': 'Relatório Disponível',
                'message': f'Seu relatório foi gerado por {professional.nome} e está disponível para visualização.',
                'patient_id': patient.id,
                'professional_id': professional.id,
                'report_id': report_id
            }
            
            result = self._send_notification(patient, notification_data)
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _send_notification(self, recipient: Patient, notification_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enviar notificação (email e/ou push)
        """
        results = {
            'email': {'sent': False, 'error': None},
            'push': {'sent': False, 'error': None}
        }
        
        # Enviar email
        if self.email_enabled and recipient.email:
            email_result = self._send_email_notification(recipient, notification_data)
            results['email'] = email_result
        
        # Enviar push notification
        if self.push_enabled:
            push_result = self._send_push_notification(recipient, notification_data)
            results['push'] = push_result
        
        # Salvar notificação no banco (para histórico)
        self._save_notification_history(recipient.id, notification_data, results)
        
        success = results['email']['sent'] or results['push']['sent']
        
        return {
            'success': success,
            'results': results,
            'notification_data': notification_data
        }
    
    def _send_email_notification(self, recipient: Patient, notification_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enviar notificação por email
        """
        try:
            # TODO: Implementar envio real de email com SMTP
            # Por enquanto, simular envio
            
            email_content = f"""
Olá {recipient.nome},

{notification_data['message']}

Atenciosamente,
Equipe Hypatium

---
Esta é uma mensagem automática. Não responda este email.
"""
            
            # Simular envio bem-sucedido
            return {
                'sent': True,
                'email': recipient.email,
                'subject': notification_data['title'],
                'content': email_content
            }
            
        except Exception as e:
            return {
                'sent': False,
                'error': str(e)
            }
    
    def _send_push_notification(self, recipient: Patient, notification_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enviar push notification
        """
        try:
            # TODO: Implementar envio real com Firebase Cloud Messaging
            # Por enquanto, simular envio
            
            push_payload = {
                'title': notification_data['title'],
                'body': notification_data['message'],
                'data': {
                    'type': notification_data['type'],
                    'patient_id': str(notification_data['patient_id']),
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            # Simular envio bem-sucedido
            return {
                'sent': True,
                'recipient_id': recipient.id,
                'payload': push_payload
            }
            
        except Exception as e:
            return {
                'sent': False,
                'error': str(e)
            }
    
    def _save_notification_history(self, patient_id: int, notification_data: Dict[str, Any], results: Dict[str, Any]):
        """
        Salvar histórico de notificação
        """
        try:
            # TODO: Criar tabela de histórico de notificações
            # Por enquanto, apenas log
            
            history_entry = {
                'patient_id': patient_id,
                'type': notification_data['type'],
                'title': notification_data['title'],
                'message': notification_data['message'],
                'email_sent': results['email']['sent'],
                'push_sent': results['push']['sent'],
                'created_at': datetime.utcnow().isoformat()
            }
            
            # Log para arquivo ou banco
            print(f"Notification sent: {json.dumps(history_entry, indent=2)}")
            
        except Exception as e:
            print(f"Error saving notification history: {str(e)}")
    
    def schedule_appointment_reminders(self) -> Dict[str, Any]:
        """
        Agendar lembretes de consultas (para ser executado periodicamente)
        """
        try:
            # Buscar sessões com próxima data nas próximas 24 horas
            tomorrow = datetime.utcnow() + timedelta(hours=24)
            upcoming_sessions = self.db.query(PsychologySession).filter(
                PsychologySession.data_proxima_sessao.between(
                    datetime.utcnow(),
                    tomorrow
                )
            ).all()
            
            sent_count = 0
            errors = []
            
            for session in upcoming_sessions:
                result = self.send_appointment_reminder(session.id)
                if result['success']:
                    sent_count += 1
                else:
                    errors.append(f"Session {session.id}: {result['error']}")
            
            return {
                'success': True,
                'sent_count': sent_count,
                'total_sessions': len(upcoming_sessions),
                'errors': errors
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_notification_preferences(self, patient_id: int) -> Dict[str, Any]:
        """
        Obter preferências de notificação do paciente
        """
        # TODO: Implementar sistema de preferências
        # Por enquanto, retornar padrões
        
        return {
            'email_enabled': True,
            'push_enabled': True,
            'appointment_reminders': True,
            'workout_reminders': True,
            'nutrition_reminders': True,
            'progress_updates': True,
            'report_notifications': True,
            'reminder_hours_before': 24
        }
    
    def update_notification_preferences(self, patient_id: int, preferences: Dict[str, Any]) -> Dict[str, Any]:
        """
        Atualizar preferências de notificação
        """
        try:
            # TODO: Implementar salvamento de preferências no banco
            # Por enquanto, simular atualização
            
            return {
                'success': True,
                'patient_id': patient_id,
                'preferences': preferences,
                'updated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_notification_status(self) -> Dict[str, Any]:
        """
        Obter status do sistema de notificações
        """
        return {
            'email_service': {
                'enabled': self.email_enabled,
                'smtp_configured': bool(os.getenv("SMTP_HOST"))
            },
            'push_service': {
                'enabled': self.push_enabled,
                'firebase_configured': bool(os.getenv("FIREBASE_CREDENTIALS"))
            },
            'services_available': {
                'appointment_reminders': True,
                'workout_reminders': True,
                'nutrition_reminders': True,
                'progress_updates': True,
                'report_notifications': True
            }
        }
